<template>
	<!-- <view class="box flex flex-column align-items">
		<view v-for="(item, index) in mysignList" :key="index" style="margin: 0 30rpx;">
			<view class="flex align-items" style="justify-content: space-between;height: 70rpx;line-height: 70rpx;">
				<view class="t1_name">
					{{item.user.realname ? item.user.realname : item.user.nickname.slice(0,5)}}
					<image v-if="item.status == 2" class="imgs" src="/static/detail/hxslice.png"> </image>
					<image v-if="item.status == 9" class="imgs" src="/static/detail/hxall.png"> </image>
				</view>
				<view class="t1_order">订单编号：{{item.order_no}}</view>
				<view class="t1_num">数量：{{item.num}}</view>
			</view>
		</view>
	</view> -->
	<view class="page">
		<view style="padding: 30rpx;">
			<view
				style="padding: 0px 30rpx;text-align: center;display: flex;align-items: center;justify-content: space-around;background-color: #323232;height: 181rpx;border-radius: 18rpx;">
				<view style="position: relative;">
					<view style="font-size: 36rpx;color: #BBFC5B;font-weight: 600;">{{activity_info.total_num}}</view>
					<view style="font-size: 28rpx;font-weight: 300;color: #FFFFFF;margin-top: 10rpx;">活动总人数</view>
				</view>
				<view style="width: 1rpx;height: 60rpx;background-color: #ffffff5e;"></view>
				<view style="position: relative;">
					<view style="font-size: 36rpx;color: #BBFC5B;font-weight: 600;">{{activity_info.sign_num}}</view>
					<view style="font-size: 28rpx;font-weight: 300;color: #FFFFFF;margin-top: 10rpx;">报名人数</view>
				</view>
				<view style="width: 1rpx;height: 60rpx;background-color: #ffffff5e;"></view>
				<view style="position: relative;">
					<view style="font-size: 36rpx;color: #BBFC5B;font-weight: 600;">{{activity_info.verification_num}}
					</view>
					<view style="font-size: 28rpx;font-weight: 300;color: #FFFFFF;margin-top: 10rpx;">核销人数</view>
				</view>
			</view>
			<view v-for="item in mysignList"
				style="background: linear-gradient( 180deg, rgba(232,255,197,0.89) 0%, #FFFFFF 31%);border-radius: 18rpx;margin-top: 30rpx;padding-bottom: 20rpx;">
				<view style="font-size: 32rpx;color: #3D3D3D;padding: 30rpx;font-weight: 600;">订单编号：{{item.order_no}}
				</view>
				<view style="width: 100%;height: 1rpx;background-color: #f8f8f8;margin: 10rpx 0;"></view>
				<view v-for="(v,i) in item.ordercode">
					<view style="display: flex;align-items: center;justify-content: space-between;padding:20rpx 30rpx;">
						<view>
							<view style="font-size: 28rpx;color: #323232;">{{v.name}}</view>
							<view style="color: #9C9C9C;font-size: 24rpx;margin-top: 20rpx;">身份证 {{v.idnum}}</view>
						</view>
						<view>
							<text v-if="v.status==3" style="font-size: 28rpx;color: #999999;">未核销</text>
							<text v-if="v.status==6" style="font-size: 28rpx;color: #FF4810;">已核销</text>
							
							<text v-if="v.status==-3" style="font-size: 28rpx;color: #FF4810;">已取消</text>
							<text v-if="v.server_status==3" style="font-size: 28rpx;color: #FF4810;">售后中</text>
							<text v-if="v.auth_status==1" style="font-size: 28rpx;color: #FF4810;">已退款</text>
						</view>
					</view>
					<view v-if="i<item.ordercode.length-1"
						style="width: 93%;height: 1rpx;background-color: #f8f8f8;margin: 0rpx auto;margin-top:10rpx;"></view>
				</view>
			</view>
			<view v-if="mysignList.length==0" style="text-align: center;margin: 0 auto;padding-top: 300rpx;">
				<image src="/static/detail/no_info.png" style="width: 180rpx;height: 180rpx"></image>
				<view style="font-size: 28rpx;font-weight: 300;">暂无报名信息</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mysignList: [],
				activity_info: {}
			};
		},
		onLoad(option) {
			this.id = option.id;
		},
		onShow() {
			this.getPeople();
		},
		methods: {
			// 获取报名详情
			getPeople() {
				const that = this
				uni.$u.http.get('/api/school.newworker.activity.order/order_list', {
					params: {
						activity_id: that.id,
						page: 1,
						limit: 1000,
						status:'0,2,3,4,7,9'
					},
				}).then(res => {
					if (res.code == 1) {
						this.activity_info = res.data.activity_info;
						this.mysignList = res.data.list;
					} else {

						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});

				});
			},
			// 跳转订单
			toDetail(status) {
				uni.navigateTo({
					url: "/packageA/my/exercise?status=" + status
				})
			},
			// 返回首页
			goHome() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		font-family: PingFang SC, PingFang SC;
		min-height: 100vh;
		background: #F7F7F7;
	}
</style>