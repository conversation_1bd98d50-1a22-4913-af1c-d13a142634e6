<template>
	<view class="box flex align-items flex-column">
		<view class="header flex align-items">
			<image :src="detail.detail.headimage" mode=""
				style="width: 280rpx;height: 200rpx;border-radius: 12rpx;margin-left: 30rpx;"></image>
			<view class="right flex flex-column">
				<span class="title white-space">{{detail.detail.title}}</span>
				<span class="buyTime">购买时间:{{detail.paytime_text}}</span>
				<span class="buyTime" style="margin-top: 8rpx;">{{'  订单ID:'+detail.order_no}}</span>
			</view>
		</view>
		<view class="content">
			<u--textarea v-model="value" height="150" :disabled="status == 3" count placeholder="请输入内容" maxlength="300"
				border="bottom"></u--textarea>
			<view class="roteList flex flex-column align-items ">
				<view class=" flex flex-column flex-start w-100">
					<span style="margin-top: 32rpx;" class="title">课程评分</span>
					<span style="margin-top: 24rpx;justify-content: space-between;" class="flex align-items w-100 rote">
						<u-rate :count="count" size='27' :readonly="status == 3" v-model="rote1" activeColor="#FFBC2C" inactiveColor="#C0C0C0"
							allowHalf="true"></u-rate>
						<span>{{rote1}}分</span>

					</span>
				</view>
				<view class=" flex flex-column flex-start w-100">
					<span style="margin-top: 32rpx;" class="title">讲师评分</span>
					<span style="margin-top: 24rpx;justify-content: space-between;" class="flex align-items w-100 rote">
						<u-rate :count="count" size='27' v-model="rote2" :readonly="status == 3" activeColor="#FFBC2C" inactiveColor="#C0C0C0"
							allowHalf="true"></u-rate>
						<span>{{rote2}}分</span>

					</span>
				</view>
				<view class=" flex flex-column flex-start w-100" style="margin-bottom: 42rpx;">
					<span style="margin-top: 32rpx;" class="title">教室环境</span>
					<span style="margin-top: 24rpx;justify-content: space-between;" class="flex align-items w-100 rote">
						<u-rate :count="count" size='27' v-model="rote3" :readonly="status == 3" activeColor="#FFBC2C" inactiveColor="#C0C0C0"
							allowHalf="true"></u-rate>
						<span>{{rote3}}分</span>

					</span>
				</view>
			</view>
		</view>
		<view class="footer flex align-items justify-center" v-if="status != 3">
			<span class="flex align-items justify-center" v-if="status == 1" @click.stop="submit()">发布评价</span>
			<span class="flex align-items justify-center" v-if="status == 2" @click.stop="edit()">修改评价</span>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				count: 5,
				rote1: 0,
				rote2: 0,
				rote3: 0,
				value: "",
				status:1,
				detail:{},
				id:'',
				info:{}
			};
		},
		onLoad(options) {
			this.getDetail(options.id)
			this.id = options.id
			this.status = options.status
			
		},
		methods:{
			getDetail(id){
				uni.$u.http.get('/api/school/order/detail', {
					params: {
						id: id
					}
				}).then(res => {
					this.detail = res.data.detail;
					this.info = res.data.detail.evaluate
					if (this.status != 1) {
						this.rote1 = res.data.detail.evaluate.classes_star
						this.rote2 = res.data.detail.evaluate.teacher_star
						this.rote3 = res.data.detail.evaluate.shop_star
						this.value = res.data.detail.evaluate.message_text
					}
					
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			
			
			// 发布
			submit(){
				uni.$u.http.post('/api/school.evaluate/evaluate', {
					order_no:this.id,
					message_text:this.value,
					classes_star:this.rote1,
					teacher_star:this.rote2,
					shop_star:this.rote3
				}).then(res => {
					if (res.code == 1){
						uni.showToast({
							title: '发布成功',
							icon: 'none',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.navigateBack()
								}, 2000);
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
						});
					}
					
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 编辑
			edit(){
				uni.$u.http.post('/api/school.evaluate/evaluate_update', {
					order_no:this.id,
					message_text:this.value,
					classes_star:this.rote1,
					teacher_star:this.rote2,
					shop_star:this.rote3
				}).then(res => {
					if (res.code == 1){
						uni.showToast({
							title: '修改成功',
							icon: 'none',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.navigateBack()
								}, 2000);
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
						});
					}
					
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: #F1F2F8;
		width: 750rpx;
		height: 100vh;

		.header {
			width: 100%;
			height: 274rpx;
			border-bottom-right-radius: 12rpx;
			border-bottom-left-radius: 12rpx;
			background: #FFFFFF;


			.right {
				margin-left: 32rpx;
				width: 50%;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
					margin-bottom: 24rpx;
				}

				.buyTime {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
				}
			}

		}

	}

	.w-100 {
		width: 100%;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.content {
		width: 690rpx;
		margin-top: 28rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;

		.roteList {
			padding: 0 24rpx;

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 34rpx;
				color: #343434;
			}

			.rote {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 42rpx;
				color: #FFBC2C;
			}
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		height: 122rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -6rpx 12rpx 0rpx rgba(111, 190, 255, 0.1);
		border-radius: 0rpx 0rpx 0rpx 0rpx;

		span {
			width: 642rpx;
			height: 80rpx;
			background: #008CFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #FFFFFF;
		}
	}
	::v-deep .u-textarea{
		border-radius: 20rpx;
	}
</style>