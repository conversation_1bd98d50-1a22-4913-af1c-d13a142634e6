<template>
	<view class="box flex flex-column align-items">
		<view class="center flex flex-column align-items">
			<image class="img_1" src="/static/successOrder.png"></image>
			<view class="text_1">恭喜您报名成功</view>
			<view class="text_2">请按时参加活动并出示二维码核销</view>
			<view class="btn_1" @click="toDetail(status)">查看支付订单</view>
			<view class="btn_2" @click="goHome">查看更多活动</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				option:{},
			};
		},
		onLoad(option) {
			this.option = option;
		},
		methods:{
			// 跳转订单
			toDetail(status){
				uni.redirectTo({
					url: "/packageA/my/pendinPayDetail?id="+this.option.id+"&order_no="+this.option.order_no
				})
			},
			// 返回首页
			goHome() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.w-100 {
		width: 100%;
	}
	.w-400{
		width: 400rpx;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}
	.hui{
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}
	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
	}
	.box{
		width: 750rpx;
		height: 100vh;
		background-color: #FFFFFF;
		.center{
			width: 690rpx;
			margin-top: 30rpx;
			.img_1{
				width: 100px;
				height: 100px;
				margin-top:190rpx ;
			}
			.text_1{
				width: 252rpx;
				height: 36rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 36rpx;
				color: #323232;
				line-height: 36rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
				margin-top: 50rpx;
			}
			.text_2{
				width: 360rpx;
				height: 36rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #9C9C9C;
				line-height: 36rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
				margin-top: 30rpx;
			}
			.btn_1{
				width: 280rpx;
				height: 70rpx;
				background: #323232;
				border-radius: 198rpx 198rpx 198rpx 198rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 32rpx;
				color: #BBFC5B;
				line-height: 70rpx;
				text-align: center;
				margin-top: 70rpx;
			}
			.btn_2{
				width: 280rpx;
				height: 70rpx;
				border-radius: 198rpx 198rpx 198rpx 198rpx;
				border: 1px solid #323232;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 32rpx;
				color: #9C9C9C;
				line-height: 70rpx;
				text-align: center;
				margin-top: 30rpx;
			}
			
		}
	}
</style>
