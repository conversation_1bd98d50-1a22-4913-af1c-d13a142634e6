<template>
	<!-- <view>查看发票</view> -->
	<view class="container">
		<view class="info-top">
			<image style="height: 122px;width: 294px;position: absolute;right: 5%;top:0%" src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/invInfoBk.png"></image>
			<view class="info-status">
				<view class="statusFir">
					<image style="width: 32rpx;height: 32rpx;" src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/statusInv.png"></image>
					<view style="margin-left: 20rpx;">{{detailList[0].invoice_status_text}}</view>
				</view>
				<view class="statusSec" style="margin-top: 20rpx;">已开发票金额: ￥{{detailAny.price}}</view>
			</view>
		</view>
		<view class="infoCon">
			<view class="conFIr">
				<view class="conFir-left">
					<view class="leftTitle">发票类型：<span style="color: #323232;" v-if="detailAny.invoice_type == 'ordinary'">普通发票 </span>
					<span style="color: #323232;" v-else>专用发票</span></view>
					<view class="leftTitle">发票内容：<span style="color: #323232;">商品明细</span></view>
					<view class="leftTitle">抬头类型：<span style="color: #323232;" v-if="detailAny.head_type == 'personal'">个人或事业单位</span>
					<span style="color: #323232;" v-else>企业</span></view>
				</view>
				<view class="conFir-right" @click="checkImages()">
					<image style="width: 266rpx;height: 166rpx;border-radius: 14rpx;" :src="detailAny.images[0]" mode=""></image>
					<view class="ringhtImg-text">共{{imagesNum}}张</view>
				</view>
			</view>
			<view class="conSec">
				<view class="rightTitle">抬头名称：<span style="color: #323232;">{{detailAny.invoice_header}}</span></view>
				<view class="rightTitle">开票金额：<span style="color: #323232;">{{detailAny.price}}</span></view>
				<view class="rightTitle">申请时间：<span style="color: #323232;">{{detailAny.createtime_text}}</span></view>
				<view class="rightTitle">开票时间：<span style="color: #323232;">{{detailAny.invoicingtime_text}}</span></view>
			</view>
		</view>
		
		
		
		
		
		
		<u-popup :show="imagesShow" :round="22" mode="center" @close="imagesShow = false" @open="imagesShow = true"
			:custom-style="popupStyImg">
			<view class="popup_tkall" style="">
				<image src="/static/center/close.png" style="width: 64rpx;height: 64rpx;position: absolute;top: 10rpx;right: 20rpx;" @click="imagesShow = false"></image>
				<view class="popup_tk">
					<view style="width: 20rpx;height: 20rpx;">
						<u-icon v-if="imagesIndex != 0" name="arrow-left" @click="changeLeft()"></u-icon>
					</view>
					
					<image style="width: 280px;height: 240px;margin: 0rpx 26rpx;" :src="imagesList[imagesIndex]" mode=""></image>
					<view style="width: 20rpx;height: 20rpx;">
						<u-icon v-if="imagesIndex != iamgesListIndexNum" name="arrow-right" @click="changeRight()"></u-icon>
					</view>
					
				</view>
				
			</view>
		</u-popup>
		
		
	</view>
	
	
	
</template>

<script>
	export default {
		data() {
			return {
				popupStyImg: {
					width: '650rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '600rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column',
					backgroundColor: '#f8f8f8'
				},
				order_nos: '', //选中的多个订单号
				id: 0,
				detailList: '', //发票订单列表
				detailAny: '', //第一个detail
				qrimages: '',
				imagesShow:false,
				imagesNum:0,
				imagesList: [],
				imagesIndex:0,
				iamgesListIndexNum: 0,
			};
		},
		onLoad(options) {
			console.log('options',options.id);
			this.id = options.id
			console.log('options',this.id);
			if(this.id){
				this.getOrderInfo()
			}
			
		},
		// onShow() {
		// 	this.getInvoiceList()
		// },
		onPullDownRefresh() {
	
		},
		onReachBottom() {
	
		},
		methods: {
			getOrderInfo() {
				console.log('id', this.id);
				uni.$u.http.get('/api/school.header/applydetail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detailList = res.data.detail.activityorders;//发票订单列表
						this.detailAny = res.data.detail;
						this.imagesNum = this.detailAny.images.length
						this.imagesList = this.detailAny.images
						this.iamgesListIndexNum = this.imagesNum -1
						console.log('qrimages:', this.qrimages);
						console.log('imagesNum:', this.imagesNum);
						console.log('detail:', this.detail);
						console.log('iamgesListIndexNum:', this.iamgesListIndexNum);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			checkImages() {
				this.imagesIndex = 0
				this.imagesShow = true;
			},
			changeLeft() {
				console.log('left');
				if(this.imagesIndex > 0) {
					this.imagesIndex = this.imagesIndex-1
				}
			},
			changeRight() {
				console.log('right');
				if(this.imagesIndex < this.iamgesListIndexNum) {
					this.imagesIndex = this.imagesIndex + 1
				}
			},
			
		}
	
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f5f5f5;
	}
	
	.container {
		width: 100%;
		height: 100vh;
		background-color: #f5f5f5;
		
		.info-top {
			width: 100%;
			height: 300rpx;
			background-color: #323232;
			// background-image: ;
			
			.info-status {
				display: grid;
				width: 100%;
				justify-content: center;
				align-items: center;
				color: #bbfc5b;
				//margin-top: 60rpx;
				position: relative;
				top: 100rpx;
				
				.statusFir {
					display: flex;
					justify-content: center;
					align-items: center;
					// margin-top: 60rpx;
				}
				
				.statusSec {
					margin-top: 20rpx;
					display: grid;
					justify-content: center;
					align-items: center;
				}
			}
		}
		
		.infoCon {
			display: block;
			// width: 690rpx;
			width: 95%;
			height: 440rpx;
			background-color: #ffffff;
			padding: 30rpx 20rpx;
			border-radius: 30rpx;
			position: relative;
			bottom: 40rpx;
			
			.conFIr{
				width: 96%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.conFir-left {
					display: grid;
					
					.leftTitle {
						font-size: 28rpx;
						font-weight: 400;
						color: #9c9c9c;
						line-height: 36rpx;
						margin-bottom: 30rpx;
					}
				}
				
				.conFir-right {
					// border-top: 2rpx solid #323232;
					position: relative;
					top: 14rpx;
					
					.ringhtImg-text {
						background-color: #4d4d4d;
						opacity: 0.65;
						font-size: 22rpx;
						font-weight: 400;
						color: #ffffff;
						text-align: center;
						display: grid;
						justify-content: center;
						align-items: center;
						height: 42rpx;
						border-radius: 0rpx 0rpx 14rpx 14rpx;
						position: relative;
						bottom: 50rpx;
					}
				}
				
			}
			
			.conSec {
				width: 90%;
				display: grid;
				justify-content: flex-start;
				align-items: center;
				position: relative;
				bottom: 18rpx;
				
				.rightTitle {
					font-size: 28rpx;
					font-weight: 400;
					color: #9c9c9c;
					line-height: 36rpx;
					margin-bottom: 30rpx;
				}
			}
			
		}
		
		
		
		
		.popup_tkall {
			display: grid;
			justify-content: center;
			align-items: center;
			// background-color: #ffffff;
		
			.popup_tk {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top: 56rpx;
				// font-size: 32rpx;
				// font-weight: 500;
				// margin: 12rpx 0 24rpx 0;
				// text-align: center;
				// color: #3D3D3D;
			}
		
			
		}
		
		
	}
</style>