<script>
	export default {
		onLaunch: function() {

			// #ifndef MP-WEIXIN
			uni.hideTabBar()
			// #endif

			// #ifdef MP-TOUTIAO
			// uni.hideTabBar()
			// #endif

			this.updateManager();

		},
		onShow: function() {
			console.log('App Show')

			// #ifndef MP-WEIXIN
			wx.loadFontFace({
				global: true,
				family: 'YouSheBiaoTiHei',
				source: 'url("https://naweigetetest2.hschool.com.cn/dyqc/YouSheBiaoTiHei.ttf")',
				success: function(res) {
					console.log('字体加载成功');
					console.log(res);
				}
			})
			// #endif

		},
		onHide: function() {
			console.log('App Hide')
		},

		methods: {
			//自动更新
			updateManager() {
				if (uni.canIUse('getUpdateManager')) {
					const updateManager = uni.getUpdateManager()
					updateManager.onCheckForUpdate(res => {
						if (res.hasUpdate) {
							updateManager.onUpdateReady(function() {
								uni.showModal({
									title: '更新提示',
									content: '新版本已经准备好，需要重启应用',
									showCancel: false,
									success: ({
										confirm,
										cancel
									}) => {
										updateManager.applyUpdate()
									}
								})
							})
							updateManager.onUpdateFailed(function() {
								// 新的版本下载失败
								uni.showModal({
									title: '已经有新版本了哟~',
									content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
								})
							})
						}
					})
				} else {
					// 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
					uni.showModal({
						title: '提示',
						content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
					})
				}
			},
		}
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	/*每个页面公共css */
	.uni-input-input {
		font-size: 35rpx;
	}
	// #ifdef MP-WEIXIN
	@font-face {
		font-family: 'YouSheBiaoTiHei';
		src: url('https://naweigetetest2.hschool.com.cn/dyqc/YouSheBiaoTiHei.ttf') format('truetype');
	}
	// #endif
</style>