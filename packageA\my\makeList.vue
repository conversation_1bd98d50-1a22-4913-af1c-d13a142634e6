<template>
	<view class="box flex justify-center align-items flex-column">
		<view class="con-center flex flex-column justify-center align-items">
			<view class="search flex  align-items" style="width: 99%;">
				<span class="dashed flex align-items justify-center">
					<image src="../../static/center/search.png" mode=""></image>
				</span>
				<span class="line-search"></span>
				<input type="text" placeholder="搜索您的预约课程"  v-model="keywords"  class="input" placeholder-class="plasty" />
				<span class="searchBtn" @click.stop="search()">搜索</span>
			</view>
			<view class="header flex align-items w-100" style="justify-content: space-between;">
				<span class="s-header flex justify-center align-items" v-for="(item,index) in headers" :key="index"
					:class="{ selected: headerSelected(item.status) }"
					@click="selectheader(item.status)">{{item.text}}</span>
			</view>
			<span class="line"></span>
			<!-- <view class="sort flex" style="width: 100%;padding: 20rpx 0;">
					<JQ-orderBy @switchSort="switchSort" :size="size"></JQ-orderBy>
			</view> -->
			<view class="centerBox flex justify-start align-items flex-column">
				<view class="flex w-100 center flex-column" style="align-items: center;" v-for="(item,index) in list"
					:key="index" @click.stop="openUrl('/packageA/center/applyDetail?id='+item.id)">
					<view class="flex" style="width: 690rpx;">
						<image :src="item.detail.headimage" mode="" style="width: 280rpx; height: 200rpx;border-radius: 12rpx;"></image>
						<view class="flex flex-column rightBox">
							<span class="name">{{item.detail.title}}</span>
							<!-- <span class="line-row"></span> -->
							<span class="minge">{{'预约时间 ' + item.reservation_time_text}}</span>
							<span class="minge">{{'上课时间 ' + item.start_time_text}}</span>
							<span class="pass" v-if="item.status == 0">已通过</span>
							<span class="pass" v-if="item.status == 3">已完成</span>
							<span class="full" v-if="item.status == -3">已取消预约</span>
							<span class="full" v-if="item.status == -3">{{item.reason}}</span>
							<span class="pass" style="color: #008CFF;" v-if="item.status == -1">审核中</span>
							<span class="Cancel" v-if="item.status == -1" @click.stop="Cancel(item.id)">取消预约</span>
							<span class="Cancel" v-if="item.status == 3 && item.detail.feel == 0 "@click.stop="service(item.classes_order_id)">售后</span>
							<span class="Cancel" v-if="item.status == 0"@click.stop="toQR(item.classes_order_id)">核销</span>
						</view>
					</view>
					<!-- <span class="error" v-if="item.auth_status == 2">审核失败:{{item.reason}}</span> -->
					<span class="box-line"></span>
				</view>
			</view>
		</view>
		<u-popup :show="show" mode="center" :zIndex="99999" :custom-style="popupStyle" @close="close" @open="open">
			<view class="popup flex flex-column align-items">
				<view class="header">
					<span>取消预约</span>
				</view>
				<span class="line-row"></span>
				<view class="pop-center flex">
					<view class="left">
						<image :src="qrcode.detail.headimage" mode="" style="width: 280rpx;height: 200rpx;"></image>
					</view>
					<view class="right flex flex-column">
						<span class="title white-space">{{qrcode.detail.title}}</span>
						<span class="hui" style="margin: 24rpx 0 6rpx 0;">开始日期:{{qrcode.start_time_text}}</span>
						<span class="hui" style="margin: 24rpx 0 6rpx 0;">结束日期:{{qrcode.end_time_text}}</span>
						<span class="hui white-space" style="width: 340rpx;">地址:{{qrcode.detail.address_detail}}</span>
					</view>
				</view>
				<span class="line-short"></span>
				<view class="popList flex" style="justify-content: space-between;">
					<span class="hui">订单号</span>
					<span class="hei">{{qrcode.order_no}}</span>
				</view>
				<view class="popList flex" style="justify-content: space-between;">
					<span class="hui">开始时间</span>
					<span class="hei">{{qrcode.start_time_text}}</span>
				</view>
				<view class="popList flex" style="justify-content: space-between;">
					<span class="hui">结束时间</span>
					<span class="hei">{{qrcode.end_time_text}}</span>
				</view>
				<view class="popList flex" style="justify-content: space-between;">
					<span class="hui">预约时间</span>
					<span class="hei">{{qrcode.reservation_time_text}}</span>
				</view>
				<view class="pop-btn flex align-items">
					<span class="Cancel flex justify-center align-items" @click="show = false">取消</span>
					<span class="Confirm flex justify-center align-items" @click="Confirm(qrcode.order_no)">确认</span>
				</view>
			</view>
		</u-popup>
		<!-- 核销 -->
		<u-popup :show="QRShow" mode="center" :zIndex="99999" :custom-style="popupStyle" closeable="true" @close="QRclose"
			@open="QRopen">
			<view class="popup flex flex-column align-items">
				<view class="pop-center flex">
					<view class="left">
						<image :src="QR.detail.headimage" mode="" style="width: 280rpx;height: 200rpx;"></image>
					</view>
					<view class="right flex flex-column">
						<span class="title white-space">{{QR.detail.title}}</span>
						<span class="hui"
							style="margin: 16rpx 0 6rpx 0;">课时:{{QR.detail.verification_num}}/{{QR.detail.limit_num}}</span>
						<!-- <span class="hui">日期:{{QR.detail.classes_date_text}}</span> -->
						<!-- <span class="hui" style="margin: 6rpx 0 6rpx 0;">时间:{{QR.detail.classes_time_text}}</span> -->
						<span class="hui" style="margin-top: 6rpx;">开始时间:{{QR.detail && QR.detail.start_time | formatDateTime}}</span>
						<span class="hui" style="margin: 6rpx 0 6rpx 0;">结束时间:{{QR.detail && QR.detail.end_time | formatDateTime}}</span>
						<span class="hui">地址:{{QR.detail.address_detail}}</span>
					</view>
				</view>
				<span class="line-short"></span>
				<image :src="QR.codeimage" mode="" style="width: 376rpx;height: 376rpx;"></image>
				<span style="margin: 24rpx 0 64rpx 0;">核销二维码</span>
			</view>
		</u-popup>
		<u-loadmore :status="loadStatus" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupStyle: {
					width: '690rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					padding: '0',
					borderRadius: '20rpx'
				},
				sortList: [{
					text: '按照价格排序',
					value: ['acs', 'desc'],
				}, {
					text: '按照编号排序',
					value: ['acs', 'desc'],
				}],
				QRShow:false,
				size: 13,
				sortStyle: ['#ff557f', '#3f3f3f'],
				headers: [{
						status: '-1,0,3,-3',
						text: '全部'
					},
					{
						status: '-1',
						text: '待审核'
					},
					{
						status: '0',
						text: '已预约'
					},
					{
						status: '3',
						text: '已完成'
					},
					{
						status: '-3',
						text: '已取消'
					},
				],
				popupStyle: {
					width: '690rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					padding: '0',
					borderRadius: '20rpx'
				},
				selected: '-1,0,3,-3',
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				keywords: '',
				list: [],
				show: false,
				qrcode: {},
				order_no: '',
				QR:{},
				count:0
			};
		},
		filters: {
		    formatDateTime(timestamp) {
		     if (!timestamp) return '';
		           const date = new Date(timestamp * 1000);
		           const year = date.getFullYear();
		           const month = String(date.getMonth() + 1).padStart(2, '0');
		           const day = String(date.getDate()).padStart(2, '0');
		           const hours = String(date.getHours()).padStart(2, '0');
		           const minutes = String(date.getMinutes()).padStart(2, '0');
		           const seconds = String(date.getSeconds()).padStart(2, '0');
		           return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		         }
		  },
		
		onLoad(option) {
			if (option.status) {
				this.selected = option.status
				this.getList(option.status);
			} else {
				this.getList(this.selected);
			}
		},
		// onShow() {
		// 	this.getList(-1);
		// },
		onReachBottom() {
			if (this.list.length < this.count) {
				this.page++;
				this.getList(this.selected);
			}
		},
		methods: {
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 关闭弹窗
			close() {
				this.qrcode = {}
				this.show = false
			},
			open() {
				this.show = true
			},
			// 关闭弹窗
			QRclose() {
				this.QR = {}
				this.QRShow = false
			},
			QRopen() {
				this.QRShow = true
			},
			switchSort(index, value) {
				console.log(index, value);
			},
			Confirm(order_no) {
				uni.$u.http.post('/api/school/hour_order/cancel', {
					order_no: order_no
				}).then(res => {
					if (res.code == 1) {
						this.page = 1
						this.list = []
						this.getList(this.selected)
						this.show = false
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					console.error('请求失败', error);
				});
			},
			// 核销码
			toQR(id) {
				uni.$u.http.get('/api/school/order/detail', {
					params: {
						id: id
					},
				}).then(res => {
					if (res.code == 1) {
						this.QR = res.data.detail
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
				this.QRShow = true
			},
			//取消预约
			Cancel(id) {
				uni.$u.http.get('/api/school/hour_order/detail', {
					params: {
						id: id
					},
				}).then(res => {
					if (res.code == 1) {
						this.qrcode = res.data.detail
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
				this.show = true
			},
			// 搜索
			search(){
				const that = this
				that.page = 1
				that.list = []
				this.selected = '-1,0,3,-3'
				this.getList('-1,0,3,-3')
			},
			// 获取课程列表
			getList(status) {
				// if (this.loadStatus === 'nomore') return;
				uni.$u.http.get('/api/school/hour_order/order_list', {
					params: {
						keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status: status,
					}
				}).then(res => {
					if (res.code == 1) {
						this.count = res.data.count
						this.list = [...this.list, ...res.data.list];
						if (this.list.length >= res.data.count) {
							this.loadStatus = 'nomore';
						} else {
							this.loadStatus = 'loading';
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.loadStatus = 'loading';
					}
				}).catch(error => {
					console.error('请求失败', error);
					this.loadStatus = 'loading';
				});
			},
			selectheader(status) {
				const that = this
				that.selected = status;
				console.log(status, '')
				that.page = 1
				that.list = []
				that.getList(status)
			},
			// 售后
			service(id) {
				uni.navigateTo({
					url: "/packageA/afterSales/index?id=" + id
				})
			},
			headerSelected(status) {
				return this.selected === status;
			},
			// 跳转详情
			// toDetail(id, orderId) {
			// 	uni.navigateTo({
			// 		url: "/packageA/center/detail?id=" + id + "&orderId=" + orderId
			// 	})
			// }
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: linear-gradient(to bottom, #F1F2F8 0%, #FFFFFF 5%, #FFFFFF 100%);

		.con-center {
			width: 690rpx;
			// margin-top: 25rpx;

			.centerBack {
				position: fixed;
				width: 100%;
				height: 100%;
				top: 25rpx;
				left: 0;
				z-index: -1;
			}

			.header {
				height: 50rpx;
				margin-top: 37rpx;

				.s-header {
					width: 104rpx;
					height: 50rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #9E9E9E;
					line-height: 26rpx;
				}

				.s-header.selected {
					width: 104rpx;
					height: 50rpx;
					background: #008CFF;
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 28rpx;
					color: #FFFFFF;
					line-height: 26rpx;
				}
			}
		}

	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		margin-top: 12rpx;
		width: 690rpx;
		height: 1rpx;
		background: #008CFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.centerBox {
		width: 690rpx;

		.error {
			width: 100%;
			word-break: break-all;
		}

		.box-line {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			margin-top: 24rpx;
		}

		.center {
			margin: 32rpx 0 32rpx 0;
		}

		.rightBox {
			margin-left: 24rpx;
			width: 378rpx;

			.line-row {
				width: 382rpx;
				height: 1rpx;
				background: #D9D9D9;
				box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
				margin: 14rpx 0 6rpx 0;
			}

			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 36rpx;
				color: #343434;
				margin-bottom: 24rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				/* 设置行数 */
				overflow: hidden;
				text-overflow: ellipsis;
				word-break: break-all;
				/* 防止单词被截断 */
			}

			.minge {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #7A7A7A;
				margin-bottom: 14rpx;
			}

			.pass {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #34DC12;
			}

			.full {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #FF2323;
			}


			.money {
				font-weight: 800;
				font-size: 24rpx;
				color: #FF2323;

				span {
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
				}
			}

			.Cancel {
				width: 138rpx;
				height: 48rpx;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				background: #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
			}

			.make {
				width: 138rpx;
				height: 48rpx;
				background: #008CFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-left: 16rpx;
			}

			.QR {
				width: 138rpx;
				height: 48rpx;
				background: #4974FF;
				box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #EAEAEA;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-top: 50rpx;
			}

		}
	}

	.charge {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 28rpx;
		color: #FF2323;
		line-height: 32rpx;
	}

	.search {
		width: 690rpx;
		height: 64rpx;
		background: #FFFFFF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008CFF;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008CFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

		}
		.searchBtn{
			width: 128rpx;
			height: 64rpx;
			background: #008CFF;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	.popup {
		.header {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 42rpx;
			color: #008CFF;
			margin-top: 34rpx;
			width: 690rpx;
		}

		.line-row {
			width: 690rpx;
			height: 1rpx;
			background: #D9D9D9;
			margin: 11rpx 0 31rpx 0;
		}

		.pop-center {
			margin-top: 50rpx;
			.left {}

			.right {
				margin-left: 30rpx;

				.title {
					width: 340rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
				}


			}
		}

		.line-short {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
		}

		.popList {
			justify-content: space-between;
			width: 600rpx;
			margin-top: 32rpx;

			.hei {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #343434;
			}
		}

		.pop-btn {
			width: 690rpx;
			margin-top: 62rpx;
			justify-content: space-around;

			.Cancel {
				width: 306rpx;
				height: 80rpx;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				border: 2rpx solid #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #008CFF;
			}

			.Confirm {
				width: 306rpx;
				height: 80rpx;
				background: #008CFF;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #FFFFFF;
			}
		}
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}
</style>