<template>
	<view class="box">
		<rich-text :nodes="Negotiate"></rich-text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				Negotiate:""
				
			};
		},
		onLoad() {
			this.getDetail()
		},
		methods:{
			getDetail() {
				uni.$u.http.get('/api/index/agreement', {
					params: {
			
					}
				}).then(res => {
					if (res.code == 1) {
						this.Negotiate = (res.data.privacy).replace(/\<img/gi,
							'<img style="max-width:100%;height:auto" ');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
.box{
	 padding: 24rpx;
 }
</style>
