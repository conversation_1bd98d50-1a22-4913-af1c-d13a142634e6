<template>
	<view class="backImg flex flex-column align-items">
		<view class="box flex flex-column align-items">
			<!--  -1未申请 0待审核 1审核通过 2审核失败 -->
			<span class="reason" style="color: red;" v-if="status == 2">
				审核失败:{{reason}}
			</span>
			<span class="reason flex align-items justify-center" style="color: #00D90E;" v-if="status == 0">
				审核中
			</span>
			<span class="title">基本信息</span>
			<view class="first flex flex-column align-items justify-start" v-if="status == -1 || status == 2">
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items">机构/个人 <span style="color: #FF2323;">*</span></span>
					<view class="row-right flex align-items" @click="cutShow()">
						<span>{{form.cut}}</span>
						<image src="../../static/my/right.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
					</view>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items" v-if="form.cut == '机构'">机构名称 <span
							style="color: #FF2323;">*</span></span>
					<span class="label flex align-items" v-if="form.cut == '个人'">教师名称 <span
							style="color: #FF2323;">*</span></span>
					<span class="row-right">
						<input type="text" placeholder="请填写名称" class="input" v-model="form.name"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items flex-column">联系电话
						<span style="color: #FF2323; font-size: 14rpx;">*请注意接听来电</span>
					</span>
					<span class="row-right">
						<input class="input" type="text" placeholder="请填写电话" v-model="form.tel"
							placeholder-class="plasty" />
					</span>
				</view>

				<span class="line-row" v-if="form.cut == '机构'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="form.cut == '机构'">
					<span class="label flex align-items ">成立时间
					</span>
					<span class="row-right" @click="dateShowHidden()">
						<input type="text" placeholder="请填写成立时间" class="input" disabled="true" v-model="form.date"
							placeholder-class="plasty" />
					</span>
				</view>

				<span class="line-row" v-if="form.cut == '机构'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="form.cut == '机构'">
					<span class="label flex align-items ">员工人数
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写人数" class="input" v-model="form.people"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items" v-if="form.cut == '机构'">机构地址 <span
							style="color: #FF2323;">*</span></span>
					<span class="label flex align-items" v-if="form.cut == '个人'">授课地址 <span
							style="color: #FF2323;">*</span></span>
					<span class="row-right" @click='institutionShow = true' style="width: 80%;">
						<input type="text" placeholder="请选择地址" disabled="true" class="input" v-model="form.institution"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx; ">
					<span class="label flex align-items">地址
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right" @click="choose()" style="width:80%;">
						<input class="input" type="text" disabled="true" placeholder="请填写地址" v-model="form.address"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin: 25rpx 0;">
					<span class="label flex align-items ">详细位置
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写详细位置" class="input" v-model="form.address_detail"
							placeholder-class="plasty" />
					</span>
				</view>
			</view>
			<!-- 禁止查看  -->
			<view class="first flex flex-column align-items justify-start" v-else>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items">机构/个人 <span style="color: #FF2323;">*</span></span>
					<view class="row-right flex align-items">
						<span>{{State == 2 ? '机构' : '个人'}}</span>
					</view>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items" v-if="State == '2'">机构名称 <span
							style="color: #FF2323;">*</span></span>
					<span class="label flex align-items" v-if="State == '1'">教师名称 <span
							style="color: #FF2323;">*</span></span>
					<span class="row-right">
						<input type="text" placeholder="请填写名称" disabled="true" class="input" v-model="apply_info.name"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items flex-column">联系电话
						<span style="color: #FF2323; font-size: 14rpx;">*请注意接听来电</span>
					</span>
					<span class="row-right">
						<input class="input" type="number" disabled="true" placeholder="请填写电话" v-model="apply_info.tel"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="State == '2'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx; " v-if="State == '2'">
					<span class="label flex align-items flex-column">成立时间
					</span>
					<span class="row-right">
						<input class="input" type="text" disabled="true"
							:placeholder="apply_info.auth_time | formatTimestamp" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="State == '2'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx; " v-if="State == '2'">
					<span class="label flex align-items flex-column">员工人数
					</span>
					<span class="row-right">
						<input class="input" type="text" disabled="true" placeholder="请填写地址" v-model="apply_info.people"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items flex-column" v-if="State == '2'">机构地址</span>
					<span class="label flex align-items flex-column" v-if="State == '1'">授课地址</span>
					<span class="row-right" style="width: 80%;">
						<input class="input" type="text" disabled="true" placeholder="请填写地址"
							v-model="apply_info.address_city" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx; ">
					<span class="label flex align-items flex-column">地址
					</span>
					<span class="row-right">
						<input class="input" type="text" disabled="true" placeholder="请填写地址"
							v-model="apply_info.address" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin: 25rpx 0;">
					<span class="label flex align-items flex-column">详细位置
					</span>
					<span class="row-right" style="width: 80%;">
						<input class="input" type="text" disabled="true" placeholder="请填写地址"
							v-model="apply_info.address_detail" placeholder-class="plasty" />
					</span>
				</view>

			</view>
			<span class="title" v-if="form.cut == '机构'">法人/委托人信息</span>
			<span class="title" v-if="form.cut == '个人'">个人信息</span>
			<view class="first flex flex-column align-items justify-start" v-if="status == -1 || status == 2">
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="form.cut == '机构'">
					<span class="label flex align-items ">机构法人/委托人
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写姓名" class="input" v-model="form.legalEntity"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="form.cut == '机构'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="form.cut == '机构'">
					<span class="label flex align-items ">社会统一信用代码
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写社会统一信用代码" class="input" v-model="form.yyzzdm"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="form.cut == '机构'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">性别
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right" @click="sexShow = true">
						<input type="text" placeholder="请填写性别" class="input" disabled="true" v-model="form.sex"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">民族
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写民族" class="input" v-model="form.nation"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">政治面貌
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right" @click="outLookShow = true">
						<input type="text" placeholder="请填写政治面貌" class="input" disabled="true" v-model="form.outlook"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">出生日期
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right" @click="birthShowHidden()">
						<input type="text" placeholder="请填写出生日期" class="input" disabled="true" v-model="form.birth"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">籍贯
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写籍贯" class="input" v-model="form.nativePlace"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">身份证号码
						<span style="color: #FF2323;">*</span>
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写身份证号码" class="input" v-model="form.cardNumber"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">学历
					</span>
					<span class="row-right" @click="diplomaShow = true">
						<input type="text" placeholder="请填写学历" class="input" disabled="true" v-model="form.diploma"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">职务
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写职务" class="input" v-model="form.post"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin: 25rpx 0;">
					<span class="label flex align-items ">社会职务
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写社会职务" class="input" v-model="form.socialPosition"
							placeholder-class="plasty" />
					</span>
				</view>
			</view>
			<!-- // 禁止查看 -->
			<view class="first flex flex-column align-items justify-start" v-else>
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="State == '2'">
					<span class="label flex align-items">机构法人/委托人 <span style="color: #FF2323;">*</span></span>
					<span class="row-right">
						<input type="text" placeholder="请填写名称" disabled="true" class="input"
							v-model="apply_info.legal_entity" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="State == '2'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;" v-if="State == '2'">
					<span class="label flex align-items">社会统一信用代码 <span style="color: #FF2323;">*</span></span>
					<span class="row-right">
						<input type="text" placeholder="请填写社会统一信用代码" disabled="true" class="input"
							v-model="apply_info.yyzzdm" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row" v-if="State == '2'"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items flex-column">性别
					</span>
					<span class="row-right">
						<input class="input" type="text" disabled="true"
							:placeholder="apply_info.gender === 0 ? '女' : '男'" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">民族
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写民族" class="input" disabled="true" v-model="apply_info.nation"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">政治面貌
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写政治面貌" class="input" disabled="true"
							v-model="apply_info.out_look" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">出生日期
					</span>
					<span class="row-right">
						<input type="text" :placeholder="apply_info.birthtime | formatTimestamp" class="input"
							disabled="true" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">籍贯
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写籍贯" class="input" disabled="true"
							v-model="apply_info.native_place" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">身份证号码
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写身份证号码" class="input" disabled="true"
							v-model="apply_info.card_number" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">学历
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写学历" class="input" disabled="true"
							v-model="apply_info.diploma" placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin-top: 25rpx;">
					<span class="label flex align-items ">职务
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写职务" class="input" disabled="true" v-model="apply_info.post"
							placeholder-class="plasty" />
					</span>
				</view>
				<span class="line-row"></span>
				<view class="row flex align-items" style="margin: 25rpx 0;">
					<span class="label flex align-items ">社会职务
					</span>
					<span class="row-right">
						<input type="text" placeholder="请填写社会职务" class="input" disabled="true"
							v-model="apply_info.social_position" placeholder-class="plasty" />
					</span>
				</view>
			</view>

			<view class="second" v-if="status == -1 || status == 2">
				<u--textarea v-model="form.desc" count maxlength="1000" placeholder="请填写申请备注"
					height="170"></u--textarea>
			</view>

			<view class="second" v-else>
				<u--textarea v-model="apply_info.desc" disabled="true" count maxlength="1000" placeholder="请填写申请备注"
					height="170"></u--textarea>
			</view>

			<view class="third flex flex-column" v-if="status == -1 || status == 2">
				<view class="flex flex-column ji" v-if="form.cut == '机构'" style="align-items: flex-start;">
					<!-- <span class="header">机构环境照片(三张)</span>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true" width="200rpx" height="200rpx">
						<image src="../../static/index/upload.png" mode="widthFix"
							style="width: 200rpx;height: 200rpx;">
					</u-upload> -->
					<span class="header">机构门头照片
						<span style="color: #FF2323;">*</span>
					</span>
					<u-upload :fileList="fileList1" @afterRead="afterRead" :previewFullImage="true" @delete="deletePic"
						@oversize="oversize" name="1" multiple :maxSize="2*1024*1024" :maxCount="1">
						<image src="../../static/index/mentou.png" mode="widthFix"
							style="width: 690rpx;height: 450rpx;">
					</u-upload>

					<span class="header">机构内部环境照片 <span style="color: #FF2323;">*</span></span>
					<u-upload :fileList="fileList2" @afterRead="afterRead" :previewFullImage="true" @delete="deletePic"
						@oversize="oversize" name="2" multiple :maxSize="2*1024*1024" :maxCount="1">
						<image src="../../static/index/neibu.png" mode="widthFix" style="width: 690rpx;height: 450rpx;">
					</u-upload>

					<span class="header">机构外部环境照片 <span style="color: #FF2323;">*</span></span>
					<u-upload :fileList="fileList3" @afterRead="afterRead" :previewFullImage="true" @delete="deletePic"
						@oversize="oversize" name="3" multiple :maxSize="2*1024*1024" :maxCount="1">
						<image src="../../static/index/waibu.png" mode="widthFix" style="width: 690rpx;height: 450rpx;">
					</u-upload>

					<span class="header">营业执照 <span style="color: #FF2323;">*</span></span>
					<u-upload :fileList="fileList4" @afterRead="afterRead" :previewFullImage="true" @delete="deletePic"
						@oversize="oversize" name="4" multiple :maxSize="2*1024*1024" :maxCount="1">
						<image src="../../static/index/yingye.png" mode="widthFix"
							style="width: 690rpx;height: 450rpx;">
					</u-upload>
				</view>

				<span class="header" v-if="form.cut == '机构'">法人身份证 <span style="color: #FF2323;">*</span></span>
				<span class="header" v-if="form.cut == '个人'">个人身份证 <span style="color: #FF2323;">*</span></span>
				<view class="shenfen flex align-items flex-column">
					<u-upload :fileList="fileList5" :previewFullImage="true" :maxSize="2*1024*1024"
						@afterRead="afterRead" @delete="deletePic" @oversize="oversize" name="5" multiple :maxCount="1">
						<image src="../../static/index/zheng.png" mode="widthFix" style="width: 690rpx;height: 450rpx;">
					</u-upload>
					<u-upload :fileList="fileList6" :previewFullImage="true" :maxSize="2*1024*1024"
						@afterRead="afterRead" @delete="deletePic" @oversize="oversize" name="6" multiple :maxCount="1">
						<image src="../../static/index/bei.png" mode="widthFix"
							style="width: 690rpx;height: 450rpx;margin: 24rpx 0 0 0;">
					</u-upload>
				</view>
			</view>

			<view class="third flex flex-column" v-else style="margin-bottom: 50rpx;">
				<view class="flex flex-column ji" v-if="State == '2'" style="align-items: flex-start;">
					<!-- <span class="header">机构环境照片(三张)</span>
					<span class="flex align-items w-100 item-image">
						<u--image :showLoading="true" :src="item" width="200rpx" height="200rpx"
							v-for="(item,index) in apply_info.images" :key="index" class="image-item"></u--image>
					</span> -->
					<span class="header">机构门头照片</span>
					<u--image :showLoading="true" :src="apply_info.images[0]" width="690rpx" height="450rpx"></u--image>

					<span class="header">机构内部环境照片</span>
					<u--image :showLoading="true" :src="apply_info.images[1]" width="690rpx" height="450rpx"></u--image>

					<span class="header">机构外部环境照片</span>
					<u--image :showLoading="true" :src="apply_info.images[2]" width="690rpx" height="450rpx"></u--image>

					<span class="header">营业执照</span>
					<u--image :showLoading="true" :src="apply_info.yyzz_images[0]" width="690rpx"
						height="450rpx"></u--image>
				</view>
				<span class="header" v-if="State == '2'">法人身份证</span>
				<span class="header" v-if="State == '1'">个人身份证</span>
				<view class="shenfen flex align-items flex-column">
					<u--image :showLoading="true" :src="apply_info.front_idcard_image" width="690rpx"
						height="450rpx"></u--image>
					<u--image :showLoading="true" :src="apply_info.reverse_idcard_image" width="690rpx"
						height="450rpx"></u--image>
				</view>
			</view>
		</view>
		<span style="margin: 30rpx 0 135rpx 0; width: 750rpx;" v-if="status == -1 || status == 2">
			<cc-protocolBox :agree="agree" :protocolArr="protocolArr" @click="agree = false"
				@protocolClick="protocolClick"></cc-protocolBox>
		</span>
		<view class="bottom flex align-items justify-center" v-if="status == -1 || status == 2">
			<span class="flex align-items justify-center" @click="apply()" v-if="agree == true">申请入驻</span>
			<span class="flex align-items justify-center" v-if="agree == false"
				style="background: #b2c9dcab;">申请入驻</span>
		</view>

		<!-- 入驻 -->
		<u-popup @touchmove.native.stop.prevent :closeable="true" :show="show" :round="10" mode="center" @close="close"
			@open="open" :custom-style="popupStyle">
			<span style="font-size: 42rpx;font-weight: 800;margin: 12rpx 0 24rpx 0;height: 120rpx;">《机构入驻协议》</span>
			<scroll-view ref="scrollView" :scroll-top="scrollTop" :show-scrollbar='true' @scrolltolower="handleScroll"
				scroll-y="true" style="height: 800rpx;margin-bottom: 24rpx;">
				<view class="popup flex align-items flex-column">
					<rich-text :nodes="Negotiate"></rich-text>
				</view>
			</scroll-view>
			<view class="popup-footer">
				<span class="zhixiao" v-if="agreeShow == false">我已知晓</span>
				<span class="zhixiao" style="background-color: #008CFF;" v-if="agreeShow == true"
					@click="change">我已知晓</span>
			</view>
		</u-popup>
		<!-- 机构/个人 -->
		<u-picker :show="showCut" :columns="columns" @cancel="cancel" @confirm="confirm"></u-picker>
		<!-- 机构地址 -->
		<u-picker :show="institutionShow"  :columns="institutionList" keyName="name" @cancel="institutioncancel"
			@confirm="institutionconfirm"></u-picker>
		<!-- 性别 -->
		<u-picker :show="sexShow" :columns="sexList" @cancel="sexCancel" @confirm="sexConfirm"></u-picker>
		<!-- 政治面貌 -->
		<u-picker :show="outLookShow" :columns="outlookList" @cancel="outlookCancel"
			@confirm="outlookConfirm"></u-picker>
		<!-- 学历 -->
		<u-picker :show="diplomaShow" :columns="diplomaList" @cancel="diplomaCancel"
			@confirm="diplomaConfirm"></u-picker>

		<!-- 成立时间 -->
		<u-datetime-picker @cancel="datecel" ref="dateRef" :minDate="minDate" @confirm="datefirm" :show="dateShow"
			v-model="date" mode="date"></u-datetime-picker>

		<!-- 出生日期 -->
		<u-datetime-picker @cancel="birthCancel" ref="birthRef" :minDate="minDate" @confirm="birthConfirm"
			:show="birthShow" v-model="date" mode="date"></u-datetime-picker>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				institutionShow: false, // 区域弹窗
				sexShow: false, // 性别弹窗
				outLookShow: false, //政治面貌弹窗
				birthShow: false, // 出生日期弹窗
				diplomaShow: false, //学历弹窗
				value: false,
				scrollTop: 0,
				minDate: new Date('1940-01-01').getTime(),
				old: {
					scrollTop: 0
				},
				status: -1,
				reason: '',
				showCut: false,
				dateShow: false,
				show: false,
				State: '',
				Negotiate: null, // 入驻协议
				agree: false,
				agreeShow: false,
				protocolArr: ["<入驻协议>"],
				form: {
					cut: '机构',
					yyzzdm: '',
					name: '',
					tel: '',
					desc: '',
					address: '',
					latitude: '',
					longitude: '',
					date: uni.$u.timeFormat(new Date(), 'yyyy-mm-dd'), //成立时间
					establish_time: Math.floor(Date.now() / 1000), // 成立时间 秒
					people: "", // 员工人数
					institution: '', //机构地址
					institutionCode: '', //机构地址
					address_detail: '', //详细位置
					legalEntity: '', //法人
					sex: '', //性别
					nation: '', //民族
					outlook: '', //政治面貌
					birth: uni.$u.timeFormat(new Date(), 'yyyy-mm-dd'), //出生日期
					birthtime: Math.floor(Date.now() / 1000), // 出生日期 秒
					nativePlace: '', //籍贯
					cardNumber: '', //身份证号码
					diploma: '', //学历
					post: '', //职务
					socialPosition: '' //社会职务

				},
				apply_info: {},
				popupStyle: {
					width: '642rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '984rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				scrollViewHeight: 820, // scroll-view 的高度
				scrollViewContentHeight: 0, // scroll-view 内容的高度

				columns: [
					['机构', '个人']
				],
				diplomaList: [
					['‌小学', '初中', '高中', '‌大专', '‌大学本科', '‌硕士研究生', '‌博士研究生']
				],
				sexList: [
					['男', '女', ]
				],
				outlookList: [
					['‌中共党员', '中共预备党员', '共青团员', '‌民革党员', '‌民盟盟员', '‌民建会员', '‌民进会员', '‌农工党党员', '‌致公党党员', '‌‌九三学社社员',
						'‌‌台盟盟员', '‌无党派人士‌', '‌群众',
					]
				],
				institutionList: [],

				images: '',
				upPicUrl: 'https://testy.hschool.com.cn//api/common/upload',
				yyzz_images: [],
				fileList1: [], // 用于存储第一个上传组件的文件列表
				fileList2: [], // 用于存储第二个上传组件的文件列表
				fileList3: [], // 用于存储第三个上传组件的文件列表
				fileList4: [], // 用于存储第四个上传组件的文件列表
				fileList5: [], // 用于存储第四个上传组件的文件列表
				fileList6: [], // 用于存储第四个上传组件的文件列表
				list1: '', // 存储第一个上传组件的图片URL，最多3张
				list2: '', // 存储第二个上传组件的图片URL，1张
				list3: '', // 存储第三个上传组件的图片URL，1张
				list4: '', // 存储第四个上传组件的图片URL，1张
				list5: '', // 存储第四个上传组件的图片URL，1张
				list6: '' // 存储第四个上传组件的图片URL，1张
			};
		},

		onLoad() {
			this.searchStatus()
			this.getAgreement()
			this.getAreaList()
		},
		// filters: {
		// 	filtersRichText(html) { //控制小程序中图片大小
		// 		let newContent = html.replace(/<img[^>]*>/gi, (match, capture) => {
		// 			match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
		// 			match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
		// 			match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
		// 			return match;
		// 		});
		// 		newContent = newContent.replace(/style="[^"]+"/gi, (match, capture) => {
		// 			match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi,
		// 				'max-width:100%;');
		// 			return match;
		// 		});
		// 		newContent = newContent.replace(/<br[^>]*\/>/gi, '');
		// 		newContent = newContent.replace(/\<img/gi,
		// 			'<img style="max-width:100%;height:auto;display:inline-block;margin:10rpx auto;"');
		// 		return newContent;
		// 	}
		// },
		filters: {
			formatTimestamp(value) {
				if (!value) return '';
				const date = new Date(value * 1000); // 将秒转换为毫秒
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
				const day = String(date.getDate()).padStart(2, '0'); // 补零
				return `${year}-${month}-${day}`;
			}
		},
		methods: {
			//同意
			handleScroll() {
				this.agreeShow = true
			},
			getTime() {
				const currentDate = new Date();
				const year = currentDate.getFullYear();
				const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
				const day = String(currentDate.getDate()).padStart(2, '0');

				const formattedDate = `${year}-${month}-${day}`;
				return formattedDate;
			},
			dateShowHidden() {
				this.dateShow = true
				this.$refs.dateRef.innerValue = new Date(this.getTime()).getTime()
			},
			birthShowHidden() {
				this.birthShow = true
				this.$refs.birthRef.innerValue = new Date(this.getTime()).getTime()
			},

			// 成立时间
			datefirm(e) {
				console.log(e)
				this.form.date = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.form.establish_time = Math.floor(new Date(e.value).getTime() / 1000);
				this.dateShow = false
			},
			datecel(e) {
				this.dateShow = false
			},
			// 出生日期
			birthConfirm(e) {
				this.form.birth = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.form.birthtime = Math.floor(new Date(e.value).getTime() / 1000);

				this.birthShow = false
			},
			birthCancel() {
				this.birthShow = false
			},
			close() {
				this.show = false
			},
			cutShow() {
				this.showCut = true
				console.log('this.showCut', this.showCut)
			},
			open() {
				this.show = true
			},
			change() {
				this.agree = true
				this.show = false
			},
			// 选择机构地址

			protocolClick(tag) {
				this.show = true
			},
			// 获取位置信息
			getAreaList() {
				uni.$u.http.get('/api/index/get_area', {
					params: {
						city: 410300
					}
				}).then(res => {
					if (res.code == 1) {
						this.institutionList[0] = res.data.map(area => ({
							name: area.label,
							value: area.value
						}));
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 获取入驻协议文章
			getAgreement() {
				uni.$u.http.get('/api/index/agreement', {
					params: {

					}
				}).then(res => {
					if (res.code == 1) {

						this.Negotiate = (res.data.entry_agreement).replace(/\<img/gi,
							'<img style="max-width:100%;height:auto" ');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			choose() {
				console.log(11111)
				const that = this
				uni.chooseLocation({
					success: function(res) {
						console.log(res)
						that.form.address = res.name
						// that.form.address_detail = res.address
						that.form.latitude = res.latitude
						that.form.longitude = res.longitude
					},
					fail: function(rot) {
						console.log(rot)
					}
				});
			},


			// 回调参数为包含columnIndex、value、values
			confirm(e) {
				console.log('confirm', e.value)
				this.form.cut = e.value[0]
				this.showCut = false
				this.fileList1 = [],
					this.fileList2 = [],
					this.fileList3 = [],
					this.fileList4 = [],
					this.fileList5 = [],
					this.fileList6 = [],
					this.list1 = '',
					this.list2 = '',
					this.list3 = '',
					this.list4 = '',
					this.list5 = '',
					this.list6 = ''
				this.name = ''
			},
			cancel(e) {
				this.showCut = false
			},
			// 性别
			sexCancel(e) {
				this.sexShow = false
			},
			sexConfirm(e) {
				this.form.sex = e.value[0]
				this.sexShow = false
			},
			// 政治面貌
			outlookCancel(e) {
				this.outLookShow = false
			},
			outlookConfirm(e) {
				console.log(e.value)
				this.form.outlook = e.value[0]
				this.outLookShow = false
			},
			// 学历
			diplomaCancel() {
				this.diplomaShow = false
			},
			diplomaConfirm(e) {
				this.form.diploma = e.value[0]
				this.diplomaShow = false
			},
			// 机构地址
			institutioncancel(e) {
				this.institutionShow = false
			},
			institutionconfirm(e) {
				this.form.institution = e.value[0].name
				this.form.institutionCode = e.value[0].value
				this.institutionShow = false

			},
			oversize(e) {
				this.$u.toast("请传2MB以内大小的图片！");
				return false;
			},

			// 删除图片
			deletePic(event) {
				console.log(event);
				const {
					name,
					index
				} = event;

				if (index >= 0 && index < this[`fileList${name}`].length) {
					this[`fileList${name}`].splice(index, 1);

					// 更新对应的 list 字段
					// if (name === '1') {
					// 	this.list1 = this.fileList1.map(item => item.url).join(',');
					// 	console.log('Updated list1:', this.list1);
					// } else
					if (name === '1') {
						this.list1 = this.fileList1.length > 0 ? this.fileList1[0].url : '';
						console.log('Updated list1:', this.list1);
					} else if (name === '2') {
						this.list2 = this.fileList2.length > 0 ? this.fileList2[0].url : '';
						console.log('Updated list2:', this.list2);
					} else if (name === '3') {
						this.list3 = this.fileList3.length > 0 ? this.fileList3[0].url : '';
						console.log('Updated list3:', this.list3);
					} else if (name === '4') {
						this.list4 = this.fileList4.length > 0 ? this.fileList4[0].url : '';
						console.log('Updated list4:', this.list4);
					} else if (name === '5') {
						this.list5 = this.fileList5.length > 0 ? this.fileList5[0].url : '';
						console.log('Updated list5:', this.list4);
					} else if (name === '6') {
						this.list6 = this.fileList6.length > 0 ? this.fileList6[0].url : '';
						console.log('Updated list6:', this.list6);
					}

					// 确保对应的 list 字段是一个数组
					let list = this[`list${name}`];
					if (!Array.isArray(list)) {
						console.warn(`list${name} is not an array, skipping splice operation`);
					} else {
						list.splice(index, 1);
						console.log(`Updated list${name}:`, list);
					}
				} else {
					console.error('Invalid index');
				}
			},
			// 新增图片
			async afterRead(event) {
				let lists = [].concat(event.file);
				let fileListLen = this[`fileList${event.name}`].length;
				let categoryMap = [{
						category: 'user'
					},
					{
						category: 'user'
					},
					{
						category: 'user'
					},
					{
						category: 'cert'
					},
					{
						category: 'cert'
					},
					{
						category: 'cert'
					},
				];
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					});
				});
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url, categoryMap[event.name - 1].category);
					let item = this[`fileList${event.name}`][fileListLen];
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}));
					console.log(this[`fileList${event.name}`], ` this[\`fileList${event.name}\`]`)
					fileListLen++;
					// 更新对应的list字段
					if (event.name === '1') {
						this.list1 = this.fileList1[0]?.url || '';
					} else if (event.name === '2') {
						this.list2 = this.fileList2[0]?.url || '';
					} else if (event.name === '3') {
						this.list3 = this.fileList3[0]?.url || '';
					} else if (event.name === '4') {
						this.list4 = this.fileList4[0]?.url || '';
					} else if (event.name === '5') {
						this.list5 = this.fileList5[0]?.url || '';
					} else if (event.name === '6') {
						this.list6 = this.fileList6[0]?.url || '';
					}
				}
			},
			uploadFilePromise(url, category) {
				console.log('category', category)
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://testy.hschool.com.cn//api/common/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							user: 'test',
							category: category
						},
						header: {
							"token": uni.getStorageSync("token")
						},
						success: (res) => {
							resolve(JSON.parse(res.data).data.fullurl);
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},
			// 查询状态
			// -1未申请 0待审核 1审核通过 2审核失败
			searchStatus() {
				uni.$u.http.get('/api/school/shop/auth_info', {
					params: {}
				}).then(res => {
					if (res.code == 1) {
						this.apply_info = res.data.apply_info
						this.State = res.data.type
						console.log(res.data.type)
						if (res.data.auth_status == 2) {
							this.status = 2
							this.reason = res.data.reason


						} else if (res.data.auth_status == 0) {
							this.status = 0
						} else if (res.data.auth_status == 1) {
							this.status = 1
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 验证电话号码格式的方法
			validateTel(tel) {
				// 这里使用一个简单的正则表达式来验证电话号码格式
				const telRegex = /^1[3-9]\d{9}$/; // 假设是中国大陆的手机号码格式
				return telRegex.test(tel);
			},

			apply() {
				let url = ''
				let params = {}
				let usciRegex = /^[0-9A-Z]{18}$/;
				let _IDRe18 =
					/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
				let _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
				if (!this.form.tel || !this.validateTel(this.form.tel)) {
					// 提示电话号码不对
					uni.showToast({
						title: '请输入正确的电话号码！',
						icon: 'none',
						duration: 2000
					});
					return;
				}


				// 校验身份证：
				if (_IDRe18.test(this.form.cardNumber) || _IDre15.test(this.form.cardNumber)) {
					console.log(' 验证通过 ')
				} else {
					uni.showToast({
						title: '请输入正确的身份证号！',
						icon: 'none',
						duration: 2000
					});
					return;
				}



				// 校验性别
				if (this.form.sex == '') {
					uni.showToast({
						title: '请输入性别！',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 校验详细地址
				if (this.form.address_detail == '') {
					uni.showToast({
						title: '请输入详细地址！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				// 校验民族
				if (this.form.nation == '') {
					uni.showToast({
						title: '请输入民族！',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 校验出生日期
				if (this.form.birth == '') {
					uni.showToast({
						title: '请输入出生日期！',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 校验籍贯
				if (this.form.nativePlace == '') {
					uni.showToast({
						title: '请输入籍贯！',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 校验政治面貌
				if (this.form.outlook == '') {
					uni.showToast({
						title: '请选择政治面貌！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				// 校验位置
				if (this.form.address == '') {
					uni.showToast({
						title: '请选择位置！',
						icon: 'none',
						duration: 2000
					});
					return;
				}


				if (this.form.cut == '机构') {
					url = '/api/school/shop/shopapply'
					params = {
						name: this.form.name,
						yyzzdm: this.form.yyzzdm,
						tel: this.form.tel,
						desc: this.form.desc,
						front_idcard_image: this.list5,
						reverse_idcard_image: this.list6,
						images: [this.list1, this.list2, this.list3].join(","),
						yyzz_images: this.list4,
						longitude: this.form.longitude,
						latitude: this.form.latitude,
						address: this.form.address,
						address_detail: this.form.address_detail,
						province: '410000',
						city: '410300',
						district: this.form.institutionCode,
						establish_time: this.form.establish_time,
						people: this.form.people,
						legal_entity: this.form.legalEntity,
						gender: this.form.sex == '男' ? 1 : 0,
						nation: this.form.nation,
						out_look: this.form.outlook,
						birthtime: this.form.birthtime,
						native_place: this.form.nativePlace,
						card_number: this.form.cardNumber,
						diploma: this.form.diploma,
						post: this.form.post,
						social_position: this.form.socialPosition

					}
					if (this.list1 == '') {
						uni.showToast({
							title: '请上传机构门头照片！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.list2 == '') {
						uni.showToast({
							title: '请上传机构内部环境照片！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.list3 == '') {
						uni.showToast({
							title: '请上传机构外部环境照片！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.form.legalEntity == '') {
						uni.showToast({
							title: '请输入机构法人/委托人！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (this.form.institutionCode == '') {
						uni.showToast({
							title: '请选择县区！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					if (usciRegex.test(this.form.yyzzdm)) {
						console.log('验证通过');
					} else {
						uni.showToast({
							title: '请输入正确的社会统一信用代码！',
							icon: 'none',
							duration: 2000
						});
						return;
					}

				} else {
					url = '/api/school/shop/userapply'
					params = {
						name: this.form.name,
						tel: this.form.tel,
						desc: this.form.desc,
						front_idcard_image: this.list5,
						reverse_idcard_image: this.list6,
						longitude: this.form.longitude,
						latitude: this.form.latitude,
						address: this.form.address,
						address_detail: this.form.address_detail,
						province: '410000',
						city: '410300',
						district: this.form.institutionCode,
						establish_time: this.form.establish_time,
						people: this.form.people,
						legal_entity: this.form.legalEntity,
						gender: this.form.sex == '男' ? 1 : 0,
						nation: this.form.nation,
						out_look: this.form.outlook,
						birthtime: this.form.birthtime,
						native_place: this.form.nativePlace,
						card_number: this.form.cardNumber,
						diploma: this.form.diploma,
						post: this.form.post,
						social_position: this.form.socialPosition
					}
				}
				console.log(params)
				uni.$u.http.post(url, params).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '申请中',
							icon: 'none',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.switchTab({
										url: "/pages/my/index"
									})
								}, 2000);
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});

			}
		}
	}
</script>

<style lang="scss" scoped>
	.backImg {
		background: linear-gradient(to bottom, #F1F2F8 0%, #F1F2F8 50%, #FFFFFF 100%);
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}

	.box {
		width: 690rpx;

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			margin: 42rpx 0 24rpx 0;

		}

		.reason {
			width: 100%;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 36rpx;
			color: #343434;
			line-height: 36rpx;
			padding: 30rpx;
			width: 630rpx;
			letter-spacing: 4.5rpx;
		}

		.first {
			width: 100%;
			// height: 266rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			// margin-top: 32rpx;

			.row {
				width: 642rpx;
				margin-top: 7rpx;
				justify-content: space-between;

				.label {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #343434;
					line-height: 32rpx;
				}

				.row-right {}
			}

			.line-row {
				margin-top: 25rpx;
				width: 642rpx;
				height: 1rpx;
				background: #F1F2F8;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
		}

		::v-deep.plasty {
			text-align: right;
		}

		.second {
			width: 690rpx;
			// height: 340rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 32rpx;
			margin-bottom: 32rpx;
		}

		.third {
			width: 690rpx;

			.header {
				margin: 42rpx 0 24rpx 0;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28rpx;
				color: #343434;
				line-height: 32rpx;
			}
		}
	}


	::v-deep .u-textarea {
		height: 100%;
	}

	.ji ::v-deep .u-upload .u-upload__wrap__preview__image {
		width: 690rpx !important;
		height: 450rpx !important;
	}

	.shenfen ::v-deep .u-upload .u-upload__wrap__preview__image {
		width: 690rpx !important;
		height: 450rpx !important;
	}

	.bottom {
		width: 750rpx;
		height: 122rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -6rpx 12rpx 0rpx rgba(111, 190, 255, 0.1);
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: fixed;
		bottom: 0;
		z-index: 999;

		span {
			width: 642rpx;
			height: 80rpx;
			background: #008CFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #FFFFFF;
			line-height: 32rpx;
		}
	}

	.input {
		text-align: right;
		font-family: PingFang SC, PingFang SC;
		font-size: 28rpx;
		color: #343434;
		line-height: 32rpx;
	}

	.shenfen ::v-deep .u-transition.data-v-39e33bf2.vue-ref.u-fade-enter-to.u-fade-enter-active:not(:first-child) {
		margin-top: 20rpx;
	}

	.popup {
		// width: 690rpx;
		height: 950rpx;
		margin-top: 40rpx;


	}

	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		// margin: 30rpx 0;
		height: 146rpx;
	}


	.zhixiao {
		width: 642rpx;
		height: 80rpx;
		background: #E8E8E8;
		border-radius: 401rpx 401rpx 401rpx 401rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 34rpx;
		color: #FFFFFF;
		line-height: 32rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	// 滚动条样式
	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}
</style>