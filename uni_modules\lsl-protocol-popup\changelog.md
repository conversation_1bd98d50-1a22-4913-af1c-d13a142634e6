## 1.0.12（2023-09-11）
新增 授权时 获取手机号，`具体 移步查看 参数说明`
## 1.0.11（2023-09-11）
新增字段如下： |open_type|String| 'agreePrivacyAuthorization' | 基础库3.0.0 不支持多个 自定义 Button open-type 有效值 多个进行竖线隔开 open-type="getPhoneNumber|agreePrivacyAuthorization" | |is_force_phone|Boolean| false |是否强制授权手机号 open_type 参数中需存在 getPhoneNumber 才会生效 |show_toast_phone|String| |是否强制授权手机号 点击拒绝时提醒内容，不填则不提醒 |
## 1.0.10（2023-09-11）
新增字段如下：
 |open_type|String| 'agreePrivacyAuthorization' | 基础库3.0.0 不支持多个 自定义 Button open-type 有效值 多个进行竖线隔开 ```open-type="getPhoneNumber|agreePrivacyAuthorization"``` 
 | |is_force_phone|Boolean| false |是否强制授权手机号 open_type 参数中需存在 getPhoneNumber 才会生效 
|show_toast_phone|String|  |是否强制授权手机号 点击拒绝时提醒内容，不填则不提醒 |
## 1.0.9（2023-09-11）
添加新 特性：

新增字段如下：
|open_type|String| 'agreePrivacyAuthorization' | `基础库3.0.0 不支持多个` 自定义 Button open-type 有效值 多个进行竖线隔开 open-type="getPhoneNumber|agreePrivacyAuthorization" |
|is_force_phone|Boolean| false |是否强制授权手机号 open_type 参数中需存在 getPhoneNumber 才会生效 |
|show_toast_phone|String|  |是否强制授权手机号 点击拒绝时提醒内容，不填则不提醒 |
## 1.0.8（2023-09-11）
文档完善
## 1.0.7（2023-09-11）
文档 更新 
## 1.0.6（2023-09-09）
1. 点击同意的时候 显示 uni.showTabBar();
## 1.0.5（2023-09-09）
更新
## 1.0.4（2023-09-09）
添加协议中间的分隔符号，完善协议
## 1.0.3（2023-09-09）
添加协议中间的分隔符号
## 1.0.2（2023-09-09）
协议完善
## 1.0.1（2023-09-09）
完善文档
## 1.0.0（2023-09-09）
1. 支持自定义弹窗内容,弹出方式,触发条件,主题颜色
2. 支持多个自定义协议 和 协议多种模式，[ 文档链接，小程序页面地址 ]
