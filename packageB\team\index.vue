<template>
	<view class="team-container">
		<view v-for="item in list" style="padding:0rpx 30rpx;">
			<view style="display: flex;align-items: center;justify-content: space-between;">
				<view>
					<view style="font-size: 28rpx;color: #323232;">{{ item.name }} {{ item.mobile }}</view>
					<view style="color: #9C9C9C;font-size: 24rpx;margin-top: 10rpx;">
						<text>身份证</text>
						<text style="margin-left: 10rpx;">{{ item.idnum }}</text>
					</view>
				</view>
				<view style="font-size: 28rpx;">
					<text style="color: #FF4810;" @click="edit(item)">编辑</text>
					<text style="color: #323232;margin-left: 30rpx;" @click="del(item)">删除</text>
				</view>
			</view>
			<view style="height: 1px;background-color: #F0F0F0;width: 100%;margin: 30rpx 0px;"></view>
		</view>
		<view style="width: 100%;height: 200rpx;"></view>
		<view
			style="padding: 30rpx;position: fixed;bottom: 0rpx;left: 0;width: 92%;z-index: 10;background-color: #ffffff;">
			<view class="btn_2" @click="openAdd()">
				<view>
					<u-icon name="plus" color="#BBFC5B" size="15"></u-icon>
				</view>
				<view style="margin-left: 10rpx;">添加报名人</view>
			</view>
		</view>
		<u-popup :show="show" :round="20" :closeable="true" mode="bottom" @close="close">
			<view
				style="padding: 26rpx;font-size: 36rpx;font-weight: 400;color: #3D3D3D;text-align: center;font-weight: 600;">
				新增报名人身份信息
			</view>
			<view style="padding: 0rpx 30rpx;">
				<view style="display: flex;align-items: center;justify-content: space-between;padding: 30rpx 0rpx;">
					<view style="font-size: 30rpx;">
						姓名
					</view>
					<view style="width: 75%;">
						<input :cursor-spacing="300" type="text" placeholder="请填写真实姓名" class="input"
							v-model="form.name" />
					</view>
				</view>
				<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;padding: 30rpx 0rpx;">
					<view style="font-size: 30rpx;">
						身份证号
					</view>
					<view style="width: 75%;">
						<input :cursor-spacing="300" type="idcard" placeholder="请填写身份证号" class="input"
							v-model="form.idnum" />
					</view>
				</view>
				<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;padding: 30rpx 0rpx;">
					<view style="font-size: 30rpx;">
						手机号
					</view>
					<view style="width: 75%;">
						<input :cursor-spacing="300" type="number" placeholder="请填写手机号" class="input"
							v-model="form.mobile" />
					</view>
				</view>
				<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>

				<view style="display: flex;justify-content: flex-start;align-items: center;margin-top: 30rpx;">
					<image v-if="privacyShow == false" src="../../static/fabu/nocheck.png"
						style="width: 34rpx;height: 34rpx;border-radius: 63rpx;" @click="privacyShow = true"></image>
					<image v-else src="../../static/fabu/check.png" @click="privacyShow = false"
						style="width: 34rpx;height: 34rpx;border-radius: 63rpx;"></image>
					<view  @click="privacyShow = !privacyShow" style="color: #3D3D3D;font-size: 22rpx;line-height: 33rpx;margin-left: 15rpx;">
						隐私报名(报名信息仅对我和组织者公开)</view>
				</view>
				<view style="font-size: 24rpx;color: #9C9C9C;margin-top: 40rpx;">
					<text>你的个人信息我们将严格保密并仅用于投保使用，详情可查看</text>
					<text style="color: #0CA013;" @click="go('/packageB/privacy?type=privacy')">《隐私政策》</text>
					<text>和</text>
					<text style="color: #0CA013;" @click="go('/packageB/privacy?type=user_protocol')">《用户协议》</text>
				</view>
			</view>
			<view style="margin-top: 40rpx;">
				<!-- <view class="btn_1" @click="save">确认
				</view> -->
				<view class="btn_1" v-if="isFormValid" @click="save">确认
				</view>
				<view class="btn_3" v-else @click="unSave">确认</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			privacyShow: true,
			show: false,
			form: {
				name: '',
				idnum: '',
				mobile: ''
			},
			isAdd: 1,
			list: []
		}
	},
	computed: {
		isFormValid() {
			return this.form.name.length > 0 &&
				   this.form.idnum.length > 0 &&
				   this.form.mobile.length > 0;
		}
	},
	watch() {

	},
	onLoad() {
		this.getList()
	},
	methods: {
		openAdd() {
			this.privacyShow=true;
			this.show = true;
			this.isAdd = 1;
			this.form = {
				name: '',
				idnum: '',
				mobile: ''
			};
		},

		edit(item) {
			console.log('修改报名人', item);
			this.isAdd = 0;
			this.form.name = item.name;
			this.form.idnum = item.idnum;
			this.form.ids = item.id;
			this.form.mobile = item.mobile;
			this.form.open = item.open;
			this.show = true;
			if(this.form.open == 0) {
				this.privacyShow = true;
			}else {
				this.privacyShow = false;
			}
			// this.checkFormValidity();
		},
		del(item) {
			var that = this;
			//确定要删除吗？
			uni.showModal({
				title: '提示',
				content: '确定要删除吗？',
				success: (res) => {
					if (res.confirm) {
						that.delData(item);
					}

				}
			})
		},
		delData(item) {
			uni.$u.http.post('/api/school.newactivity.activity_join/del', {
				ids: item.id
			}).then(res => {
				console.log(res)
				if (res.code == 1) {
					uni.showToast({
						title: res.msg,
						icon: 'success',
						duration: 2000
					});
					this.getList();
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			})
		},
		getList() {
			uni.$u.http.get('/api/school.newactivity.activity_join/people_list').then(res => {
				console.log(res)
				if (res.code == 1) {
					this.list = res.data.list
				} else {
					this.list = [];
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			})
		},
		save() {
			if (this.form.name == '') {
				uni.showToast({
					title: '请填写真实姓名',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (this.form.idnum == '') {
				uni.showToast({
					title: '请填写身份证号',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			if (this.form.mobile == '') {
				uni.showToast({
					title: '请填写手机号',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			//正则判断身份证
			const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
			if (!reg.test(this.form.idnum)) {
				uni.showToast({
					title: '请输入正确的身份证号',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			if (!/^1[3-9]\d{9}$/.test(this.form.mobile)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			console.log('privacyShow', this.privacyShow);
			if (this.privacyShow == true) {
				this.form.open = 0
			} else {
				this.form.open = 1
			}
			var url = "/api/school.newactivity.activity_join/add";
			if (this.isAdd == 0) {
				url = "/api/school.newactivity.activity_join/edit";
			}
			uni.$u.http.post(url, this.form).then(res => {
				console.log(res)
				if (res.code == 1) {
					uni.showToast({
						title: res.msg,
						icon: 'success',
						duration: 2000
					});
					this.getList()
					this.show = false;
					this.privacyShow = true;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			})
		},
		unSave() {
			uni.showToast({
				title: '填完全部信息后，才能提交',
				icon: 'none'
			})
		},
		close() {
			this.show = false
		},
		go(url) {
			uni.navigateTo({
				url: url
			})
		}
	}
}
</script>

<style scoped lang="scss">
.team-container {
	font-family: PingFang SC, PingFang SC;
	padding-top: 20rpx;
}

.btn_2 {
	width: 95%;
	height: 90rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn_1 {
	width: 95%;
	height: 90rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn_3 {
	width: 95%;
	height: 90rpx;
	background: #f0f0f0;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #9c9c9c;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.input {
	text-align: right;
}
</style>