<template>
	<view class="protocol_box" @click.stop="protocolClick()">
		<view class="select" :class="{active: agree}" @click="agreeClick"></view>
		<view style="color:#9C9C9C;">
			<text>我已阅读并同意</text>
			<text class="key-text" style="color: #0CA013;" @click.stop="agreeClick1"> {{name}} </text>
			<text v-if="nameOne!=''"> 和 </text>
			<text class="key-text" style="color: #0CA013;" @click.stop="agreeClick2"> {{nameOne}} </text>
			<text> {{desc}} </text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 是否同意
			agree: {
				type: Boolean,
				default: false
			},
			desc: {
				type: String,
				default: ''
			},
			name: {
				type: String,
				default: ''
			},
			nameOne: {
				type: String,
				default: ''
			},
			// 协议数组
			protocolArr: {
				type: Array,
				default: function() {
					return [];
				}
			},

		},

		methods: {
			// 是否同意勾选
			agreeClick() {

				this.$emit('click')

			},
			agreeClick1(){
				this.$emit('clickOne')
			},
			agreeClick2(){
				this.$emit('clickTwo')
			},
			protocolClick(tag) {

				this.$emit('protocolClick', tag)

			}

		}


	}
</script>




<style lang="scss" scoped>
	//主题色 #008CFF
	$themeColor: #323232;



	.protocol_box {
		display: flex;
		flex-wrap: nowrap;
		justify-content: center;
		align-items: center;

		//margin-top: 20rpx;
		// margin-left: 40rpx;
		font-size: 28rpx;
		color: $themeColor;
		font-size: 13px;

		.select {
			width: 36rpx;
			height: 36rpx;
			background-image: url("./ic_nor.png");
			background-position: center center;
			background-repeat: no-repeat;
			background-size: 100% auto;
			margin-right: 15rpx;
			margin-top: 2px;


			&.active {
				background-image: url("./ic_sel.png");
			}
		}

		.key-text {
			color: $themeColor;
			font-size: 13px;
			text-decoration: underline;
			white-space: pre-wrap;
		}


	}
</style>