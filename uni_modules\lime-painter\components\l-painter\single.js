var t=function(){return t=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},t.apply(this,arguments)};function e(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{a(n.next(t))}catch(t){o(t)}}function h(t){try{a(n.throw(t))}catch(t){o(t)}}function a(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,h)}a((n=n.apply(t,e||[])).next())}))}function i(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:h(0),throw:h(1),return:h(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function h(o){return function(h){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,h])}}}var n={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},r=["Top","Right","Bottom","Left"],o="right",s="bottom",h=["contentSize","clientSize","borderSize","offsetSize"],a="row",l="column",d={TOP:"top",MIDDLE:"middle",BOTTOM:s},c={LEFT:"left",CENTER:"center",RIGHT:o},f="view",u="text",p="image",g="qrcode",v="block",y="inline-block",x="none",b="flex",w="absolute",m="fixed",S={display:v,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:a,flexWrap:"nowrap",textAlign:"left",alignItems:"flex-start",justifyContent:"flex-start",position:"static",transformOrigin:"".concat("center"," ").concat("center")},z={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth,screenHeight:window.innerHeight}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:r.naturalWidth,height:r.naturalHeight,path:r.src,src:e})},r.onerror=n,r.src=e}},I="object"==typeof window?"undefined"==typeof uni||"undefined"!=typeof uni&&!uni.addInterceptor?n.WEB:n.H5:"object"==typeof swan?n.MP_BAIDU:"object"==typeof tt?n.MP_TOUTIAO:"object"==typeof plus?n.PLUS:"object"==typeof wx?n.MP_WEIXIN:void 0,M=I==n.MP_WEIXIN?wx:"undefined"!=typeof uni?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,z):"undefined"!=typeof window?z:uni;if(!M.upx2px){var B=((M.getSystemInfoSync&&M.getSystemInfoSync()).screenWidth||375)/750;M.upx2px=function(t){return B*t}}function W(t){return/^-?\d+(\.\d+)?$/.test(t)}function k(t,e,i){if(W(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|vw|vh|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;if("rpx"===r)o=M.upx2px(t);else if("px"===r)o=1*t;else if("%"===r&&e)o=t*k(e)/100;else if("em"===r&&e)o=t*k(e||14);else if(["vw","vh"].includes(r)){var s=M.getSystemInfoSync(),h=s.screenWidth,a=s.screenHeight;o=t*("vw"==r?h:a)/100}return 1*o.toFixed(2)}return 0}function P(t){return/%$/.test(t)}function O(t){return/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(rpx|px)$/.test(t)}var T=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},L=function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var s=function(t,e,i,n,r){void 0===n&&(n=0);void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),s=o&&o[1]?parseFloat(o[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===s)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===s)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===s)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var h=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===h)return{x0:n,y0:i+r,x1:e+n,y1:r};if(s===180-h)return{x0:n,y0:r,x1:e+n,y1:i+r};if(s===180+h)return{x0:e+n,y0:r,x1:n,y1:i+r};if(s===360-h)return{x0:e+n,y0:i+r,x1:n,y1:r};var a=0,l=0,d=0,c=0;if(s<h||s>180-h&&s<180||s>180&&s<180+h||s>360-h){var f=s*Math.PI/180,u=s<h||s>360-h?i/2:-i/2,p=Math.tan(f)*u,g=s<h||s>180-h&&s<180?e/2-p:-e/2-p;a=-(d=p+(v=Math.pow(Math.sin(f),2)*g)),l=-(c=u+v/Math.tan(f))}if(s>h&&s<90||s>90&&s<90+h||s>180+h&&s<270||s>270&&s<360-h){var v;f=(90-s)*Math.PI/180,p=s>h&&s<90||s>90&&s<90+h?e/2:-e/2,u=Math.tan(f)*p,g=s>h&&s<90||s>270&&s<360-h?i/2-u:-i/2-u;a=-(d=p+(v=Math.pow(Math.sin(f),2)*g)/Math.tan(f)),l=-(c=u+v)}return a=Math.round(a+e/2)+n,l=Math.round(i/2-l)+r,d=Math.round(d+e/2)+n,c=Math.round(i/2-c)+r,{x0:a,y0:l,x1:d,y1:c}}(r,t,e,i,n),h=s.x0,a=s.y0,l=s.x1,d=s.y1,c=o.createLinearGradient(h,a,l,d),f=r.match(/linear-gradient\((.+)\)/)[1],u=R(f.substring(f.indexOf(",")+1)),p=0;p<u.colors.length;p++)c.addColorStop(u.percents[p],u.colors[p]);o.setFillStyle(c)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var s=R(r.match(/radial-gradient\((.+)\)/)[1]),h=Math.round(t/2)+i,a=Math.round(e/2)+n,l=o.createRadialGradient(h,a,0,h,a,Math.max(t,e)/2),d=0;d<s.colors.length;d++)l.addColorStop(s.percents[d],s.colors[d]);o.setFillStyle(l)}(e,i,n,r,t,o)};function R(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function F(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function A(){return A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},A.apply(this,arguments)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function H(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return E(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?E(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function C(t){return"number"==typeof t}function D(t){return"auto"===t||null===t}function $(t){return/%$/.test(t)}var Y=p,U=u,N=f,X=g,_=y,q=w,G=m;function V(t){return t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}))}function J(t,e){var i,n,o=function(t){var e=t.match(/([a-z]+)/)[1];return[e,V(t.split(e)[1])]}(t),s=o[0],h=o[1],a=e.split(" ");if(h)return(i={})[s+h]=e,i;if(a.length&&!h){var l=a[0],d=a[1],c=a[2],f=a[3];return(n={})[s+r[0]]=l,n[s+r[1]]=d||l,n[s+r[2]]=c||l,n[s+r[3]]=f||d||l,n}}function Q(t){t=t.trim();for(var e=new Array,i="+",n="",r=t.length,o=0;o<r;++o){if("."===t[o]||!isNaN(Number(t[o]))&&" "!==t[o])n+=t[o];else if("("===t[o]){for(var s=1,h=o;s>0;)"("===t[h+=1]&&(s+=1),")"===t[h]&&(s-=1);n="".concat(Q(t.slice(o+1,h))),o=h}if(isNaN(Number(t[o]))&&"."!==t[o]||o===r-1){var a=parseFloat(n);switch(i){case"+":e.push(a);break;case"-":e.push(-a);break;case"*":e.push(e.pop()*a);break;case"/":e.push(e.pop()/a)}i=t[o],n=""}}for(var l=0;e.length;)l+=e.pop();return l}var Z,K=0,et=function(){function t(){F(this,"elements",[]),F(this,"afterElements",[]),F(this,"beforeElements",[]),F(this,"ids",[]),F(this,"width",0),F(this,"height",0),F(this,"top",0),F(this,"left",0),F(this,"pre",null),F(this,"offsetX",0),F(this,"offsetY",0),K++,this.id=K}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){if(!this.ids.includes(t.id)){this.ids.push(t.id),this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()}},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.includes(t.id)||(this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t))},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;"center"===e?t/=2:"left"===e&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return e===s?this.height-t.contentSize.height:"middle"===e?(this.height-t.contentSize.height)/2:0},e.setIndent=function(t){var e=t.style.textIndent;if(e&&/^calc/.test(e)){var i=/^calc\((.+)\)$/.exec(e);if(i&&i[1]){var n=i[1].replace(/([^\s\(\+\-\*\/]+)\.(left|right|bottom|top|width|height)/g,(function(e){var i=e.split("."),n=i[0],r=i[1],o=t.parent.querySelector(n);if(o&&o.offsetSize){var s={right:o.offsetSize.left+o.offsetSize.width,bottom:o.offsetSize.top+o.offsetSize.height};return o.offsetSize[r]||s[r]||0}})),r=Q(n.replace(new RegExp(/-?[0-9]+(\.[0-9]+)?(rpx|px|%)/,"g"),k));t.style.textIndent=r}}},e.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style.top=i.top+r,t.style.left=n?n.offsetSize.left+n.offsetSize.width:i.left,t.getBoxPosition()}))},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),it=((Z={})[a]={width:"width",contentWidth:"width",lineMaxWidth:"lineMaxWidth",left:"left",top:"top",height:"height",lineMaxHeight:"lineMaxHeight",marginLeft:"marginLeft"},Z[l]={width:"height",contentWidth:"height",lineMaxWidth:"lineMaxWidth",left:"top",top:"left",height:"width",lineMaxHeight:"lineMaxHeight",marginLeft:"marginTop"},Z),nt=function(t){var e,i;function n(){var e;return F(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.width=0,e.key=null,e.flexDirection="row",e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,j(e,i);var r=n.prototype;return r.bind=function(t){this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.flexDirection=t.parent.style.flexDirection,this.key=it[this.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},r.add=function(t){this.ids.push(t.id);var e=t.style.flex;C(e)?this.flexTotal+=e:C(this.getWidth(t.style))&&(this.exactValue+=this.getWidth(t.offsetSize)),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},r.closeLine=function(){this.calcFlex()},r.initHeight=function(t){this[this.key.height]=0},r.getWidth=function(t){return t[this.key.width]||0},r.getHeight=function(t){return t[this.key.height]||0},r.setWidth=function(t,e){t[this.key.width]=e},r.setHeight=function(t,e){t[this.key.height]=e},r.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth],i=0;this.elements.forEach((function(n){var r=n.style,o=n.contentSize,s=t.getWidth(r)||t.getWidth(o);C(r.flex)&&(s=r.flex/t.flexTotal*(e-t.exactValue)),t.setWidth(n.computedStyle,s),n.isFlexCalc=!0,delete n.line,delete n.lines,delete n.lineMaxWidth,n.getBoxWidthHeight(),i=Math.max(i,t.getHeight(n.offsetSize))})),this.setHeight(this,i)},r.refreshWidthHeight=function(t){var e=this.container.style.alignItems;e&&!t.style.alignSelf&&(t.style.alignSelf=e);var i=this.getHeight(t.offsetSize);i>this[this.key.height]&&(this.container[this.key.lineMaxHeight]=this[this.key.height]=i),this[this.key.width]+=this.getWidth(t.offsetSize);var n=Math.min(this.getWidth(this),!this.getWidth(this.container.contentSize)&&1/0);(this.container[this.key.lineMaxWidth]||0)<n&&(this.container[this.key.lineMaxWidth]=n)},r.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+t.getWidth(i.offsetSize)}),0),i=(this.outerWidth==1/0?0:this.outerWidth-e)||0,n=this.container.style.justifyContent;"center"===n?i/=2:"flex-start"===n?i=0:["space-between","space-around"].includes(n)&&(!function(e,i){void 0===i&&(i=0),i/=t.elements.length+(e?-1:1),t.elements.forEach((function(n,r){var o;e&&!r||(n.style.margin?n.style.margin[t.key.marginLeft]+=i:n.style.margin=((o={})[t.key.marginLeft]=i,o),n.getBoxPosition())})),i=0}("space-between"==n,i),i=0),this.offsetX=i||0,this.refreshYAlign()},r.refreshYAlign=function(){var t=this;if(1==this.container.lines.length)return 0;var e=this.container.lines.reduce((function(e,i){return e+t.getHeight(i)}),0),i=this.container.style.alignItems,n=this.getHeight(this.container.contentSize);if("center"===i){var r=(n-e)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=r}))}if("flex-end"===i){var o=n-e;this.container.lines[0].offsetY=o}},r.getOffsetY=function(t){if(this.container.lines.length>1)return 0;var e=t.style.alignSelf,i=this.getHeight(this.container.contentSize),n=i-this.getHeight(t.offsetSize);return"flex-end"===e?n:"center"===e?n/2:"stretch"===e?(n&&t.name==f&&(t.style[this.key.width]=this.getWidth(t.offsetSize),t.style[this.key.height]=i,delete t.line,delete t.lines,t.getBoxWidthHeight()),0):0},r.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style[i.key.top]=i[i.key.top]+r,t.style[i.key.left]=n?n.offsetSize[i.key.left]+i.getWidth(n.offsetSize):i[i.key.left],t.getBoxPosition()}))},n}(et),rt=p,ot=u,st=f,ht=v,at=y,lt=b,dt=w,ct=m,ft=0,ut={left:null,top:null,width:null,height:null},pt=new Map,gt=function(){function t(t,e,i,n){var o=this;F(this,"id",ft++),F(this,"style",{left:null,top:null,width:null,height:null}),F(this,"computedStyle",{}),F(this,"originStyle",{}),F(this,"children",{}),F(this,"layoutBox",A({},ut)),F(this,"contentSize",A({},ut)),F(this,"clientSize",A({},ut)),F(this,"borderSize",A({},ut)),F(this,"offsetSize",A({},ut)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.type||t.name,this.attributes=this.getAttributes(t);var s=function(t,e){var i,n=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],o=t.type,s=void 0===o?N:o,h=t.styles,a=void 0===h?{}:h,l=(e||{}).computedStyle,d=Object.assign({},S);if([U,Y,X].includes(s)&&!a.display&&(d.display=_),l)for(var c=0;c<n.length;c++){var f=n[c];(a[f]||l[f])&&(a[f]=a[(i=f,i.replace(/([A-Z])/g,"-$1").toLowerCase())]||a[f]||l[f])}for(var u=function(t){var e,i,n,o,h=a[t];if(/-/.test(t)&&(t=V(t),d[t]=h),/^(box|text)?shadow$/i.test(t)){var l=[];return h.replace(/((-?\d+(rpx|px|vw|vh)?\s+?){3})(.+)/,(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];l=t[1].match(/-?\d+(rpx|px|vw|vh)?/g).map((function(t){return k(t)})).concat(t[4])})),/^text/.test(t)?d.textShadow=l:d.boxShadow=l,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var c=t.match(/^border([BTRLa-z]+)?/)[0],f=t.match(/[W|S|C][a-z]+/),u=h.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?k(t,""):t}));return d[c]||(d[c]={}),1==u.length&&f?d[c][c+f[0]]=u[0]:d[c]=((e={})[c+"Width"]=W(u[0])?u[0]:0,e[c+"Style"]=u[1]||"solid",e[c+"Color"]=u[2]||"black",e),"continue"}if(/^background(color)?$/i.test(t))return d.backgroundColor=h,"continue";if(/^objectPosition$/i.test(t))return d[t]=h.split(" "),"continue";if(/^backgroundPosition$/i.test(t))return d[t]=h.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var p=/radius$/i.test(t),g="borderRadius",v=p?g:t.match(/[a-z]+/)[0],y=function(t,e){return"border".concat(t).concat(e,"Radius")},x=[0,0,0,0].map((function(t,e){return p?[y(r[0],r[3]),y(r[0],r[1]),y(r[2],r[1]),y(r[2],r[3])][e]:v+r[e]}));if("padding"===t||"margin"===t||/^(border)?radius$/i.test(t)){u="".concat(h).split(" ").map((function(e){return/^-?\d+(rpx|px|vh|vw)?$/.test(e)?k(e):"margin"!=t&&/auto/.test(e)?0:e}),[])||[0];var b=p?g:t,w=u[0],m=u[1],S=u[2],z=u[3];d[b]=((i={})[x[0]]=D(w)?0:w,i[x[1]]=W(m)||D(m)?m:w,i[x[2]]=D(W(S)?S:w)?0:W(S)?S:w,i[x[3]]=W(z)?z:null!=m?m:w,i)}else"object"==typeof d[v]||(d[v]=((n={})[x[0]]=d[v]||0,n[x[1]]=d[v]||0,n[x[2]]=d[v]||0,n[x[3]]=d[v]||0,n)),d[v][t]="margin"==v&&D(h)||$(h)?h:k(h);return"continue"}if(/^transform$/i.test(t))return d[t]={},h.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),o=function(t,e){return t.includes("deg")?1*t:e&&!$(e)?k(t,e):t};i.includes("matrix")?d[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?d[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?d[t][i]=/[X]/.test(i)?o(r[0],a.width):o(r[0],a.height):(d[t][i+"X"]=o(r[0],a.width),d[t][i+"Y"]=o(r[1]||r[0],a.height))})),"continue";if(/^font$/i.test(t)&&console.warn("font 不支持简写"),/^textindent/i.test(t)&&(d[t]=/^calc/.test(h)?h:k(h)),/^textstroke/i.test(t)){var I=t.match(/color|width|type$/i),M=(c="textStroke",h.split(" ").map((function(t){return/^\d+(rpx|px|vh|vw)?$/.test(t)?k(t):t})));return I?d[c]?d[c][I[0]]=M[0]:d[c]=((o={})[I[0]]=M[0],o):d[c]={width:M[0],color:M[1],type:M[2]},"continue"}/^left|top$/i.test(t)&&![q,G].includes(a.position)?d[t]=0:d[t]=/^-?[\d\.]+(px|rpx|vw|vh)?$/.test(h)?k(h):/em$/.test(h)&&s==U?k(h,a.fontSize):h},p=0,g=Object.keys(a);p<g.length;p++)u(g[p]);return d}(t,e);this.isAbsolute=s.position==dt,this.isFixed=s.position==ct,this.originStyle=s,this.styles=t.styles,Object.keys(s).forEach((function(t){Object.defineProperty(o.style,t,{configurable:!0,enumerable:!0,get:function(){return s[t]},set:function(e){s[t]=e}})}));var h={contentSize:A({},this.contentSize),clientSize:A({},this.clientSize),borderSize:A({},this.borderSize),offsetSize:A({},this.offsetSize)};Object.keys(h).forEach((function(t){Object.keys(o[t]).forEach((function(e){Object.defineProperty(o[t],e,{configurable:!0,enumerable:!0,get:function(){return h[t][e]},set:function(i){h[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.prev=function(t){void 0===t&&(t=this);var e=t.parent.getChildren();return e[e.findIndex((function(e){return e.id==t.id}))-1]},e.querySelector=function(t){var e=this.getChildren();if("string"!=typeof t)return null;var i=e.find((function(e){var i=e.id,n=e.attributes;return i==t||n&&n.uid==t}));return i||(this.parent&&this.parent.querySelector&&this.parent.querySelector(t)||null)},e.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){var r=n==o?"left":"top";[o,s].includes(n)&&void 0!==t.style[n]&&!W(t.originStyle[r])?t.style[r]=e[i[n]]-t.offsetSize[i[n]]-k(t.style[n],e[i[n]]):t.style[n]=k(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes,i=void 0===e?{}:e,n=t.uid,r=t.url,o=t.src,s=t.replace,h=t.text;return n&&(i.uid=n),(r||o)&&(i.src=i.src||r||o),s&&(i.replace=s),h&&(i.text=h),i},e.getOffsetSize=function(t,e,i){void 0===i&&(i=h[3]);var n=e||{},r=n.margin,o=(r=void 0===r?{}:r).marginLeft,s=void 0===o?0:o,a=r.marginTop,l=void 0===a?0:a,d=r.marginRight,c=void 0===d?0:d,f=r.marginBottom,u=void 0===f?0:f,p=n.padding,g=(p=void 0===p?{}:p).paddingLeft,v=void 0===g?0:g,y=p.paddingTop,x=void 0===y?0:y,b=p.paddingRight,w=void 0===b?0:b,m=p.paddingBottom,S=void 0===m?0:m,z=n.border,I=(z=void 0===z?{}:z).borderWidth,M=void 0===I?0:I,B=n.borderTop,W=(B=void 0===B?{}:B).borderTopWidth,k=void 0===W?M:W,P=n.borderBottom,O=(P=void 0===P?{}:P).borderBottomWidth,T=void 0===O?M:O,L=n.borderRight,R=(L=void 0===L?{}:L).borderRightWidth,F=void 0===R?M:R,A=n.borderLeft,j=(A=void 0===A?{}:A).borderLeftWidth,E=void 0===j?M:j,H=s<0&&c<0?Math.abs(s+c):0,C=l<0&&u<0?Math.abs(l+u):0,D=s>=0&&c<0,$=l>=0&&u<0;return i==h[0]&&(this[i].left=t.left+s+v+E+(D?2*-c:0),this[i].top=t.top+l+x+k+($?2*-u:0),this[i].width=t.width+(this[i].widthAdd?0:H),this[i].height=t.height+(this[i].heightAdd?0:C),this[i].widthAdd=H,this[i].heightAdd=C),i==h[1]&&(this[i].left=t.left+s+E+(D<0?-c:0),this[i].top=t.top+l+k+($?-u:0),this[i].width=t.width+v+w,this[i].height=t.height+x+S),i==h[2]&&(this[i].left=t.left+s+E/2+(D<0?-c:0),this[i].top=t.top+l+k/2+($?-u:0),this[i].width=t.width+v+w+E/2+F/2,this[i].height=t.height+x+S+T/2+k/2),i==h[3]&&(this[i].left=t.left+(D<0?-c:0),this[i].top=t.top+($?-u:0),this[i].width=t.width+v+w+E+F+s+c,this[i].height=t.height+x+S+T+k+u+l),this[i]},e.layoutBoxUpdate=function(t,e,i,n){var r=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var o=e||{},s=o.border,a=(s=void 0===s?{}:s).borderWidth,l=void 0===a?0:a,d=o.borderTop,c=(d=void 0===d?{}:d).borderTopWidth,f=void 0===c?l:c,u=o.borderBottom,p=(u=void 0===u?{}:u).borderBottomWidth,g=void 0===p?l:p,v=o.borderRight,y=(v=void 0===v?{}:v).borderRightWidth,x=void 0===y?l:y,b=o.borderLeft,w=(b=void 0===b?{}:b).borderLeftWidth,m=void 0===w?l:w,S=o.padding,z=(S=void 0===S?{}:S).paddingTop,I=void 0===z?0:z,M=S.paddingRight,B=void 0===M?0:M,W=S.paddingBottom,k=void 0===W?0:W,P=S.paddingLeft,O=void 0===P?0:P;i||(t.width-=O+B+x+m),1!==i||n||(t.height-=I+k+f+g)}this.layoutBox&&(h.forEach((function(i){return r.layoutBox[i]=r.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,r=void 0===n?0:n,o=t.top,s=void 0===o?0:o,h=A({},this.contentSize,{left:r,top:s}),a=this.contentSize.top-this.offsetSize.top,l=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var d,c=H(this.root.fixedLine.elements);!(d=c()).done;){var f=d.value;f.setPosition(f,this.root.offsetSize),f.getBoxPosition()}}if(e)for(var u,p=H(e.elements);!(u=p()).done;){var g=u.value,v=A({},this.borderSize,{left:r,top:s});g.setPosition(g,v);var y=this.borderSize.top-this.offsetSize.top,x=this.borderSize.left-this.offsetSize.left;g.style.left+=r+x,g.style.top+=s+y,g.getBoxPosition()}if(i)for(var b,w=H(i);!(b=w()).done;){b.value.layout(h.top+a,h.left+l)}return this.layoutBoxUpdate(h,t),this.layoutBox},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==ht},e.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==lt},e.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},e.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==lt)||void 0))},e.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==at},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,r=i.actualBoundingBoxAscent,o=i.actualBoundingBoxDescent;return{ascent:r,descent:o,width:n,fontHeight:r+o||.7*e+1}},e.getParentSize=function(t,e){if(void 0===t&&(t=this),void 0===e&&(e=!1),t&&t.parent){if(t.parent.contentSize.width)return t.parent.contentSize;if(e)return this.getParentSize(t.parent,e)}return null},e.getBoxWidthHeight=function(){var t=this,e=this.name,i=this.computedStyle,n=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,h=this.getChildren(),a=i.left,l=void 0===a?0:a,d=i.top,c=void 0===d?0:d,f=i.bottom,u=i.right,p=i.width,g=void 0===p?0:p,v=i.minWidth,y=i.maxWidth,x=i.minHeight,b=i.maxHeight,w=i.height,m=void 0===w?0:w,S=i.fontSize,z=i.fontWeight,I=i.fontFamily,M=i.fontStyle,B=i.position;i.textIndent;var W=i.lineClamp,P=i.lineHeight,O=i.padding,T=void 0===O?{}:O,L=i.margin,R=void 0===L?{}:L,F=i.border,A=(F=void 0===F?{}:F).borderWidth,j=void 0===A?0:A,E=i.borderRight,H=(E=void 0===E?{}:E).borderRightWidth,C=void 0===H?j:H,Y=i.borderLeft,U=(Y=void 0===Y?{}:Y).borderLeftWidth,N=void 0===U?j:U,X=o.contentSize&&o.contentSize.width,_=o.contentSize&&o.contentSize.height;if($(g)&&X&&(g=k(g,X)),$(g)&&!X&&(g=null),$(m)&&_&&(m=k(m,_)),$(m)&&!_&&(m=null),$(v)&&X&&(v=k(v,X)),$(y)&&X&&(y=k(y,X)),$(x)&&_&&(x=k(x,_)),$(b)&&_&&(b=k(b,_)),i.padding&&X)for(var q in i.padding)Object.hasOwnProperty.call(T,q)&&(T[q]=k(T[q],X));var G=T.paddingRight,V=void 0===G?0:G,J=T.paddingLeft,Q=void 0===J?0:J;if(i.margin&&[R.marginLeft,R.marginRight].includes("auto"))if(g){var Z=X&&X-g-V-Q-N-C||0;R.marginLeft==R.marginRight?R.marginLeft=R.marginRight=Z/2:D(R.marginLeft)?R.marginLeft=Z:R.marginRight=Z}else R.marginLeft=R.marginRight=0;var K=R.marginRight,tt=void 0===K?0:K,it=R.marginLeft,ht={width:g,height:m,left:0,top:0},at=Q+V+N+C+(void 0===it?0:it)+tt;if(this.offsetWidth=at,e==ot&&!this.attributes.widths){var lt=n.text||"";s.save(),s.setFonts({fontFamily:I,fontSize:S,fontWeight:z,fontStyle:M}),lt.length,"\n"==lt&&(lt="",this.isBr=!0),(""+lt).split("\n").map((function(e){var i=Array.from(e).map((function(e){var i=""+(/^[\u4e00-\u9fa5]+$/.test(e)?"cn":e)+I+S+z+M,n=pt.get(i);if(n)return{width:n,text:e};var r=t.measureText(e,S).width;return pt.set(i,r),{width:r,text:e}})),n=t.measureText(e,S),r=n.fontHeight,o=n.ascent,s=n.descent;t.attributes.fontHeight=r,t.attributes.ascent=o,t.attributes.descent=s,t.attributes.widths||(t.attributes.widths=[]),t.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e.width}),0)})})),s.restore()}if(e==rt&&null==g){var ct=n.width,ft=n.height;ht.width=this.contrastSize(Math.round(ct*m/ft)||0,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==ot&&null==g){var ut=this.attributes.widths,gt=Math.max.apply(Math,ut.map((function(t){return t.total})));if(o&&X>0&&(gt>X||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)gt=X;ht.width=this.contrastSize(gt,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==ot&&(o.style.flex||!this.attributes.lines)){var vt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e.width>ht.width?(vt++,e.width):t+e.width}),0)})),vt=W&&vt>W?W:vt,this.attributes.lines=vt}if(e==rt&&null==m){var yt=n.width,xt=n.height;n.text,ht.height=this.contrastSize(k(ht.width*xt/yt)||0,x,b),this.layoutBoxUpdate(ht,i,1)}e==ot&&null==m&&(P=k(P,S),ht.height=this.contrastSize(k(this.attributes.lines*P),x,b),this.layoutBoxUpdate(ht,i,1,!0)),!g&&o&&o.children&&X&&(!this.isFlex(o)||o.isFlexCalc)&&([st,ot].includes(e)&&this.isFlex()||e==st&&this.isBlock(this)&&this.isInFlow())&&(ht.width=this.contrastSize(X-(o.isFlexCalc?0:at),v,y),this.layoutBoxUpdate(ht,i)),g&&!$(g)&&(ht.width=this.contrastSize(g,v,y),this.layoutBoxUpdate(ht,i,0)),m&&!$(m)&&(ht.height=this.contrastSize(ht.height,x,b),this.layoutBoxUpdate(ht,i,1));var bt=0;if(h.length){var wt=null,mt=!1;h.forEach((function(e,n){e.getBoxWidthHeight();var r=h[n+1];if(r&&r.isInFlow()&&(e.next=r),!t.line||!t.line.ids.includes(e.id))if(e.isInFlow()&&!e.inFlexBox()){var o=t.getBoxState(wt,e);if(e.isBr)return mt=!0;t.line&&t.line.canIEnter(e)&&!o&&!mt?t.line.add(e):(mt=!1,(new et).bind(e)),wt=e}else e.inFlexBox()?t.line&&(t.line.canIEnter(e)||"nowrap"==i.flexWrap)?t.line.add(e):(new nt).bind(e):e.isFixed?t.root.fixedLine?t.root.fixedLine.fixedAdd(e):(new et).fixedBind(e):t.fixedLine?t.fixedLine.fixedAdd(e):(new et).fixedBind(e,1)})),this.lines&&(bt=this.lines.reduce((function(t,e){return t+e.height}),0))}var St=0,zt=0;if(!g&&(this.isAbsolute||this.isFixed)&&X){var It=B==dt?X:this.root.width,Mt=It-($(l)?k(l,It):l)-($(u)?k(u,It):u);St=i.left?Mt:this.lineMaxWidth}if(!m&&(null!=c?c:this.isAbsolute||this.isFixed&&_)){var Bt=B==dt?_:this.root.height,Wt=Bt-($(c)?k(c,Bt):c)-($(f)?k(f,Bt):f);zt=i.top?Wt:0}if(g&&!$(g)||ht.width||(ht.width=St||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?X||o.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,v,y),this.layoutBoxUpdate(ht,i,0)),m||!bt&&!zt||(ht.height=zt||this.contrastSize(bt,x,b),this.layoutBoxUpdate(ht,i)),i.borderRadius&&this.borderSize&&this.borderSize.width)for(var q in i.borderRadius)Object.hasOwnProperty.call(i.borderRadius,q)&&(i.borderRadius[q]=k(i.borderRadius[q],this.borderSize.width));return this.layoutBox},e.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.root.contentSize=this.contentSize,this.getBoxPosition(),this.offsetSize},t}(),vt=p,yt=u,xt=g,bt=f,wt=d.TOP,mt=d.MIDDLE,St=d.BOTTOM,zt=c.LEFT,It=c.CENTER,Mt=c.RIGHT,Bt=function(){function r(t){var e,i,r=this;this.v="1.9.5.1",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.performance=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(i,n){var o=null;window||r.canvas.createImage?(o=r.canvas&&r.canvas.createImage?r.canvas.createImage():new Image,e&&o.setAttribute("crossOrigin","Anonymous"),o.src=t,o.onload=function(){i({width:o.naturalWidth||o.width,height:o.naturalHeight||o.height,path:o,src:this.src})},o.onerror=function(t){n(t)}):n({fail:"getImageInfo fail",src:t})}))},this.options=t,Object.assign(this,t),this.ctx=(e=t.context,i={get:function(t,i){if("setFonts"===i)return function(t){var i=t.fontFamily,r=void 0===i?"sans-serif":i,o=t.fontSize,s=void 0===o?14:o,h=t.fontWeight,a=void 0===h?"normal":h,l=t.fontStyle,d=void 0===l?"normal":l;I==n.MP_TOUTIAO&&(a="bold"==a?"bold":"",d="italic"==d?"italic":""),e.font="".concat(d," ").concat(a," ").concat(Math.round(s),"px ").concat(r)};if(!e.draw||!e.setFillStyle){if("setFillStyle"===i)return function(t){e.fillStyle=t};if("setStrokeStyle"===i)return function(t){e.strokeStyle=t};if("setLineWidth"===i)return function(t){e.lineWidth=t};if("setLineCap"===i)return function(t){e.lineCap=t};if("setFontSize"===i)return function(t){e.font="".concat(String(t),"px sans-serif")};if("setGlobalAlpha"===i)return function(t){e.globalAlpha=t};if("setLineJoin"===i)return function(t){e.lineJoin=t};if("setTextAlign"===i)return function(t){e.textAlign=t};if("setMiterLimit"===i)return function(t){e.miterLimit=t};if("setShadow"===i)return function(t,i,n,r){e.shadowOffsetX=t,e.shadowOffsetY=i,e.shadowBlur=n,e.shadowColor=r};if("setTextBaseline"===i)return function(t){e.textBaseline=t};if("createCircularGradient"===i)return function(){};if("draw"===i)return function(){};if("function"==typeof e[i])return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e[i].apply(e,t)}}return t[i]},set:function(t,i,n){return e[i]=n,!0}},new Proxy(e,i)),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root;var o=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){o=t,r.lifecycle("onProgress",t/r.count)},get:function(){return o||0}})}return r.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},r.prototype.setContext=function(t){t&&(this.ctx=t)},r.prototype.init=function(){if(this.canvas.height||n.WEB==I){this.ctx.setTransform(1,0,0,1,0,0);var t=this.size.height*this.pixelRatio,e=this.size.width*this.pixelRatio;this.canvas.height=t,this.canvas.width=e,this.ctx.scale(this.pixelRatio,this.pixelRatio)}},r.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},r.prototype.clipPath=function(t,e,i,n,r,o,s){void 0===o&&(o=!1),void 0===s&&(s=!1);var h=this.ctx;if(/polygon/.test(r)){var a=r.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];h.beginPath(),a.map((function(r){var o=r.split(" "),s=o[0],h=o[1];return[k(s,i)+t,k(h,n)+e]})).forEach((function(t,e){0==e?h.moveTo(t[0],t[1]):h.lineTo(t[0],t[1])})),h.closePath(),s&&h.stroke(),o&&h.fill()}},r.prototype.roundRect=function(t,e,i,n,r,o,s){if(void 0===o&&(o=!1),void 0===s&&(s=!1),!(r<0)){var h=this.ctx;if(h.beginPath(),r){var a=r||{},l=a.borderTopLeftRadius,d=void 0===l?r||0:l,c=a.borderTopRightRadius,f=void 0===c?r||0:c,u=a.borderBottomRightRadius,p=void 0===u?r||0:u,g=a.borderBottomLeftRadius,v=void 0===g?r||0:g;h.arc(t+i-p,e+n-p,p,0,.5*Math.PI),h.lineTo(t+v,e+n),h.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),h.lineTo(t,e+d),h.arc(t+d,e+d,d,Math.PI,1.5*Math.PI),h.lineTo(t+i-f,e),h.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),h.lineTo(t+i,e+n-p)}else h.rect(t,e,i,n);h.closePath(),s&&h.stroke(),o&&h.fill()}},r.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=this.ctx,o=i||{},s=o.scaleX,h=void 0===s?1:s,a=o.scaleY,l=void 0===a?1:a,d=o.translateX,c=void 0===d?0:d,f=o.translateY,u=void 0===f?0:f,p=o.rotate,g=void 0===p?0:p,v=o.skewX,y=void 0===v?0:v,x=o.skewY,b=void 0===x?0:x,w=t.left,m=t.top,S=t.width,z=t.height;c=k(c,S)||0,u=k(u,z)||0;var I=k("0%",1),M=k("50%",1),B=k("100%",1),P={top:I,center:M,bottom:B},O={left:I,center:M,right:B};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=k(e,1)/(/px|rpx$/.test(e)?W(t.x)?z:S:1);return W(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return W(O[e])&&!W(t.x)?Object.assign(t,{x:O[e]}):Object.assign(t,{y:P[e]||.5})}),{}),(c||u)&&r.translate(c,u),(h||l)&&r.scale(h,l),g){var T=w+S*n.x,L=m+z*n.y;r.translate(T,L),r.rotate(g*Math.PI/180),r.translate(-T,-L)}(y||b)&&r.transform(1,Math.tan(b*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},r.prototype.setBackground=function(t,e,i,r,o){var s=this.ctx;t&&"transparent"!=t?T(t)?L(t,e,i,r,o,s):s.setFillStyle(t):[n.MP_TOUTIAO,n.MP_BAIDU].includes(I)?s.setFillStyle("rgba(0,0,0,0)"):s.setFillStyle("transparent")},r.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],s=i[2],h=i[3];n.setShadow(r,o,s,h)}},r.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,r=t.height,o=t.left,s=t.top,h=e.border,a=e.borderBottom,l=e.borderTop,d=e.borderRight,c=e.borderLeft,f=e.borderRadius,u=e.lineCap,p=h||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,x=p.borderColor,b=a||{},w=b.borderBottomWidth,m=void 0===w?v:w,S=b.borderBottomStyle,z=void 0===S?y:S,M=b.borderBottomColor,B=void 0===M?x:M,W=l||{},k=W.borderTopWidth,P=void 0===k?v:k,O=W.borderTopStyle,T=void 0===O?y:O,L=W.borderTopColor,R=void 0===L?x:L,F=d||{},A=F.borderRightWidth,j=void 0===A?v:A,E=F.borderRightStyle,H=void 0===E?y:E,C=F.borderRightColor,D=void 0===C?x:C,$=c||{},Y=$.borderLeftWidth,U=void 0===Y?v:Y,N=$.borderLeftStyle,X=void 0===N?y:N,_=$.borderLeftColor,q=void 0===_?x:_,G=f||{},V=G.borderTopLeftRadius,J=void 0===V?f||0:V,Q=G.borderTopRightRadius,Z=void 0===Q?f||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?f||0:K,et=G.borderBottomLeftRadius,it=void 0===et?f||0:et;if(a||c||l||d||h){var nt=function(t,e,n){"dashed"==e?/mp/.test(I)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},rt=function(t,e,n,r,o,s,h,a,l,d,c,f,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(f),nt(f,p,g),i.beginPath(),i.arc(t,e,h,Math.PI*l,Math.PI*d),i.lineTo(n,r),i.arc(o,s,a,Math.PI*d,Math.PI*c),i.stroke(),i.restore()};if(i.save(),h&&!a&&!c&&!l&&!d)return i.setLineWidth(v),nt(v,y,x),this.roundRect(o,s,n,r,f,!1,!!x),void i.restore();m&&rt(o+n-tt,s+r-tt,o+it,s+r,o+it,s+r-it,tt,it,.25,.5,.75,m,z,B,U&&j),U&&rt(o+it,s+r-it,o,s+J,o+J,s+J,it,J,.75,1,1.25,U,X,q,P&&m),P&&rt(o+J,s+J,o+n-Z,s,o+n-Z,s+Z,J,Z,1.25,1.5,1.75,P,T,R,U&&j),j&&rt(o+n-Z,s+Z,o+n,s+r-tt,o+n-tt,s+r-tt,Z,tt,1.75,2,.25,j,H,D,P&&m)}},r.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},r.prototype.drawPattern=function(t,n,r){return e(this,void 0,void 0,(function(){var e=this;return i(this,(function(i){return[2,new Promise((function(i,o){e.drawView(n,r,!0,!1,!0);var s=e,h=s.ctx;s.canvas;var a=n.width,l=n.height,d=n.left,c=n.top,f=r||{},u=f.borderRadius,p=void 0===u?0:u,g=f.backgroundImage,v=f.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var o=h.createPattern(t.src,y);h.setFillStyle(o),e.roundRect(d,c,a,l,p,!0,!1),e.setBorder(n,r),i()}(t)}))]}))}))},r.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,s=t.width,h=t.height,a=t.left,l=t.top,d=e||{},c=d.borderRadius,f=void 0===c?0:c,u=d.backgroundColor,p=void 0===u?"transparent":u,g=d.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),r&&(o.save(),this.setShadow(e)),i&&this.setBackground(p,s,h,a,l),e.clipPath?this.clipPath(a,l,s,h,e.clipPath,i,!1):this.roundRect(a,l,s,h,f,i,!1),r&&o.restore(),n&&this.setBorder(t,e),"hidden"==g&&o.clip()},r.prototype.drawImage=function(t,r,o,s){return void 0===r&&(r={}),void 0===o&&(o={}),void 0===s&&(s=!0),e(this,void 0,void 0,(function(){var h=this;return i(this,(function(a){switch(a.label){case 0:return[4,new Promise((function(a,l){return e(h,void 0,void 0,(function(){var e,h,l,d,c,f,u,p,g,v,y,x,b,w,m,S,z,M,B,W,T,L=this;return i(this,(function(i){return e=this.ctx,h=o.borderRadius,l=void 0===h?0:h,d=o.backgroundColor,c=void 0===d?"transparent":d,f=o.objectFit,u=void 0===f?"fill":f,p=o.backgroundSize,g=void 0===p?"fill":p,v=o.objectPosition,y=o.backgroundPosition,x=o.boxShadow,o.backgroundImage&&(u=g,v=y),x&&this.drawView(r,Object.assign(o,{backgroundColor:c||x&&(c||"#ffffff")}),!0,!1,!0),b=r.width,w=r.height,m=r.left,S=r.top,e.save(),z=r.contentSize.left-r.borderSize.left,M=r.contentSize.top-r.borderSize.top,s||(this.setOpacity(o),this.setTransform(r,o),this.setBackground(c,b,w,m,S),this.roundRect(m,S,b,w,l,!!(l||!x&&c),!1)),m+=z,S+=M,e.clip(),B=function(t){if("fill"!==u){var i=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,s=i.width/i.height,h=1,a="contain",l="cover";n==a&&o>=s||n==l&&o<s?h=e.height/i.height:(n==a&&o<s||n==l&&o>=s)&&(h=e.width/i.width);var d=i.width*h,c=i.height*h,f=r||[],u=f[0],p=f[1],g=O(u)?k(u,e.width):(e.width-d)*(P(u)?k(u,1):{left:0,center:.5,right:1}[u||"center"]),v=O(p)?k(p,e.height):(e.height-c)*(P(p)?k(p,1):{top:0,center:.5,bottom:1}[p||"center"]),y=function(t,e){return[(t-g)/h,(e-v)/h]},x=y(0,0),b=x[0],w=x[1],m=y(e.width,e.height),S=m[0],z=m[1],I=Math.max,M=Math.min;return{sx:I(b,0),sy:I(w,0),sw:M(S-b,i.width),sh:M(z-w,i.height),dx:I(g,0),dy:I(v,0),dw:M(d,e.width),dh:M(c,e.height)}}({objectFit:u,objectPosition:v},r.contentSize,t),o=i.sx,s=i.sy,h=i.sh,a=i.sw,l=i.dx,d=i.dy,c=i.dh,f=i.dw;I==n.MP_BAIDU?e.drawImage(t.src,l+m,d+S,f,c,o,s,a,h):e.drawImage(t.src,o,s,a,h,l+m,d+S,f,c)}else e.drawImage(t.src,m,S,b,w)},W=function(){e.restore(),L.drawView(r,o,!1,!0,!1),a(1)},T=function(t){B(t),W()},T(t),[2]}))}))}))];case 1:return a.sent(),[2]}}))}))},r.prototype.drawText=function(t,e,i,n){var r=this,o=this.ctx,s=e.borderSize,h=e.contentSize,a=e.left,l=e.top,d=h.width,c=h.height,f=h.left-s.left||0,u=h.top-s.top||0,p=i.color,g=i.lineHeight,v=i.fontSize,y=i.fontWeight,x=i.fontFamily,b=i.fontStyle,w=i.textIndent,m=void 0===w?0:w,S=i.textAlign,z=i.textStroke,I=i.verticalAlign,M=void 0===I?mt:I,B=i.backgroundColor,P=i.lineClamp,O=i.backgroundClip,T=i.textShadow,L=i.textDecoration;if(m=W(m)?m:0,this.drawView(e,i,O!=yt),g=k(g,v),t){o.save(),a+=f,l+=u;var R=n.fontHeight,F=n.descent,A=void 0===F?0:F,j=n.ascent,E=A+(void 0===j?0:j);switch(o.setFonts({fontFamily:x,fontSize:v,fontWeight:y,fontStyle:b}),o.setTextBaseline(mt),o.setTextAlign(S),O?this.setBackground(B,d,c,a,l):o.setFillStyle(p),S){case zt:break;case It:a+=.5*d;break;case Mt:a+=d}var H=n.lines*g,C=Math.ceil((c-H)/2);switch(C<0&&(C=0),M){case wt:break;case mt:l+=C;break;case St:l+=2*C}var D=(g-R)/2,$=g/2,Y=function(t){var e=o.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,r=e.actualBoundingBoxAscent;return M==wt?{fix:E?void 0===r?0:r:$-D/2,lineY:E?0:D-D/2}:M==mt?{fix:E?$+n/4:$,lineY:E?0:D}:M==St?{fix:E?g-n:$+D/2,lineY:E?2*D:D+D/2}:{fix:0,height:0,lineY:0}},U=function(t,e,i){var r=t;switch(S){case zt:r+=i;break;case It:r=(t-=i/2)+i;break;case Mt:r=t,t-=i}if(L){o.setLineWidth(v/13),o.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(L)&&(o.moveTo(t,e+n.fontHeight+s),o.lineTo(r,e+n.fontHeight+s)),/\boverline\b/.test(L)&&(o.moveTo(t,e-s),o.lineTo(r,e-s)),/\bline-through\b/.test(L)&&(o.moveTo(t,e+.5*n.fontHeight),o.lineTo(r,e+.5*n.fontHeight)),o.closePath(),o.setStrokeStyle(p),o.stroke()}},N=function(t,e,i){var n=function(){o.setLineWidth(z.width),o.setStrokeStyle(z.color),o.strokeText(t,e,i)},s="outset";z&&z.type!==s?(o.save(),r.setShadow({boxShadow:T}),o.fillText(t,e,i),o.restore(),n()):z&&z.type==s?(o.save(),r.setShadow({boxShadow:T}),n(),o.restore(),o.save(),o.fillText(t,e,i),o.restore()):(r.setShadow({boxShadow:T}),o.fillText(t,e,i))};if(!n.widths||1==n.widths.length&&n.widths[0].total+m<=h.width){var X=Y(t),_=X.fix,q=void 0===_?0:_,G=X.lineY;return N(t,a+m,l+q),U(a+m,l+G,n&&n.widths&&n.widths[0].total||n.text),l+=g,o.restore(),void this.setBorder(e,i)}for(var V=l,J=a,Q="",Z=0,K=o.measureText("...").width,tt=n.widths,et=0;et<tt.length;et++){var it=tt[et].widths,nt=0;Q="",l+=1==(Z+=1)?0:g,1==Z&&m&&(nt=m,J=a+m);for(var rt=0;rt<it.length;rt++){1!==Z&&m&&(J=a);var ot=it[rt],st=ot.width,ht=ot.text,at=(it[rt+1]||{}).width;if(Q+=ht,(nt+=st)+(void 0===at?0:at)+(0==Z?m:0)+(Z==P?K:0)>h.width){Z>=P&&(Q+="…"),Z++,nt=0;var lt=Y(Q);q=lt.fix,G=lt.lineY;N(Q,J,l+q),U(J,l+G,nt),l+=g,Q=""}else if(rt==it.length-1){et!=tt.length-1&&Z==P&&K+nt<h.width&&(Q+="…");var dt=Y(Q);q=dt.fix,G=dt.lineY;N(Q,J,l+q),U(J,l+G,nt)}if(l>V+c||Z>P)break}}o.restore()}},r.prototype.source=function(t){return e(this,void 0,void 0,(function(){var e,n,r,o,s=this;return i(this,(function(i){switch(i.label){case 0:if(this.node=null,e=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(n in t.type=bt,t.styles=t.styles||t.css||{},t)["views","children","type","css","styles"].includes(n)||(t.styles[n]=t[n],delete t[n]);return t.styles.boxSizing||(t.styles.boxSizing="border-box"),[4,this.create(t)];case 1:return(r=i.sent())?(o=r.layout()||{},this.size=o,this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),this.performance&&console.log("布局用时："+(+new Date-e)+"ms"),[2,this.size]):[2,console.warn("no node")]}}))}))},r.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},r.prototype.create=function(n,r){return e(this,void 0,void 0,(function(){function e(i,n,r){void 0===n&&(n={}),void 0===r&&(r=!0);var o=[];return i.forEach((function(i){var s=i.styles,h=void 0===s?{}:s,a=i.children,l=void 0===a?[]:a,d=i.text,c=void 0===d?"":d,f=i.type,u=void 0===f?"":f,p={};p=t(r?t({},n):{},h);var g={},v={},y={};Object.keys(p).map((function(t){if(t.includes("padding")||t.includes("margin")){var e=J(t,p[t]);Object.keys(e).map((function(t){t.includes("Left")?v[t]=e[t]:t.includes("Right")?y[t]=e[t]:g[t]=e[t]}))}}));if(p.textIndent&&(v.textIndent=p.textIndent,delete n.textIndent),""!==c){var x=Array.from(c);x.forEach((function(t,e){var i=Object.assign({},p,g);0===e?Object.assign(i,v):e==x.length-1&&Object.assign(i,y),delete i.padding,delete i.margin,o.push({type:"text",text:t,styles:i})}))}if(u==vt||u==xt)o.push(i);else if("block"===h.display&&l.length>0){var b=e(l,p,!1);i.children=b,i.flattened=!0,o.push(i)}else if(l.length>0){b=e(l,p,r);o=o.concat(b)}})),o}var o,s,h,a,l,d,c,f,u,p,g,v,y,b,w,m,S,z,I,M,B;return i(this,(function(i){switch(i.label){case 0:if(!n)return[2];if(n.styles||(n.styles=n.css||{}),o=n.type,s=o==vt,h=[yt,xt].includes(o),a="textBox"==o,l=n.styles||{},d=l.backgroundImage,c=l.display,s&&!n.src&&!n.url)return[2];if(c==x)return[2];if(h||a){if(f=n.children,!n.text&&(!f||f&&!f.length))return[2];f&&f.length&&!n.flattened&&(u=e(n.children),n.type="view",n.children=u)}if(!(s||n.type==bt&&d))return[3,4];p=s?n.src:"",g=/url\(['"]?(.*?)['"]?\)/.exec(d),d&&g&&g[1]&&(p=g[1]||""),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.getImageInfo(p)];case 2:return v=i.sent(),y=v.width,b=v.height,!(w=v.path)&&s?[2]:(w&&(n.attributes=Object.assign(n.attributes||{},{width:y,height:b,path:w,src:w,naturalSrc:p})),[3,4]);case 3:return m=i.sent(),n.type!=bt?[2]:(this.lifecycle("onEffectFail",t(t({},m),{src:p})),[3,4]);case 4:if(this.count+=1,S=new gt(n,r,this.root,this.ctx),!(z=n.views||n.children))return[3,8];I=0,i.label=5;case 5:return I<z.length?(M=z[I],[4,this.create(M,S)]):[3,8];case 6:(B=i.sent())&&S.add(B),i.label=7;case 7:return I++,[3,5];case 8:return[2,S]}}))}))},r.prototype.drawNode=function(t,n){return void 0===n&&(n=!1),e(this,void 0,void 0,(function(){var e,r,o,s,h,a,l,d,c,f,u,p,g,v,y,x,b,w,m,S,z,I,M;return i(this,(function(i){switch(i.label){case 0:return e=t.layoutBox,r=t.computedStyle,o=t.attributes,s=t.name,h=t.children,a=t.fixedLine,l=t.attributes,d=l.src,c=l.text,f=r.position,u=r.backgroundImage,p=r.backgroundRepeat,["fixed"].includes(f)&&!n?[2]:(this.ctx.save(),s!==bt?[3,7]:d&&u?p?[4,this.drawPattern(o,e,r)]:[3,2]:[3,5]);case 1:return i.sent(),[3,4];case 2:return[4,this.drawImage(o,e,r,!1)];case 3:i.sent(),i.label=4;case 4:return[3,6];case 5:this.drawView(e,r),i.label=6;case 6:return[3,10];case 7:return s===vt&&d?[4,this.drawImage(o,e,r,!1)]:[3,9];case 8:return i.sent(),[3,10];case 9:s===yt?this.drawText(c,e,r,o):s===xt&&QR.api&&console.warn("single"),i.label=10;case 10:if(this.progress+=1,v=(g=a||{}).beforeElements,y=g.afterElements,!v)return[3,14];x=0,b=v,i.label=11;case 11:return x<b.length?(M=b[x],[4,this.drawNode(M)]):[3,14];case 12:i.sent(),i.label=13;case 13:return x++,[3,11];case 14:if(!h)return[3,18];w=Object.values?Object.values(h):Object.keys(h).map((function(t){return h[t]})),m=0,S=w,i.label=15;case 15:return m<S.length?"absolute"===(M=S[m]).computedStyle.position?[3,17]:[4,this.drawNode(M)]:[3,18];case 16:i.sent(),i.label=17;case 17:return m++,[3,15];case 18:if(!y)return[3,22];z=0,I=y,i.label=19;case 19:return z<I.length?(M=I[z],[4,this.drawNode(M)]):[3,22];case 20:i.sent(),i.label=21;case 21:return z++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},r.prototype.render=function(t){var n=this;return void 0===t&&(t=30),new Promise((function(r,o){return e(n,void 0,void 0,(function(){var e,n,s,h,a,l,d,c,f,u;return i(this,(function(i){switch(i.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:i.sent(),i.label=2;case 2:if(i.trys.push([2,14,,15]),!this.node)return[3,12];if(n=this.root.fixedLine||{},s=n.beforeElements,h=n.afterElements,!s)return[3,6];a=0,l=s,i.label=3;case 3:return a<l.length?(f=l[a],[4,this.drawNode(f,!0)]):[3,6];case 4:i.sent(),i.label=5;case 5:return a++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(i.sent(),!h)return[3,11];d=0,c=h,i.label=8;case 8:return d<c.length?(f=c[d],[4,this.drawNode(f,!0)]):[3,11];case 9:i.sent(),i.label=10;case 10:return d++,[3,8];case 11:return r(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),i.label=13;case 13:return[3,15];case 14:return u=i.sent(),this.lifecycle("onEffectFail",u),o(u),[3,15];case 15:return this.performance&&console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},r.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},r.prototype.destroy=function(){this.node=[]},r.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/".concat(n),o)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},r}();n.WEB==I&&(window.Painter=Bt);export{Bt as Painter,Bt as default};
