<template>
	<view class="detail_all">
		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="background" title="详情"
				:titleStyle="{ color: '#000000', fontSize: '34rpx', fontWeight: 'bold' }"></u-navbar>
		</view>
		<view v-if="!topShow" class="third-top flex align-items"
			style="padding: 30rpx;position: fixed;top: 175rpx;width: 100%;z-index: 10000;">
			<view class="tab-item" :class="{ 'tab-active': checkTab === 0 }" @click="switchTab(0)">活动详情
			</view>
			<view class="tab-item" :class="{ 'tab-active': checkTab === 1 }" @click="switchTab(1)"
				style="margin-left: 50rpx;">名单</view>
			<view class="tab-underline" :style="{ transform: `translateX(${checkTab * 160}rpx)` }"></view>
		</view>
		<view class="box flex justify-start flex-column align-items" :class="overlay ? 'no-scroll' : ''">
			<view class="swiper">
				<u-swiper :list="detail.images" indicator indicatorInactiveColor="#D8D8D8"
					indicatorActiveColor="#323232" indicatorMode="dot"
					:indicator-style="{ bottom: '60rpx', zIndex: 999 }" :height="height_sw" circular
					@click="swiperImg"></u-swiper>
			</view>

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">
				<image src="/static/detail/conbg.png" class="topimgs"></image>
				<image src="/static/detail/rmb.png" class="topimgs_rmb"></image>

				<view class="top_texts flex align-items" v-if="detail.feel == 0">
					<span style="font-weight: 900;font-size: 56rpx;">{{ detail.price.split('.')[0] }}</span>
					<view class="flex flex-column xiao">
						<span>.{{ detail.price.split('.')[1] }}</span>
						<span>元/人</span>
					</view>
				</view>
				<view class="top_texts flex align-items" v-if="detail.feel == 1 && detail.offline==2" style="top:-140rpx;right: 32rpx;">
					<span style="font-weight: 900;font-size: 44rpx;">免费</span>
				</view>
				<view class="top_texts flex align-items" v-if="detail.offline==1" style="top:-140rpx;right: 32rpx;">
					<span style="font-weight: 900;font-size: 42rpx;">线下活动</span>
				</view>
				<view class="first-box flex flex-start flex-column justify-start">
					<view class="flex align-items">
						<span class="first-name">{{ detail.title }}</span>
					</view>



					<view class="first-image flex align-items" style="margin-top: 25rpx;width: 100%;">
						<image :src="detail.user.avatar" mode=""
							style="width: 36rpx;height: 36rpx;margin-right: 12rpx;border-radius: 80rpx;">
						</image>
						<view style="color: #9C9C9C ;width: auto;margin-right: 20rpx;">{{ detail.user.nickname }}</view>
						<view class="flex justify-center align-items xieyi">
							<image src="/static/detail/baohu.png" mode=""
								style="width: 28rpx;height: 28rpx;margin-right: 0;"></image>
							<view style="text-align: center;color: #323232;padding: 0rpx 10rpx;">{{
								detail.refund_info.title }}</view>
							<!-- 	<image src="/static/detail/zidong.png" mode=""
								style="width: 20rpx;height: 20rpx;margin-right: 0;"></image> -->
						</view>
					</view>

					<view class="first-image flex align-items" style="margin-top: 30rpx;">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<span>{{ formattedTime.formattedTime }}</span>
					</view>

					<span class="line"></span>

					<view class="first-image flex align-items">
						<image src="/static/center/address.png" mode="" class="icon-size"></image>
						<text style="font-weight: 400;width: 460rpx;padding-right: 40rpx;color: #323232;font-size: 30rpx;">{{ detail.address
						}}{{ detail.address_detail }}</text>
						<image @click.stop="toPhone()" src="/static/detail/phone.png" class="icon-size"
							style="width: 50rpx; height: 50rpx;margin-right: 35rpx;">
						</image>
						<image @click.stop="toMap(detail.latitude, detail.longitude, detail.address_detail)"
							src="/static/detail/daohang.png" class="icon-size" style="width: 50rpx; height: 50rpx;">
						</image>
					</view>
					<!-- <span class="line" style="margin-bottom: 0rpx;"></span>
					<view class="second-box flex align-items space-between" v-if="detail.join_info">
						<view class="flex align-items" v-if="detail.join_info.users">
							<u-avatar-group :urls="detail.join_info.users" keyName="avatar" size="30" gap="0.3"
								:maxCount="4"></u-avatar-group>
							<image src="/static/index/dian.png"
								:class="detail.join_info.users && detail.join_info.users.length > 0 ? '' : 'smalld'"
								style="width: 60rpx;height: 60rpx;margin-left:-20rpx;z-index: 1;"></image>
							<span class="number">{{ detail.join_info.people_number }}/{{ detail.join_info.stock >= 10000 ?
									'9999+' : detail.join_info.stock}}
								人数</span>
						</view>
						<view class="value_slide">
							<u-slider v-model="value_slide" showValue min="0" max="100" blockSize="12"
								inactiveColor="#EBEBEB" activeColor="#BBFC5B" disabled></u-slider>
						</view>
					</view> -->
				</view>


			</view>
			<view v-if="detail.image != '' && detail.image != null"
				style="border-radius: 44rpx;width: 100%;position: relative;margin: 20rpx 0rpx 20rpx 0rpx;background-color: #ffffff;">
				<view style="display: flex;justify-content: space-between;align-items: center;padding:20rpx 30rpx;">
					<view style="display: flex;align-items: center;">
						<view>
							<image src="/static/detail/wx.png" style="width: 100rpx;height: 100rpx;"></image>
						</view>
						<view style="margin-left: 20rpx;">
							<view style="font-size: 28rpx;color: #3D3D3D;">群二维码</view>
							<view style="margin-top: 10rpx;font-size: 24rpx;color: #9C9C9C;">
								上传时间：{{ formatTimestamp(detail.updatetime) }}</view>
						</view>
					</view>
					<view style="display: flex;align-items: center;">
						<view class="ck-qrcode" @click="qunShow = true">
							查看
						</view>
					</view>
				</view>
			</view>
			<view id="third" style="background-color: #ffffff;border-radius: 44rpx;overflow: hidden;width: 100%;"
				:style="detail.image == '' || detail.image == null?'margin-top: 20rpx':''">
				<view v-if="topShow" class="third-top flex align-items" style="padding: 30rpx;">
					<view class="tab-item" :class="{ 'tab-active': checkTab === 0 }" @click="switchTab(0)">活动详情
					</view>
					<view class="tab-item" :class="{ 'tab-active': checkTab === 1 }" @click="switchTab(1)"
						style="margin-left: 50rpx;">名单</view>
					<view class="tab-underline" :style="{ transform: `translateX(${checkTab * 160}rpx)` }"></view>
				</view>
				<view class="third flex flex-column" style="justify-content: flex-start;">
					<view class="third-center1" ref="richTextContainer" v-if="!showToggleButtons" style="height: auto;">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="detail.content"></rich-text>
						</view>
					</view>
					<view class="third-center" v-if="showToggleButtons"
						:style="{ height: richTextShow ? 'auto' : '300px', overflow: 'hidden', margin: '0 auto', paddingBottom: '0' }"
						ref="richTextContainer">
						<view class="v_html">
							<rich-text style="white-space: pre-line;" :nodes="detail.content"></rich-text>
						</view>
						<!-- 仅当内容高度超过容器高度时显示展开/收起按钮 -->
						<view v-if="!richTextShow" @click="richTextShow = true"
							style="padding: 30rpx 0;position: sticky; bottom: 0;background: #fff">
							<view style=" display: flex; align-items: center; justify-content: center;margin: 0 auto;
							 background: #fff;border-radius: 200rpx;width: 288rpx;height: 80rpx;
							border: 1px solid #ff4810; color: #ff4810;font-size: 32rpx;line-height: 45rpx;">
								查看完整内容
								<!-- <u-icon name="arrow-down" color="#323232"></u-icon> -->
							</view>

						</view>
						<view v-if="richTextShow" @click="richTextShow = false"
							style="position: sticky; bottom: -16px; background: #fff; padding: 30rpx 0;">
							<view style="width: 100%; display: flex; align-items: center; justify-content: center;margin: 0 auto;
						background: #fff;border-radius: 200rpx;width: 200rpx;height: 80rpx;
						border: 1px solid #ff4810; color: #ff4810;font-size: 32rpx;line-height: 45rpx;">
								收起
								<!-- <u-icon name="arrow-up" color="#323232"></u-icon> -->
							</view>

						</view>
						<view v-if="!richTextShow"
							style="position: absolute;bottom: 140rpx;width: 100%;height: 100px;background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 80%);">
						</view>
					</view>

					<!--大图排列-->
					<!-- <view class="third-center" style="padding-top: 0;">
					<image mode="widthFix" class="imgs" v-for="(item, index) in detail.images" :key="index" :src="item">
					</image>
				</view> -->
					<!--9宫格排列-->
					<view class=""
						style="display: flex;justify-content: flex-start;flex-wrap: wrap;width: 690rpx;margin: 0 auto;gap: 15rpx;">
						<view class="imgs" v-for="(item, index) in detail.images" :key="index">
							<image mode="aspectFill" style="width: 220rpx;height: 220rpx;border-radius: 10rpx;"
								:src="item" @click="swiperImg(index)">
							</image>
						</view>
					</view>


				</view>
			</view>
			<!-- 报名信息 -->
			<view id="fourth" class="fourth flex flex-column"
				style="justify-content: flex-start;margin-bottom: 180rpx;padding-bottom: 40rpx;">
				<view class="flex align-items" style="padding: 30rpx;justify-content: space-between;">
					<view class="fourth-top flex align-items" style="margin: 0px;">
						<span>报名信息
							<image class="icons" src="/static/detail/xiangqing.png"></image>
						</span>
					</view>

				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;padding:0px 30rpx;">
					<view style="color: #9C9C9C;font-size: 28rpx;">
						<text>活动人数{{ detail.stock }}</text>
						<text style="margin-left: 30rpx;">已报{{ peopleNum }}人</text>
					</view>
					<view style="display: flex;align-items: center;" @click="openSer()">
						<view v-if="peopleParams.order == 'normal'" style="font-size: 28rpx;">默认排序</view>
						<view v-if="peopleParams.order == 'earliest'" style="font-size: 28rpx;">最早报名</view>
						<view v-if="peopleParams.order == 'new'" style="font-size: 28rpx;">最新报名</view>
						<view v-if="peopleParams.order == 'recently'" style="font-size: 28rpx;">最近报名</view>
						<view>
							<image src="https://naweigetetest2.hschool.com.cn/uniapp_image/signSort.png" mode=""
								style="width: 24rpx;height: 24rpx;margin-left: 15rpx;"></image>
						</view>
					</view>
				</view>

				<view v-for="(item, index) in signPeopleList" style="padding:30rpx 0rpx 0rpx 30rpx;">
					<view :style="{ 'paddingTop': index == 0 ? '20rpx' : '0' }">
						<view style="display: flex;align-items: center;justify-content: space-between;">
							<view>
								<view style="font-size: 32rpx;color: #3D3D3D;font-weight: 600;">
									<text>{{ (index + 1) < 10 ? '0' + (index + 1) : index + 1 }}. {{ item.name }}</text>
								</view>
								<view style="color: #9C9C9C;font-size: 30rpx;margin-top: 20rpx;padding-left: 45rpx;">
									<text>{{ formatCreateTime(item.createtime) }}</text>
									<text style="margin: 0rpx 15rpx;">·</text>
									<text>报名号{{ item.code }}</text>
									<text style="margin: 0rpx 15rpx;">·</text>
									<text v-if="item.open == 1">公开</text>
									<text v-if="item.open == 0">不公开</text>
								</view>
							</view>
						</view>
					</view>
					<view v-if="signPeopleList.length > index + 1"
						style="height: 1px;background-color: #F0F0F0;width: 93%;margin-top: 30rpx;"></view>
				</view>

				<!-- <view v-for="(item, index) in signPeopleList" @click="chickTeam(item, index)"
					style="padding:0rpx 30rpx;">
					<view style="padding-top: 40rpx;">
						<view style="display: flex;align-items: center;justify-content: space-between;">
							<view>
								<view style="font-size: 28rpx;color: #323232;">{{ item.name }} {{ item.mobile }}</view>
								<view style="color: #9C9C9C;font-size: 24rpx;margin-top: 10rpx;">身份证 {{ item.idnum }}
								</view>
							</view>
						</view>
					</view>
					<view style="height: 1px;background-color: #F0F0F0;width: 100%;margin-top: 40rpx;"></view>
				</view> -->
			</view>

			<view style="width: 100%;height: 100rpx;"></view>

			<view class="footer flex align-items" style="justify-content: space-between;" v-if="detail.status == 4">
				<view class="btn_1" style="background-color: #E2E2E2;color: #999999;">报名已结束</view>
			</view>
			<view class="footer flex align-items" style="justify-content: space-between;" v-if="detail.status == 5">
				<view class="btn_1" style="background-color: #E2E2E2;color: #999999;">活动已结束</view>
			</view>
			<view class="footer flex align-items" style="justify-content: space-between;" v-if="detail.status == 2">
				<view class="footer-left">
					<!-- <view @click="callPhone(detail.user.mobile)">
						<image src="../../static/center/phone.png" mode=""></image>
						<span>电话</span>
					</view> -->
					<view @click="overlayShow()">
						<!-- <view @click="Share()"> -->
						<image src="@/static/detail/fenxiang.png" mode=""></image>
						<!-- <span>分享</span> -->
					</view>
					<!-- <view v-if="is_collect == 0" @click="Collect(1)">
						<image src="../../static/center/Collect.png" mode=""></image>
						<span>收藏</span>
					</view>
					<view v-if="is_collect != 0" @click="Collect(0)">
						<image src="../../static/center/Collected.png" mode=""></image>
						<span>已收藏</span>
					</view> -->
				</view>
				<view class="footer-right flex justify-center align-items" @click="sign()">
					<span v-if="type == 0 && detail.feel == 0"> 支付并参加 </span>
					<span v-if="type == 0 && detail.feel == 1"> 免费参加 </span>
					<!-- <span v-if="type == 1 && detail.feel == 0" @click="buy()"> 立即支付 </span>
					<span v-if="type == 1 && detail.feel == 1" @click="buy()"> 确认报名 </span>
					<span v-if="type == 2" @click="open()"> 我要预约 </span>
					<span v-if="type == 3" @click="confimTime()"> 确认时间 </span> -->
					<!-- <image src="../../static/center/signUp.png" mode="" v-if="type == 0" @click="sign()"></image>
				<image src="../../static/center/appointment.png" mode="" v-else-if="type == 1" @click="open()"></image>
				<image src="../../static/center/Confirm.png" mode="" v-else="type == 2" @click="confimTime()"></image> -->
				</view>
			</view>



			<!-- 课程时间 -->
			<u-popup :show="show" mode="bottom" :round="10" :zIndex="99999" :custom-style="popupStyle" @close="close"
				@open="open">
				<view class="popupBox flex justify-start align-items flex-column">
					<view class="pop-header flex align-items" style="justify-content: space-between;">
						<image src="../../static/center/classTime.png" mode="" style="width: 252rpx; height: 48rpx;">
						</image>
						<span @click="cancel">取消选择</span>
					</view>
					<!-- <span class="line"></span> -->
					<view class="times flex align-items justify-center" style="flex-wrap: wrap;">
						<span class="selectTime flex justify-center align-items" v-for="(item, index) in timeList"
							:key="index" :class="{ selected: timeSelected(item) }" @click="selectTime(item)">
							{{ item.name }}</span>
					</view>
				</view>

			</u-popup>

			<!-- 报名人的列表展示类型 -->
			<u-popup :show="sortTypeShow" mode="bottom" :round="10" :zIndex="99999" :custom-style="popupStyle"
				@close="sortTypeShow == false" @open="sortTypeShow == true">
				<view class="popupBox flex justify-start align-items flex-column">
					<!-- <view class="pop-header flex align-items" style="justify-content: space-between;">
						<image src="../../static/center/classTime.png" mode="" style="width: 252rpx; height: 48rpx;">
						</image>
						<span @click="cancel">取消选择</span>
					</view> -->
					<!-- <span class="line"></span> -->
					<!-- <view class="times flex align-items justify-center" style="flex-wrap: wrap;">
						<span class="selectTime flex justify-center align-items" v-for="(item, index) in timeList"
							:key="index" :class="{ selected: timeSelected(item) }" @click="selectTime(item)">
							{{ item.name }}</span>
					</view> -->
				</view>

			</u-popup>

			<!-- 购买弹窗 -->
			<u-popup :show="buyShow" mode="center" :round="10" :zIndex="99999" :custom-style="popupStyle"
				@close="buyClose" @open="buyOpen" :safeAreaInsetBottom="false" :closeable="true">
				<view class="popupBox flex justify-start align-items flex-column">
					<view class="pop-header flex align-items flex-column flex-start">
						<span class="name white-space">{{ detail.title }}</span>
						<span class="price">
							<span v-if="detail.feel == 0">￥{{ detail.price }}</span>
							<span v-if="detail.feel == 1">公益</span>
						</span>
						<!-- <image src="../../static/center/buy.png" mode="" style="width: 168rpx; height: 48rpx;">
					</image> -->
					</view>
					<view class="popup flex-column">
						<span class="first-image flex align-items" style="margin: 16rpx 0;">
							<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
							<span>{{ formattedTime.formattedTime }} (共计{{ formattedTimeList.length }}节)</span>
						</span>
						<span class="first-image flex align-items">
							<image src="../../static/center/address.png" mode="" class="icon-size"></image>
							<span>{{ detail.address_detail }}</span>
						</span>
						<!-- <image :src="detail.headimage" mode="" style="width: 200rpx; height: 140rpx;"></image>
					<view class="popur-right flex flex-column">
						<span class="name white-space">{{detail.title}}</span>
						<span class="address">地址:{{detail.address_detail}}</span>
						<span class="date">开始时间:{{detail.start_time_text}}</span>
						<span class="time">结束时间:{{detail.end_time_text}}</span>
						<span class="line-row"></span>
						<span class="price">
							课程价格:
							<span v-if="detail.feel == 0">￥{{detail.price}}</span>
							<span v-if="detail.feel == 1">免费</span>
						</span>
					</view> -->
					</view>
					<view class="popup-footer flex " @click="buy()">
						<span v-if="type == 1 && detail.feel == 0">立 即 支 付</span>
						<span v-if="type == 1 && detail.feel == 1">确 认 报 名</span>
						<!-- <image src="../../static/center/price.png" mode="" style="width: 642rpx;height: 80rpx;"></image> -->
						<u-loading-icon :vertical="true" v-if="uloadingShow"></u-loading-icon>
					</view>
				</view>
			</u-popup>
			<u-overlay zIndex="5555" :show="overlay"></u-overlay>
			<!-- 分享海报 -->
			<view v-if="overlay" class="pos">

				<image @click="closeoo" src="/static/center/close.png" mode=""
					style="z-index: 10000;width: 64rpx;height: 64rpx;position: absolute;top: 50rpx;right: 45rpx;">
				</image>

				<l-painter isCanvasToTempFilePath :after-delay="500" @success="sunccessimg"
					css="width:661rpx;height: 1072rpx;background-image:url(https://naweigetetest2.hschool.com.cn/dyqc/fenxiang.png);"
					custom-style="position:absolute;left:45rpx;right:44rpx;top:50rpx;z-index:100">
					<l-painter-image :src="detail.user.avatar"
						css="z-index:300;margin-left: 15rpx; margin-top: 120rpx;border: 2rpx solid #FFFFFF; width: 60rpx;  height: 60rpx; border-radius: 50%;" />
					<l-painter-view css="margin-top: 130rpx; padding-left: 20rpx; display: inline-block">
						<l-painter-text :text="detail.user.nickname + '的邀请'"
							css="display: block; height: 36rpx;color: #3D3D3D; font-size: 28rpx; fontWeight: 600;" />
					</l-painter-view>
					<l-painter-view
						css="margin-left: 20rpx; margin-top: 20rpx; margin-bottom: 20rpx; box-sizing: border-box;width: 100%;">
						<l-painter-image :src="detail.images[0]"
							css="width: 620rpx; height: 620rpx; border-radius: 24rpx;object-fit: cover;" />

						<l-painter-view
							css="margin-top: 30rpx;display: flex;justify-content: space-between;align-items: center;width:100%;">
							<l-painter-view css="display: flex;flex-direction: column;width: 420rpx;">
								<l-painter-text
									css="line-clamp: 1;font-weight: bold;color:#202020;font-size: 32rpx;width:400rpx;box-sizing:border-box;line-height: 42rpx;"
									:text="detail.title"></l-painter-text>
								<l-painter-text
									css="line-clamp: 1; color:#FF4810;font-size: 35rpx;width:220rpx;margin-top: 20rpx;line-height: 38rpx;fontWeight: 600;"
									:text="'￥' + detail.price"></l-painter-text>
								<l-painter-text
									css="line-clamp: 2; color:#9C9C9C;font-size: 26rpx;margin-top: 20rpx;line-height: 36rpx;width: 360rpx;"
									:text="'地址：' + detail.address"></l-painter-text>
							</l-painter-view>
							<l-painter-view
								css="background: #ffffff;border-radius: 18rpx;width: 200rpx; height: 200rpx;margin-right: 50rpx;">
								<l-painter-qrcode css="width: 160rpx; height: 160rpx;margin:20rpx;"
									:text="qrUrl"></l-painter-qrcode>
							</l-painter-view>

						</l-painter-view>
					</l-painter-view>
				</l-painter>

				<view class="btnList">
					<button open-type="share" class="no-border-button" plain="true">
						<image src="/static/detail/savewec.png" mode=""
							style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						微信
					</button>
					<!-- <view class="save"  @click.stop="sharePoster()">
						<image src="/static/detail/pyq.png" mode="" style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						朋友圈
					</view> -->
					<view class="save" @click="save()">
						<image src="/static/detail/donwload.png" mode=""
							style="width: 88rpx;height: 88rpx;margin-bottom: 15rpx;"></image>
						保存图片
					</view>


				</view>
				<!-- <canvas canvas-id="myCanvas"
					style="position: absolute;top:256rpx;left: 48rpx; width: 661rpx;height:1075rpx; visibility: hidden;z-index: 10;">
				</canvas> -->
			</view>

		</view>
		<u-popup @touchmove.native.stop.prevent :closeable="false" :show="qunShow" :round="10" mode="bottom">
			<view style="text-align: center;font-size: 32rpx;color: #3D3D3D;padding: 30rpx;font-weight: 600;">活动二维码
			</view>
			<view style="text-align: center;">
				<image :show-menu-by-longpress="true" :src="detail.image"
					style="width: 400rpx;border: 1rpx solid #D5FD99;" mode="widthFix"></image>
			</view>
			<view style="padding: 0rpx 40rpx;">
				<view style="font-size: 28rpx;font-weight: 300;color: #9C9C9C;text-align: center;margin-top: 20rpx;">
					长按识别二维码进群</view>
				<view style="font-size: 28rpx;color: #3D3D3D;text-align: center;margin-top: 30rpx;">
					如果无法加入或者开启了群验证，可能是成员已满您即将加入由用户自发组织的户外活动，请知悉</view>
				<view style="font-size: 28rpx;color: #0CA013;text-align: center;margin-top: 30rpx;">
					确认您已知晓《用户协议》的用户义务与责任平台不对活动真实性作担保，请入群自行辨别</view>
			</view>
			<view @click="qunShow = false" class="btn_1">我已知晓</view>
		</u-popup>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import {
		dateWeek,
		dateWeekend
	} from '../../utils/dateFormat'
	export default {
		computed: {
			// formattedTitle() {
			// 	if (this.detail.title.length > 9) {
			// 		return this.detail.title.slice(0, 9) + '..';
			// 	}
			// 	return this.detail.title;
			// },
			formattedTimeList() {
				return this.timeList.map(item => {
					const startTime = dayjs.unix(item.start_time).format('YYYY-MM-DD HH:mm:ss');
					const endTime = dayjs.unix(item.end_time).format('HH:mm:ss');
					return {
						formattedTime: `${startTime}~${endTime}`,
						limit_num: item.limit_num,
						sign_num: item.sign_num
					};
				});
			},
			formattedTime() {
				const startTime = dateWeek(this.detail.start_time);
				const endTime = dateWeekend(this.detail.end_time);
				return {
					formattedTime: `${startTime} - ${endTime}`
				};
			}
		},

		data() {
			return {
				topShow: true,
				lastScrollTop: 0, // 记录上次滚动位置，用于防抖动
				checkTab: 0,
				checkSortType: 0,
				sortTypeShow: false,
				signPeopleList: [], //报名人信息列表
				peopleNum: 0,
				peopleParams: {
					page: 1,
					limit: 15,
					order: 'normal',
					status: '2,3,4,9'
				},
				richTextShow: false,
				showToggleButtons: false, // 控制是否显示展开/收起按钮
				qunShow: false,
				height_sw: '580rpx',
				style: {
					// 字符串的形式
					img: 'width: 100%'

				},
				isShare: '',
				value_slide: 0,
				scrollTop: 0,
				overlay: false,
				userInfo: {},
				path: '',
				uloadingShow: false,
				show: false,
				buyShow: false,
				type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
				id: 1,
				count: 5,
				value: 5,
				order_no: '',
				PayPirce: 0,
				detail: {},
				people: {},
				qrUrl: null,
				is_collect: 0,
				popupStyle: {
					width: '690rpx',
					height: '716rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center'
				},
				timeList: [],
				selectedTime: null,
				// indexBackgroundImage: indexBackgroundImage,
				orderId: "",
				classes_lib_spec_id: '',
				order_no2: '',
				mobile: '',
				is_show_model: false, //是否显示分享模态窗
				background: '#ffffff',
				titleStyle: {
					color: '#000000'
				},
			};
		},
		//发送给朋友
		onShareAppMessage() {
			return {
				title: this.detail.title, //分享的标题
				imageUrl: this.detail.images[0], //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
				path: `/packageA/center/detail?id=${this.id}&isShare=1`
			}
		},
		//发送朋友圈
		// onShareTimeline() {
		// 	return {
		// 		title: this.detail.title, //分享的标题
		// 		imageUrl: this.detail.images[0], //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
		// 		query: `id=${this.id}&isShare=1`
		// 	}
		// },

		onLoad(options) {
			this.userInfo = uni.getStorageSync("userInfo")
			this.id = options.id
			if (options.isShare) {
				this.isShare = options.isShare;
			}
			// this.id = 1
			if (options.type == 2) {
				this.type = 2
				this.orderId = options.orderId
			}
			if (options.type == 1) {
				this.type = 1
				this.order_no = options.order_no
				this.pament()
			}
			console.log(options.id)
			this.getDetail();
			this.getSignPeople();
			//this.getPeople()
			// this.getTime()
			this.getShare()
		},
		mounted() {
			this.checkContentHeight();
		},
		updated() {
			this.checkContentHeight();
		},
		onReachBottom() {
			this.peopleParams.page += 1;
			this.getSignPeople();
		},
		onPageScroll(r) {
			const currentScrollTop = r.scrollTop;
			const upperThreshold = 620; // 向下滚动隐藏导航栏的阈值
			const lowerThreshold = 580; // 向上滚动显示导航栏的阈值

			// 判断滚动方向
			const isScrollingDown = currentScrollTop > this.lastScrollTop;
			const isScrollingUp = currentScrollTop < this.lastScrollTop;

			// 双阈值防抖动逻辑
			if (isScrollingDown && currentScrollTop > upperThreshold) {
				// 向下滚动超过上阈值，隐藏导航栏
				this.topShow = false;
			} else if (isScrollingUp && currentScrollTop < lowerThreshold) {
				// 向上滚动低于下阈值，显示导航栏
				this.topShow = true;
			}
			// 在阈值区间内(580-620px)保持当前状态不变

			// 更新上次滚动位置
			this.lastScrollTop = currentScrollTop;
		},
		methods: {
			toPhone() {
			if (this.detail.mobile == '' || this.detail.mobile == null) {
				uni.showToast({
					title: '暂无联系电话',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			//uni拨打电话
			uni.makePhoneCall({
				phoneNumber: this.detail.mobile, // 替换为你要拨打的电话号码
				success: () => {
					console.log('拨打电话成功！');
				},
				fail: () => {
					console.error('拨打电话失败！');
				}
			});
		},
			// 切换tab
			switchTab(index) {
				this.checkTab = index;

				// 滚动到对应内容区域
				// 使用 setTimeout 确保 DOM 更新完成后再获取位置
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this);
					let selector = '';

					if (index === 0) {
						// 活动详情 - 滚动到富文本内容区域
						selector = '#third';
					} else if (index === 1) {
						// 名单 - 滚动到报名信息区域
						selector = '#fourth';
					}

					if (selector) {
						query.select(selector).boundingClientRect(data => {
							if (data) {
								// 获取页面滚动信息
								uni.createSelectorQuery().selectViewport().scrollOffset(scroll => {
									// 计算目标滚动位置：元素距离文档顶部的位置 - 顶部间距
									const targetScrollTop = data.top + scroll.scrollTop - uni
										.upx2px(400);
									uni.pageScrollTo({
										scrollTop: Math.max(0, targetScrollTop),
										duration: 300 // 300ms滚动动画
									});
								}).exec();
							}
						}).exec();
					}
				}, 150); // 增加延迟确保富文本展开动画完成
			},
			openSer() {
				var that = this;
				uni.showActionSheet({
					itemList: ['默认排序', '最早报名', '最新报名', '最近报名'],
					success: function(res) {
						if (res.tapIndex == 0) {
							that.peopleParams.order = 'normal';
						} else if (res.tapIndex == 1) {
							that.peopleParams.order = 'earliest';
						} else if (res.tapIndex == 2) {
							that.peopleParams.order = 'new';
						} else {
							that.peopleParams.order = 'recently';
						}
						that.signPeopleList = [];
						that.peopleParams.page = 1;
						that.getSignPeople();
					},
					fail: function(res) {
						console.log(res.errMsg);
					}
				});
			},
			checkContentHeight() {
				// 使用 uni.createSelectorQuery 获取富文本容器的高度
				const query = uni.createSelectorQuery().in(this);
				query.select('.v_html').boundingClientRect(data => {
					if (data && data.height > 300) { // 300px 是容器的固定高度
						this.showToggleButtons = true;
					} else {
						this.showToggleButtons = false;
					}
				}).exec();
			},
			swiperImg(index) {
				uni.previewImage({
					current: index,
					urls: this.detail.images,
				});
			},
			openImg(img) {
				uni.previewImage({
					urls: [img],
				});
			},
			// saveClick() {
			// 	console.log('saveClick')
			// 	// 生成图片
			// 	this.$refs.painter.canvasToTempFilePathSync({
			// 		fileType: "jpg",
			// 		// 如果返回的是base64是无法使用 saveImageToPhotosAlbum，需要设置 pathType为url
			// 		pathType: 'url',
			// 		quality: 0.9,
			// 		success: (res) => {
			// 			console.log(res.tempFilePath);
			// 			// 非H5 保存到相册
			// 			wx.saveImageToPhotosAlbum({
			// 				filePath: res.tempFilePath,
			// 				success: function() {
			// 					uni.showToast({
			// 						title: '图片已保存'
			// 					})
			// 				},
			// 				fail:function(){
			// 					uni.showToast({
			// 						icon: 'error',
			// 						title: '图片保存失败'
			// 					})
			// 				}
			// 			});
			// 		},
			// 	});
			// },

			// capturePage() {
			// 	let that = this;
			// 	uni.canvasToTempFilePath({
			// 		x: 0, // 起始x坐标（可选）
			// 		y: 0, // 起始y坐标（可选）
			// 		width: uni.upx2px(750), // 宽度（单位px）
			// 		height: uni.upx2px(1334), // 高度（单位px）
			// 		destWidth: 750, // 目标图片宽度（可选）
			// 		destHeight: 1334, // 目标图片高度（可选）
			// 		canvasId: 'myCanvas', // Canvas组件的id
			// 		success: function (res) {
			// 			console.log('tempFilePath:', res.tempFilePath); // 图片路径信息输出到控制台或进行其他处理
			// 			that.save(res.tempFilePath);
			// 		},
			// 		fail: function (err) {
			// 			console.error(err); // 输出错误信息到控制台进行调试
			// 		}
			// 	});
			// },
			// 返回首页
			goHome() {
				console.log(1);
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.switchTab({
						url: "/pages/index/index"
					})
				}
			},
			closeoo() {
				this.overlay = false;
			},
			overlayShow() {
				const token = uni.getStorageSync('token');
				if (token) {

					let that = this;
					uni.showToast({
						title: '海报生成中...',
						icon: 'none',
						duration: 2000,
						complete: function() {
							// 提示框消失后的回调函数
							setTimeout(() => {
								// 这里写你的后续操作代码
								that.overlay = true;
							}, 2000);
						}
					});



				} else {
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function() {
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/my/index',
								});
							}, 2000);
						}
					});
				}
			},
			getShare() {
				uni.$u.http.post('/api/wechat_util/link', {
					path: 'packageA/center/detail',
					query: `id=${this.id}`,
				}).then(res => {
					if (res.code == 1) {
						this.qrUrl = res.data.url_link
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			formatTimestamp(timestamp) {
				const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, "0");
				const day = String(date.getDate()).padStart(2, "0");
				const hours = String(date.getHours()).padStart(2, "0");
				const minutes = String(date.getMinutes()).padStart(2, "0");
				const seconds = String(date.getSeconds()).padStart(2, "0");
				return `${year}.${month}.${day}`;
			},
			// 格式化创建时间为 MM-DD HH:mm 格式
			formatCreateTime(timestamp) {
				const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
				const month = String(date.getMonth() + 1).padStart(2, "0");
				const day = String(date.getDate()).padStart(2, "0");
				const hours = String(date.getHours()).padStart(2, "0");
				const minutes = String(date.getMinutes()).padStart(2, "0");
				return `${month}-${day} ${hours}:${minutes}`;
			},
			// 时间转换函数
			timeago(timestamp) {
				const now = new Date().getTime(); // 当前时间（毫秒）
				const diff = (now - timestamp * 1000) / 1000; // 时间差（秒）

				if (diff < 60) {
					return `${Math.floor(diff)}秒前`;
				} else if (diff < 3600) {
					return `${Math.floor(diff / 60)}分钟前`;
				} else if (diff < 86400) {
					return `${Math.floor(diff / 3600)}小时前`;
				} else if (diff < 2592000) { // 30天
					return `${Math.floor(diff / 86400)}天前`;
				} else {
					return `${Math.floor(diff / 2592000)}个月前`;
				}
			},

			sharePoster() {
				//获取带参数二维码并传递
				this.is_show_model = false
				this.$refs.poster.showCanvas()
			},

			// 获取课时规格
			getTime() {
				uni.$u.http.get('/api/school/classes/spec', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {

						this.timeList = res.data.spec;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 获取报名详情
			getPeople() {
				const that = this
				uni.$u.http.get('/api/school/classes/people', {
					params: {
						id: that.id,
					}
				}).then(res => {
					if (res.code == 1) {
						that.processPeopleData(res.data).then(processedData => {
							that.people = processedData;
							console.log('that.people', that.people)
						});

					} else {
						that.showErrorToast(res.msg);
					}
				}).catch(error => {
					that.showErrorToast('请求失败，请稍后再试');

				});
			},
			// 数据转换
			processPeopleData(data) {
				return new Promise((resolve) => {
					const paidUserData = data.paid_user_data.map(item => {
						item.createTime = this.timeago(item.time);
						return item;
					}).reverse();
					const unpaidUserData = data.unpaid_user_data
					const processedData = {
						unpaid_user_data: unpaidUserData,
						paid_user_data: paidUserData
					}
					resolve(processedData);
				});
			},
			// 提示
			showErrorToast(msg) {
				uni.showToast({
					title: msg,
					icon: 'none',
					duration: 2000
				});
			},
			// 获取详情
			getDetail() {
				uni.$u.http.get('/api/school.new_activity/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
						this.value_slide = res.data.detail.join_info.percent;
						if (res.data.detail.is_collect != 0) {
							this.is_collect = 1
						} else {
							this.is_collect = 0
						}
						if (this.detail.user.realname) {
							this.detail.user.realname = this.detail.user.realname.slice(0, 1) + 'XX';
						}
						this.mobile = this.detail.user.mobile.slice(0, 3) + 'XXXX' + this.detail.user.mobile.slice(
							7);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},

			//报名人信息列表
			getSignPeople() {
				uni.$u.http.get('/api/school.new_activity/people_list', {
					params: {
						activity_id: this.id,
						page: this.peopleParams.page,
						limit: this.peopleParams.limit,
						order: this.peopleParams.order,
						status: this.peopleParams.status,
					}
				}).then(res => {
					if (res.code == 1) {
						console.log('peopleList', res.code, res.data);
						this.signPeopleList.push(...res.data.list);
						this.peopleNum = res.data.count;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},

			// 报名  0 支付 1 立即购买 2 预约  3确认时间

			sign() {
				uni.navigateTo({
					url: '/packageA/center/applyDetail?id=' + this.id
				})

			},

			buy() {
				this.uloadingShow = true
				this.getMoney()
			},
			// 购买弹窗 type =  0 支付 1 立即购买 2 预约  3确认时间
			buyOpen() {
				this.buyShow = true
				this.type = 1
			},
			// 预约弹窗 type = 2 0 支付 1 立即购买 2 预约  3确认时间
			open() {
				this.show = true
				this.type = 3
			},



			// 导航
			toMap(latitude, longitude, name) {
				uni.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					success: function() {
						console.log('success');
					}
				});
			},


			close() {
				this.type = 0
				this.selectedTime = null
				this.show = false
			},
			buyClose() {
				this.type = 0
				this.selectedTime = null
				this.buyShow = false
			},
			sunccessimg(event) {
				this.path = event
			},
			// 保存海报
			save() {
				const base64 = this.path.replace(/^data:image\/\w+;base64,/, ""); //图片替换
				const arrayBuffer = uni.base64ToArrayBuffer(base64);
				const filePath = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`;
				uni.getFileSystemManager().writeFile({
					filePath, //创建一个临时文件名
					data: arrayBuffer, //写入的文本或二进制数据
					encoding: 'binary', //写入当前文件的字符编码
					success: () => {
						uni.saveImageToPhotosAlbum({
							filePath,
							success: () => {
								uni.showToast({
									title: '保存成功',
									icon: "none",
									duration: 5000
								})
								this.overlay = false;
							},
							fail: (err) => {
								console.log(err);
								uni.showToast({
									title: '保存失败，请检查权限',
									icon: "none",
									duration: 5000
								})
								this.overlay = false;
							}
						})
					},
					fail: (err) => {
						console.log(err)
						this.overlay = false;
					}
				})
			},
			//分享发布
			sharePoster() { //分享图片给好友按钮的点击事件函数
				let that = this
				this.base64ToFilePath(this.path, (filePath) => {
					//console.log(filePath);
					wx.showShareImageMenu({ //分享给朋友
						path: filePath,
						success: (res) => {
							console.log("分享成功：", res);
						},
						fail: (err) => {
							console.log("分享取消：", err);
						},
					})
				})
			},


			base64ToFilePath(base64data, callback) {
				const time = new Date().getTime();
				const imgPath = `${wx.env.USER_DATA_PATH}/addFriends${time}share_qrcode.png`;
				const imageData = base64data.replace(/^data:image\/\w+;base64,/, "");
				const fileSystemManager = uni.getFileSystemManager();

				fileSystemManager.writeFile({
					filePath: imgPath,
					data: imageData,
					encoding: 'base64',
					success: () => {
						callback(imgPath);
					},
					fail: (err) => {
						console.error('Write file failed:', err);
						uni.showToast({
							title: '写入文件失败',
							icon: 'none'
						});
					}
				});
			},

			// 收藏和取消
			Collect(number) {
				uni.$u.http.post('/api/school/classes/collect', {
					id: this.id,
					is_collect: number
				}).then(res => {
					if (res.code == 1) {
						this.is_collect = number
						if (number == 0) {
							uni.showToast({
								title: '取消收藏',
								icon: 'none',
								duration: 2000
							})
						} else {
							uni.showToast({
								title: '收藏成功',
								icon: 'none',
								duration: 2000
							})
						}

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},

			// 取消选择
			cancel() {
				this.selectedTime = null
			},
			selectTime(time) {
				this.selectedTime = time;
				this.classes_lib_spec_id = time.id

			},
			timeSelected(time) {
				return this.selectedTime === time;
			},
			moveScroll() {},
			// 获取价格
			getMoney() {
				uni.$u.http.post('/api/school.newactivity.order/confirm', {
					activity_id: this.id,
					order_no: this.order_no,
					is_compute: 1,
					num: 1
				}).then(res => {
					if (res.code == 1) {
						this.PayPirce = res.data.order_data.totalprice
						this.order_no = res.data.order_no
						this.create(this.order_no, this.PayPirce)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
					this.uloadingShow = false
				}).catch(error => {

				});
			},
			// 创建订单
			create(order_no, PayPirce) {
				uni.$u.http.post('/api/school.newactivity.order/create', {
					order_no: order_no,
				}).then(res => {
					if (res.code == 1) {
						if (PayPirce != 0) {
							this.pament()
						} else {
							this.uloadingShow = false
							uni.showToast({
								title: '创建成功',
								icon: 'success',
								duration: 2000,
								complete: function() {
									setTimeout(function() {
										uni.redirectTo({
											url: "/packageA/my/orderList?status=" + 3
										})
									}, 2000);
								}
							});
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						that.uloadingShow = false

						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 支付
			pament() {
				let that = this
				that.uloadingShow = false
				uni.$u.http.post('/api/school.newactivity.pay/payment', {
					type: 'wechat',
					order_no: that.order_no,
					platform: 'miniapp'
				}).then(res => {
					if (res.code == 1) {
						wx.requestPayment({
							timeStamp: res.data.paydata.timeStamp, //时间戳
							nonceStr: res.data.paydata.nonceStr, //随机字符串
							package: res.data.paydata.package, //prepay_id
							signType: res.data.paydata.signType, //签名算法MD5
							paySign: res.data.paydata.paySign, //签名
							success(res) {
								if (res.errMsg == "requestPayment:ok") {
									that.order_no = ''
									uni.redirectTo({
										url: "/packageA/my/exercise?status=" + 3
									})
									console.log('支付成功', res)
								} else {
									that.uloadingShow = false
									console.log('支付失败')
								}
							},
							fail(res) {
								that.uloadingShow = false
								console.log('支付失败', res)
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.detail_all {
		background-color: #f7f7f7;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.con-center {
		background: white;
		border-radius: 44rpx;
		position: relative;
		padding-bottom: 25rpx;
	}

	.space-between {
		justify-content: space-between;
	}

	.swiper {
		width: 100%;
		height: 580rpx;
	}

	.box {
		padding-top: 175rpx;
		position: relative;
	}

	.topimgs_rmb {
		position: absolute;
		top: -168rpx;
		right: 30rpx;
		width: 201rpx;
		height: 118rpx;
		z-index: 0;
	}

	.topimgs {
		position: absolute;
		top: -120rpx;
		width: 100%;
		z-index: 0;
		height: 100%;
	}

	.top_texts {
		position: absolute;
		top: -148rpx;
		right: 30rpx;
		z-index: 3;
		color: #ffffff;
		width: 200rpx;
		text-align: center;
		display: flex;
		justify-content: center;

		.xiao {
			margin-left: 4rpx;
			font-size: 22rpx;
			font-weight: bold;
		}
	}

	.first-box {
		width: 690rpx;
		background: #FFFFFF;
		// background: url('@/static/detail/conbg.png');
		// padding-left: 24rpx;
		// margin-top: 20rpx;
		border-radius: 20rpx;
		z-index: 1;

		.sigh {
			width: 88rpx;
			height: 40rpx;
			background: #BEEE03;
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 24rpx;
			color: #222222;
		}

		.first-name {
			width: 690rpx;
			height: 52rpx auto;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #323232;
			// margin-left: 16rpx;
		}


		.first-mine {
			height: 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

		}

		.first-txt {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;
			line-height: 32rpx;
			margin: 0 6rpx;
		}

		.first-image {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

			span {
				width: 600rpx;
				// height: 40rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 30rpx;
				color: #222222;
			}

			.xieyi {
				background-color: #BBFC5B;
				height: 48rpx;
				border-radius: 8rpx;
				padding: 0px 10rpx;
			}
		}
	}

	.second-box {
		width: 690rpx;
		height: 64rpx;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 20rpx 0 20rpx 0;

		.smalld {
			margin-left: 0 !important;
		}

		.number {
			height: 40rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #323232;
			margin-left: 20rpx;
		}

		view {
			span {
				background: rgba(255, 255, 255, 0.4);
				border-radius: 24rpx;
			}
		}
	}

	.tab-underline {
		position: absolute;
		bottom: 15rpx;
		left: 70rpx;
		height: 6rpx;
		background-color: #323232;
		width: 50rpx;
		border-radius: 3rpx;
		transition: transform 0.3s ease;
	}

	.third-top {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 36rpx;
		color: #323232;
		line-height: 50rpx;
		position: relative;
		z-index: 10;
		background-color: #ffffff;
	}

	.tab-item {
		cursor: pointer;
		transition: color 0.3s ease;
		color: #9C9C9C;
		position: relative;
	}

	.tab-active {
		color: #323232 !important;
	}

	.third {
		width: 100%;
		background: #ffffff;
		margin-top: 15rpx;
		// margin-bottom: 150rpx;
		padding-bottom: 30rpx;





		span {
			position: relative;

			.icons {
				width: 37rpx;
				height: 20rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				z-index: -1;
			}
		}

		.third-center {
			padding: 30rpx;
			overflow: hidden;
			margin: 0 auto;
			position: relative;
			// height: 100%;

			.v_html {
				font-size: 34rpx;
				line-height: 52rpx;
			}

			.imgs {
				display: block;
				width: 690rpx;
				// display: flex;
				// width: 690rpx;

			}
		}

		.third-center1 {
			padding: 30rpx;
			overflow: hidden;

			.v_html {
				font-size: 34rpx;
				line-height: 52rpx;
			}

			.imgs {
				display: block;
				width: 690rpx;
				// width: 690rpx;
				// display: block;
			}
		}

		.imgs {
			display: block;
			border-radius: 10rpx;
			width: 220rpx;
			height: 220rpx;
		}


	}

	.fourth {
		width: 100%;
		background: #ffffff;
		margin-top: 20rpx;
		border-radius: 44rpx;

		.fourth-top {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #323232;
			line-height: 50rpx;
			margin-top: 30rpx;
			margin-left: 30rpx;
			position: relative;
			z-index: 10;
		}

		span {
			position: relative;

			.icons {
				width: 37rpx;
				height: 20rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				z-index: -1;
			}
		}

		.mgbot {
			margin-bottom: 210rpx;
		}

	}

	.yes {
		background-color: #999999;
	}

	.line {
		width: 690rpx;
		height: 2rpx;
		background: #eeeeee;
		margin: 19rpx 0;
	}

	.icon-size {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}


	.footer {
		width: 100%;
		height: 166rpx;
		// background: #ffffff;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: fixed;
		z-index: 99;
		/* 绝对定位 */
		bottom: 40rpx;
		/* 定位在底部 */
		left: 0;
		/* 定位在左边 */

		.footer-left {
			position: absolute;
			left: 100rpx;
			display: flex;

			view {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-flow: column;
				margin-right: 40rpx;
				width: 48rpx;

				span {
					text-align: center;
					width: 48rpx;
					height: 34rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 22rpx;
					color: #A4A4A4;
				}
			}

			image {
				width: 191rpx;
				height: 118rpx;
			}
		}

		.footer-right {
			position: absolute;
			right: 30rpx;
			width: 487rpx;
			height: 102rpx;
			background: url('@/static/detail/shangche.png');
			background-size: 487rpx 102rpx;
			border-radius: 200rpx;

			span {
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 48rpx;
				color: #BBFC5B;
			}
		}
	}


	.popupBox {
		width: 690rpx;
		height: 716rpx;

		.pop-header {
			width: 100%;
			background-image: url("/static/center/bg.png");
			background-repeat: no-repeat;
			background-position: left bottom;
			height: 265rpx;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}

			.name {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #222222;
				margin-top: 80rpx;
			}

			.price {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #FF4810;
				margin-top: 16rpx;

				span {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 36rpx;
					color: #FF2323;
					line-height: 32rpx;
				}
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			width: 594rpx;
		}

		.popup-footer {
			position: absolute;
			left: 48rpx;
			bottom: 48rpx;

			span {
				width: 594rpx;
				height: 100rpx;
				background: #222222;
				border-radius: 200rpx 200rpx 200rpx 200rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 32rpx;
				color: #BEEE03;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #eeeeee;
		}

		.times {
			width: 93%;

			.selectTime {
				width: 288rpx;
				height: 50rpx;
				background: #FFFFFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 1rpx solid #D9D9D9;
				color: #4B4B4B;
				font-family: 'PingFang SC', 'PingFang SC';
				font-weight: 500;
				font-size: 24rpx;
				padding-left: 15rpx;
				cursor: pointer;
				margin: 24rpx 32rpx 0 0;
				white-space: nowrap;
				/* 防止文本换行 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-align: left;
				/* 文字靠左对齐 */
				line-height: 50rpx;
				/* 垂直居中对齐 */
				box-sizing: border-box;
				/* 确保 padding 和 border 不影响宽度和高度 */
				display: inline-block;
				/* 确保容器内文字正确对齐 */
			}

		}

		.selectTime.selected {
			width: 288rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;

		}
	}

	.share {
		position: fixed;
		color: #FFFFFF;
		right: 0;
		bottom: 190rpx;
		background: linear-gradient(to bottom right, #FE726B, #FE956B);
		padding: 10rpx 10rpx 10rpx 20rpx;
		border-top-left-radius: 50px;
		border-bottom-left-radius: 50px;
		box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
	}

	.cancel {
		width: 100vw;
		padding: 30rpx;
		text-align: center;
		background: #FFFFFF;
		color: red;
		font-weight: bold;
		font-size: 30rpx;
	}

	.md-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;
		background-color: white;
	}

	.md-content-item {
		margin: 0 70rpx;
		position: relative;
	}

	.md-content-item image {
		width: 100rpx;
		height: 100rpx;
	}

	.md-content-item view {
		margin-top: 15rpx;
		font-size: 28rpx;
	}

	.sharebtn {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
	}

	.cu-modal {
		position: fixed;
		bottom: 166rpx;
		left: 0;
		z-index: 999999;
	}

	.gj {
		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 32rpx;
			color: #4B4B4B;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #4B4B4B;
			}
		}

		.scroll {
			width: 642rpx;
			max-height: 340rpx;

			view {
				margin: 24rpx;
				width: 600rpx;
				height: 56rpx;
				background: #E8E8E8;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #7A7A7A;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #FF5F5F;
				}

				.lan {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #008CFF;
				}
			}
		}
	}

	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}

	.pos {
		position: fixed;
		width: 100%;
		height: 100%;
		z-index: 99999;

		.posterClose {
			position: absolute;
			// right: 8rpx;
			top: 250rpx;
		}
	}

	.Poster {
		position: relative;
		margin: 0 auto;

		.posterClose {
			position: absolute;
			right: 8rpx;
			top: 200rpx;
		}

		.title_top {
			position: relative;
			margin: 0 auto;
			width: 661rpx;
			height: 1075rpx;
			background-image: url('https://naweigetetest2.hschool.com.cn/dyqc/fenxiang.png');
			background-size: 100%;
			background-repeat: no-repeat;
			margin-top: 240rpx;

		}

		.toutop {
			position: absolute;
			top: 120rpx;
			left: 30rpx;
			display: flex;
			align-items: center;
			width: 80%;

			.toutu {
				width: 60rpx;
				height: 60rpx;
				border-radius: 134rpx 134rpx 134rpx 134rpx;
				border: 2rpx solid #FFFFFF;
			}

			.toututt {
				padding-left: 15rpx;
				height: 36rpx;
				font-family: PingFang SC Bold, PingFang SC Bold;
				font-weight: 400;
				font-size: 28rpx;
				color: #3D3D3D;
				line-height: 36rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}

		.center_con {
			padding-top: 200rpx;

			.cenimg {
				width: 620rpx;
				height: 620rpx;
				border-radius: 24rpx;
				margin: 0 auto;
				display: block;
			}

			.center_text {
				display: flex;
				align-items: center;
				justify-content: space-around;
				margin-top: 30rpx;

				.c1t1 {
					width: 340rpx;
					color: #202020;
					font-size: 36rpx;
					// margin-top: 30rpx;
				}

				.c2t2 {
					font-family: D-DIN-PRO, D-DIN-PRO;
					font-weight: 900;
					font-size: 28rpx;
					color: #FF4810;
					margin-top: 20rpx;
				}

				.c3t3 {
					margin-top: 20rpx;
					font-family: PingFang SC Regular, PingFang SC Regular;
					font-weight: 400;
					font-size: 26rpx;
					color: #9C9C9C;
					width: 340rpx;
				}
			}
		}

	}





	.btnList {
		position: absolute;
		bottom: 173rpx;
		display: flex;
		justify-content: center;
		align-items: self-start;
		width: 750rpx;
		height: 247rpx;
		background: #FFFFFF;
		border-radius: 44rpx 44rpx 0rpx 0rpx;
		color: #999999;
		font-family: PingFang SC Regular, PingFang SC Regular;
		font-weight: 400;
		font-size: 28rpx;
		gap: 180rpx;

		.save {
			margin-top: 60rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}

	.no-scroll {
		overflow: hidden;
		height: 100vh;
	}

	::v-deep ._root {
		padding: 0 10rpx;
	}

	.no-border-button {
		background-color: transparent;
		/* 去掉背景色 */
		border: none;
		/* 去掉边框 */
		padding: 0;
		/* 去掉内边距 */
		margin: 0;
		/* 去掉外边距 */
		display: inline-flex;
		/* 使按钮内容居中 */
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		flex-flow: column;
		// height: 80rpx;
		line-height: inherit;
		margin-top: 60rpx;
		font-family: PingFang SC Regular, PingFang SC Regular;
		font-weight: 400;
		font-size: 28rpx;
		color: #999999;
	}
</style>
<style lang="scss">
	.value_slide {
		width: 40%;

		::v-deep .uni-slider-handle-wrapper {
			height: 10rpx;
		}

		::v-deep .uni-slider-handle {
			background: url('@/static/detail/qiu.png') !important;
			border-radius: 0;
			background-size: 36rpx 36rpx !important;
			width: 36rpx;
			height: 36rpx;
			top: 14rpx;
			margin-left: -18rpx !important;
		}

		::v-deep .uni-slider-value {
			color: #323232;

			&::after {
				content: '%';
			}
		}

		// #ifdef MP-WEIXIN
		.wx-slider-handle-wrapper {
			height: 8rpx;
		}

		.wx-slider-handle {
			background: url('@/static/detail/qiu.png') !important;
			border-radius: 0;
			background-size: 30rpx 30rpx !important;
			width: 28rpx;
			height: 28rpx;
			top: 14rpx;
			margin-left: -14rpx !important;
		}

		.wx-slider-value {
			display: flex;
			width: 30rpx;
			color: #323232;

			&::after {
				content: '%';
			}
		}

		// #endif
	}

	.ck-qrcode {
		width: 180rpx;
		height: 70rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 42rpx;
		color: #BBFC5B;
		line-height: 70rpx;
		text-align: center;
	}

	.btn_1 {
		width: 95%;
		height: 90rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 90rpx;
		text-align: center;
		margin: 0 auto;
		margin-top: 30rpx;
	}

	.no-check {
		color: #9C9C9C !important;
	}
</style>