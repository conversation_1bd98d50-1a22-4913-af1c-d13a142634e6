<template>
	<view class="bank-container">
		<view style="background-color: #ffffff;border-radius: 0rpx 0rpx 44rpx 44rpx;padding:40rpx 30rpx;">
			<view @click="addBank()"
				style="display: flex;justify-content: space-between;align-items: center;background: #323232;height: 220rpx;width: 100%;border-radius: 18rpx;">
				<view v-if="bankInfo == ''" style="display: flex;align-items: center;padding-left: 30rpx;">
					<image src="/static/add.png" style="width: 40rpx;height: 40rpx;"></image>
					<view style="color: #ffffff;font-size: 32rpx;margin-left: 20rpx;">添加银行卡</view>
				</view>
				<view v-if="bankInfo != ''" style="padding-left: 30rpx;">
					<view style="color: #ffffff;font-size: 32rpx;">{{ bankInfo.bank_name }}</view>
					<view
						style="color: #ffffff;font-size: 32rpx;margin-top: 20rpx;font-family: PingFang SC, PingFang SC;">
						{{ formatBankCard(bankInfo.bank_user_name) }}</view>
				</view>
				<view style="padding-right: 20rpx;">
					<u-icon color="#BBFC5B" name="arrow-right"></u-icon>
				</view>
			</view>
		</view>
		<view style="background-color: #ffffff;border-radius:44rpx;padding:40rpx 30rpx;margin-top: 20rpx;">
			<view style="font-size: 28rpx;color: #3D3D3D;font-weight: 400;">
				可提现金额
			</view>
			<view style="margin-top: 20rpx;font-family: D-DIN-PRO, D-DIN-PRO;">
				<text style="font-size: 28rpx;color: #3D3D3D;font-weight: 700;">￥</text>
				<text style="font-size: 70rpx;color: #3D3D3D;font-weight: 700;margin-left: 20rpx;">{{
					settleInfo.settled_amount }}</text>
			</view>
			<view style="background-color: #eeeeee;height: 1px;width: 100%;margin-top: 20rpx;"></view>
			<view style="line-height:40rpx;padding-bottom: 200rpx;">
				<view style="font-size: 24rpx;color: #000000;font-weight: 600;margin-top: 20rpx;">
					<view>1、提现金额：</view>
					<view style="text-indent: 2em;color: #FF4810;">本平台的提现操作默认将您当前“可提现金额”的全部款项一次性提现，暂不支持部分提现。</view>
				</view>
				<view style="font-size: 24rpx;color: #000000;margin-top: 20rpx;">
					<view style="font-weight: 600;">2、到账时间：</view>
					<view style="text-indent: 2em;">我们将在您提交有效提现申请后的<text style="color: #FF4810;font-weight: 600;">T+3个工作日内</text>处理您的请求（T为申请日）。工作日不含周末及法定节假日，我们会尽快处理，但具体到账时间以收款银行的实际处理速度为准。</view>
				</view>
				<view style="font-size: 24rpx;color: #000000;margin-top: 20rpx;font-weight: 600;">
					<view>3、关于手续费：</view>
					<view style="text-indent: 2em;color: #FF4810;">若您的提现账户为跨行或跨区域的对公账户，银行在处理转账时可能会收取额外的手续费。该费用由银行直接扣除，非本平台收取，具体扣费金额以您的最终实际到账金额为准。</view>
				</view>
				<view style="font-size: 24rpx;color: #000000;margin-top: 20rpx;">
					<view style="font-weight: 600;">4、信息准确性：</view>
					<view style="text-indent: 2em;">请务必确保您填写的<text style="color: #FF4810;font-weight: 600;">持卡人姓名、银行卡号、开户银行</text>等信息完全正确。因您填写信息错误导致的任何资金损失、提现延迟或失败，全部责任由您自行承担。</view>
				</view>
				<view style="font-size: 24rpx;color: #000000;margin-top: 20rpx;">
					<view style="font-weight: 600;">5、联系客服：</view>
					<view style="text-indent: 2em;">如在提现过程中遇到任何问题，或超过预计到账时间仍未收到款项，请及时联系平台客服。</view>
				</view>
			</view>
			
		</view>
		<view style="position: fixed;bottom: 0;width: 100%;background-color: #ffffff;padding: 20px 0px;">
			<view class="btn_1" @click="withdraw()">确认提现</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bankInfo: '',
			settleInfo: {},
		}
	},
	onShow() {
		this.getBankList();
		this.getUserInfo();
	},
	methods: {
		getBankList() {
			uni.$u.http
				.get("/api/school.newactivity.settle_log/detail")
				.then((res) => {
					console.log(res);
					this.bankInfo = res.data.withdrawal==''||res.data.withdrawal==null?'':res.data.withdrawal;
				});
		},
		//格式化银行卡，格式为：4252 **** **** **** 2563
		formatBankCard(cardNumber) {
			// 转换为字符串并移除非数字字符 
			const cleaned = cardNumber.toString().replace(/\D/g, '');
			// 验证长度（至少8位才能保留首尾各4位）
			if (cleaned.length < 8) return cardNumber;
			// 提取前4位和后4位 
			const firstPart = cleaned.substring(0, 4);
			const lastPart = cleaned.substring(cleaned.length - 4);
			// 组合成目标格式 
			return `${firstPart}${'*'.repeat(12)}${lastPart}`;
		},
		withdraw() {
			if(this.bankInfo==''||this.bankInfo==null){
				uni.showToast({
					title: '请先添加银行卡',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			//确认是否提现
			uni.showModal({
				title: '提示',
				content: '确认提现？',
				success: (res) => {
					if (res.confirm) {
						this.withdrawDo();
					}
				}
			});
		},
		withdrawDo() {
			//提现
			uni.$u.http
				.post("/api/school.newactivity.settle_log/withdrawal")
				.then((res) => {
					console.log(res);
					if(res.code==1){
						uni.showToast({
							title: '提现成功',
							icon: 'success',
							duration: 2000,
							complete: function () {
								setTimeout(function () {
									uni.navigateBack();
								}, 1500);
							}
						})
					}else{
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				});
		},// 获取个人信息
		getUserInfo() {
			uni.$u.http.get('/api/user/index', {}).then(res => {
				if (res.code == 1) {
					uni.setStorageSync('userInfo', res.data.user_info)
					uni.setStorageSync('settleInfo', res.data.settle_info)
					uni.setStorageSync('activityInfo', res.data.activity_info)
					this.settleInfo = res.data.settle_info;
				} else {
					uni.showToast({
						title: '登陆失败',
						icon: 'error',
						duration: 2000
					})
				}

			}).catch(error => {
				console.log('error', error);
				this.showPopup = false
				uni.showToast({
					title: '登陆失败',
					icon: 'error',
					duration: 2000
				})
			})
		},
		addBank(){
			uni.navigateTo({
				url: '/packageB/wallet/add_bank'
			})
		},
	}
}
</script>

<style scoped lang="scss">
.bank-container {
	font-family: PingFang SC, PingFang SC;
	background: #F7F7F7;
	min-height: 100vh;
}

.btn_1 {
	width: 94%;
	height: 90rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
}
</style>
