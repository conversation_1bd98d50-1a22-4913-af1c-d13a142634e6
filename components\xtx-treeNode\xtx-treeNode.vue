<template>
	<view>
		<view 
			class="item--list" 
			v-for="(item, index) in list" 
			:key="index"
		>
			<view class="item--title" hover-class="tree__hover-class" @click="handleNodeClick(item);handleOpenClose(item, index)">
				<view :class="['ellipsis-multiline', `level-${level}`]">{{ item.name }}</view>
				<view 
					v-if="item.children && item.children.length" 
					class="open__and--close"
				>
					<image v-if="!item.isOpen" src="/static/my/Fold.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
					<image v-if="item.isOpen" src="/static/my/Spread.png" mode="" style="width: 24rpx;height: 24rpx;"></image>
				</view>
			</view>
			<view v-if="item.isOpen && item.children && item.children.length" class="">
				<xtx-treeNode :list="item.children" :level="level + 1" @change="selectNode"></xtx-treeNode>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'xtx-treeNode',
	props: {
		list: {
			type: Array,
			default: () => []
		},
		level: {
			type: Number,
			default: 1
		}
	},
	methods: {
		selectNode(item) {
			this.$emit('change', item)
		},
		handleOpenClose(item, index) {
			if (!item.hasOwnProperty('isOpen')) {
				item.isOpen = false
			}
			item.isOpen = !item.isOpen
			this.$forceUpdate()
		},
		handleNodeClick(item) {
			if (item.help_cate_ids) {
				this.selectNode(item)
			}
		}
	}
}
</script>

<style scoped>
	.item--list {
		padding-left: 25rpx;
	}
	.item--title {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		border-bottom: 1rpx solid #f7f7f7;
		padding: 25rpx;
	}
	.open__and--close {
		margin-left: auto;
		font-size: 24rpx;
	}
	.tree__hover-class {
		background-color: #f7f7f7;
	}
	.ellipsis-multiline.level-1 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #343434;
	}
	.ellipsis-multiline.level-2 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #343434;
	}
	.ellipsis-multiline.level-3 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #008CFF;
	}
	.ellipsis-multiline {
	  display: -webkit-box;
	  -webkit-box-orient: vertical;
	  -webkit-line-clamp: 2;
	  overflow: hidden;
	  text-overflow: ellipsis;
	}
</style>