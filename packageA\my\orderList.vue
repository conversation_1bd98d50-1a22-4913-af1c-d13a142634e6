<template>
	<view class="box">
		<view class="con-center flex flex-column justify-center align-items">
			<view class="tabs">
				<u-tabs :current="headers.findIndex(item => item.status === selected)" :activeStyle="{
            color: '#303133',
            fontWeight: 'bold',fontSize: '32rpx'}" :inactiveStyle="{
            color: '#606266'
        }" :list="headers" lineColor="#323232" @click="selectheader"></u-tabs>
			</view>
			<!-- <view class="tabs align-items">
				<span class="s-header flex flex-column" v-for="(item, index) in headers" :key="index"
					:class="{ selected: headerSelected(item.status) }" @click="selectheader(item.status)">
					{{ item.text }}
					<span class="lines" :class="{ selected: headerSelected(item.status) }"></span>
				</span>
			</view> -->

			<view class="flex align-items justify-start shenhe" v-if="status == 0">
				<span :class="['span_s', { active: !auth_status }]" @click="toShlist(0)">待审核</span>
				<span :class="['span_s', { active: auth_status }]" style="margin-left: 20rpx;"
					@click="toShlist(2)">未通过</span>
			</view>

			<view class="flex align-items justify-start shenhe" v-if="status == -2">
				<span :class="['span_s', { active: server_status == 3 }]" @click="toAfter(3)">待售后</span>
				<span :class="['span_s', { active: server_status == 6 }]" style="margin-left: 20rpx;"
					@click="toAfter(6)">已售后</span>
				<span :class="['span_s', { active: server_status == -1 }]" style="margin-left: 20rpx;"
					@click="toAfter(-1)">已拒绝</span>
			</view>



			<view style="width: 100%;">
				<!-- 草稿箱列表 -->
				<!-- <view v-if="draftShow === true"> -->
				<view v-if="status == 15" style="margin-top: 110rpx;width: 100%;min-height: 100vh;height:auto;margin-bottom: 20rpx;">
					<!-- v-for="(item,index) in draftList" :key="index" @click="checkInvoice(item.invoiceCheck,index)"-->
					<view class="invoiceList" >
						<view class="invoiceList-item" v-for="(item,index) in draftList" :key="index">
							<view class="invoiceList-itemInfo flex">
								<view class="item-img">
									<image v-if="item.images.length>0"
										style="width: 170rpx;height: 170rpx;border-radius: 20rpx;" :src="item.images[0]"
										mode="aspectFill"></image>
									<image v-else style="width: 170rpx;height: 170rpx;border-radius: 20rpx;"
										src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/draftImgNo.png"
										mode="aspectFill"></image>
								</view>
								<view class="item-con">
									<view class="itenCon-actName" style="">{{item.title !='' ? item.title:'描述待补充' }}
									</view>
									<view class="itenCon-actCon" style="">
										{{item.content != '' ? item.content:'快来添加活动内容吧'}}
									</view>
									<view class="itenCon-actPrice" style="">保存时间： {{item.updatetime_text}}</view>
								</view>
							</view>
							<view class="item-draftBtns">
								<view class="part1 flex justify-center align-items" style="margin-right: 20rpx;" @click.stop="toDeleteDraft(item.id)">
									删除
								</view>
								<view class="part flex justify-center align-items" @click.stop="toEditDraft(item.id)">
									编辑
								</view>
							</view>
						</view>

					</view>
					<!-- <view class="invoiceBtn" @click="handleIssueInvoice()">
								<view>申请开票</view>
							</view> -->
					<view class="bottom_box flex justify-center align-items" style="height: 1240rpx;width: 100%;"
						v-if="draftList.length == 0">
						<view style="text-align: center;">
							<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
							<view>暂无数据</view>
						</view>
					</view>
				</view>

				<view :class="['hot', status == 0 || status == -2 ? 'wsh_hot' : '']" v-if="status != 15">
					<view class="content flex align-items flex-column">
						<view class="flex flex-column w-100 bbb" style="position: relative;"
							v-for="(item, index) in hotList" :key="index">
							<view v-if="status != -2" @click.stop="detail(item.id)">
								<image v-if="item.auth_status == 0" src="/static/my/daishenhe.png"
									style="width: 134rpx; height: 106rpx;position: absolute;right: 0;"></image>
								<image v-if="item.auth_status == 2" src="/static/my/wtg.png"
									style="width: 134rpx; height: 106rpx;position: absolute;right: 0;"></image>
								<view class="flex" style="margin-top: 24rpx">
									<view class="right flex flex-column" style="align-items: self-start;">

										<span class="title white-space"
											style="padding-left: 30rpx;font-weight: 600;">{{ item.title }}</span>
										<span class="first-image flex align-items"
											style="margin-top: 20rpx; width: 100%;padding-left: 30rpx;">
											<image :src="item.user.avatar"
												style="width: 36rpx; height: 36rpx;border-radius: 50%;"></image>
											<span class="white-space" style="color: #9c9c9c; margin-left: 10rpx;width: 210rpx;">{{
												item.user.nickname
											}}</span>
											<view class="white-space" style="width: 56%">
												<span style="color: #0ca013; margin-left: 10rpx"
													v-for="items_t in item.classes_cate">#{{ items_t }}</span>
											</view>
										</span>

										<view class="flex flex-column" style="margin-top: 20rpx;padding-left: 30rpx;">
											<view class="time flex align-items white-space">
												<span class="time_tex" v-if="item.status == 2">{{
													"报名中"
												}}</span>
												<!-- <span v-if="item.status == 2"
													style="margin: 0 20rpx">{{ dateWeeks(item.sign_start_time) }}</span>
												<span v-if="item.status != 2"
													style="margin: 0 20rpx 0 0">{{ dateWeeks(item.sign_start_time) }}
												</span> -->
												<span v-if="item.status == 2"
													style="margin: 0 20rpx">{{ dateWeeks(item.start_time) }}</span>
												<span v-if="item.status != 2"
													style="margin: 0 20rpx 0 0">{{ dateWeeks(item.start_time) }}
												</span>
												<span style="width: 1rpx;height: 22rpx;background: #323232;"></span>
												<span class="white-space" style="margin-left: 20rpx; width: 260rpx">{{
														item.address_detail }}</span>

											</view>
										</view>

										<view class="flex align-items justify-center"
											style="gap: 16rpx;width: 100%;margin-top: 20rpx;margin-bottom: 32rpx;">

											<view class="imgs_con_div"
												v-for="(items_img, index) in item.images.slice(0, 3)" :key="index">
												<image class="imgs_con" :src="items_img" mode="aspectFill"></image>
											</view>
										</view>





									</view>
								</view>
								<view
									style="display: flex;align-items: center;justify-content: flex-end;gap: 20rpx;width: 96%;margin-bottom: 30rpx;"
									v-if="item.auth_status == 0">
									<view class="part1 flex justify-center align-items" v-if="item.auth_status == 0 "
										@click.stop="editItem(item.id)"> 修改 </view>
								</view>
								<!-- <view
									style="display: flex;align-items: center;justify-content: flex-end;gap: 20rpx;width: 96%;margin-bottom: 30rpx;"
									v-if="item.auth_status == 1 && item.status == -1">
									<view class="part flex justify-center align-items"
										@click.stop="editItem(item.id)"> 重发 </view>
										<view class="part flex justify-center align-items"
											@click.stop="editItem(item.id)"> 详情 </view>
								</view> -->
								<view class="bottom flex align-items" v-if="item.auth_status != 0"
									style="justify-content: space-between;padding: 0rpx 20rpx;">
									<view v-if="item.status == 1" style="width: auto;">
										<image src="/static/index/dian.png"
											style="width: 60rpx;height: 60rpx;vertical-align: middle;"></image>
										<text
											style="margin-left: 20rpx;vertical-align: middle;font-size: 26rpx;">未开始</text>
									</view>
									<view v-if="item.status == 2" style="display: flex;align-items: center;">
										<u-avatar-group :urls="item.join_info.users" keyName="avatar" size="30"
											maxCount="1" gap="0.4"></u-avatar-group>
										<image src="/static/index/dian.png" style="
									    width: 60rpx;
									    height: 60rpx;
									    margin-left: -20rpx;
									  "></image>
										<view style="margin-left: 20rpx;vertical-align: middle;font-size: 24rpx;">
											{{ Number(item.join_info.people_number) + "人已上车" }}
										</view>
									</view>
									<!-- <view v-if="item.auth_status != 2 || item.status != 1" style="font-size: 26rpx; width: 24%;"> -->
									<view v-if="item.status == 3 ">
									</view>
									<view v-if="item.status == 4 || item.status == 5"
										style="font-size: 26rpx; width: 24%;">
										<text>核销：</text>
										<text style="color: orangered;">{{ item.verification_num }}</text>
										<text>/{{ item.stock }}</text>
									</view>
									<view v-if="item.status == 4">
									</view>
									<view v-if="item.status == -1 && item.auth_status == 1">

									</view>
									<view
										style="display: flex;align-items: center;justify-content: flex-end;gap: 20rpx;">
										<view v-if="item.status == 3 || item.status == 4"
											class="part1 flex justify-center align-items" @click.stop="toHexiao"> 核销
										</view>
										<view class="part flex justify-center align-items" v-if="item.auth_status == 0"
											@click.stop="editItem(item.id)"> 修改 </view>
										<view class="part1 flex justify-center align-items" v-if="item.auth_status == 1 && item.status == 1"
											@click.stop="editItem(item.id)"> 修改 </view>
										<view class="part1 flex justify-center align-items"
											v-if="item.status == -1 && item.auth_status == 1"
											@click.stop="copyNewItem(item.id)"> 重发 </view>
										<view class="part flex justify-center align-items"
											v-if="item.auth_status != 2  " @click.stop="detail(item.id)"> 详情 </view>
									</view>
									<!-- <view style="display: flex;align-items: center;justify-content: flex-end;gap: 20rpx;width: 1005;">
										<view class="part flex justify-center align-items" v-if="item.auth_status == 0 || item.status == 1"
											@click.stop="editItem(item.id)"> 重发 </view>
										<view class="part flex justify-center align-items" @click.stop="detail(item.id)"> 详情 </view>
									</view> -->
								</view>

								<view style="padding: 30rpx;" v-if="item.auth_status == 2 && item.status == -1">
									<view
										style="height: 160rpx;color: #323232;font-size: 26rpx;;padding: 20rpx;background: #F7F7F7;border: 1rpx solid #C1C1C1;border-radius: 18rpx 18rpx 18rpx 18rpx;">
										未通过原因：{{item.reason}}
									</view>
								</view>

								<view
									style="display: flex;align-items: center;justify-content: flex-end;gap: 20rpx;width: 96%;margin-bottom: 30rpx;"
									v-if="item.auth_status == 2 && item.status == -1">
									<view class="part1 flex justify-center align-items"
										v-if="item.auth_status == 2 && item.status == -1"
										@click.stop="cancelsOpen(item.id)"> 删除 </view>
									<view class="part flex justify-center align-items"
										v-if="item.auth_status == 2 && item.status == -1"
										@click.stop="copyItem(item.id)"> 重发 </view>
								</view>
							</view>
							<view v-if="status == -2" @click.stop="detail(item.activity_id)">
								<view class="flex" style="margin-top: 30rpx">
									<view class="right flex flex-column"
										style="align-items: self-start;padding-left: 15px;">
										<view class="flex flex-column">
											<view class="time flex align-items white-space">
												<span style="color: #9C9C9C;">活动时间：{{ dateWeeks(item.detail.sign_start_time)
													}}</span>
											</view>
										</view>
										<view
											style="height: 1px;width: 98%;background-color: #eeeeee;margin: 30rpx 0px;">
										</view>
										<view class="flex align-items" style="width: 100%;">
											<view>
												<image
													style="height: 130rpx;width: 130rpx;object-fit: cover;border-radius: 8rpx"
													:src="item.detail.images[0]"></image>
											</view>
											<view class="title white-space" style="padding-left: 20rpx;">
												<view>{{ item.detail.title }}</view>
												<view
													style="display: flex;justify-content: space-between;align-items: center;margin-top: 30rpx;padding-right: 30rpx;">
													<view>
														<text style="font-weight: 400;">退款金额：</text>
														<text v-if="server_status==3"
															style="color: #FF4810;font-weight: 600;">￥{{ item.first_refundprice }}</text>
														<text v-if="server_status==6"
															style="color: #FF4810;font-weight: 600;">￥{{ item.real_refundprice }}</text>
													</view>
													<view v-if="item.before_status==-3"
														style="font-size: 26rpx;color: #9C9C9C;">数量：{{ item.num }}
													</view>
													<view v-if="item.before_status!=-3"
														style="font-size: 26rpx;color: #9C9C9C;">数量：{{ item.auth_num }}
													</view>
												</view>
											</view>
										</view>
										<view
											style="height: 1px;width: 98%;background-color: #eeeeee;margin:30rpx 0px 0rpx 0px;">
										</view>
									</view>
								</view>
								<view class="flex align-items" style="justify-content:space-between;padding: 30rpx">
									<view style="font-size: 28rpx;color: #323232;">
										<view v-if="item.auth_status == 1">
											已同意退款
										</view>
										<view v-if="item.auth_status == 2">
											已驳回
										</view>
									</view>
									<view
										style="font-weight: 600;font-size: 28rpx;width: 180rpx;height: 70rpx;text-align: center;line-height: 70rpx;background: linear-gradient( 270deg, #FBF66D 0%, #9CEAA2 100%);border-radius: 276rpx;"
										@click.stop="goAfterInfo(item.id)"> 查看详情 </view>
								</view>
							</view>
						</view>

						<view class="flex flex-column flex-start align-items"
							v-if="hotList.length == 0 && selected != 15" style="margin-top: 300rpx;">
							<image src="/static/message/activen.png" mode="" style="width: 180rpx;height: 180rpx;">
							</image>
							<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无发布活动</view>
							<view class="tofb" @click="tofb">前往发布</view>
						</view>
						<!-- <view class="flex flex-column flex-start align-items" v-if="hotList.length == 0"
							style="margin-top: 300rpx;">
							<image src="/static/message/activen.png" mode="" style="width: 180rpx;height: 180rpx;">
							</image>
							<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无发布活动</view>
							<view class="tofb" @click="tofb">前往发布</view>
						</view> -->

					</view>
				</view>
			</view>








		</view>
		<!-- 热门活动 -->

		<!-- <u-popup :show="show" mode="center" :zIndex="99999" :custom-style="popupStyle" closeable="true" @close="close"
			@open="open">
			<view class="popup flex flex-column align-items">
				<view class="pop-center flex">
					<view class="left">
						<image :src="qrcode.detail.headimage" mode="" style="width: 280rpx;height: 200rpx;"></image>
					</view>
					<view class="right flex flex-column">
						<span class="title white-space">{{qrcode.detail.title}}</span>
						<span class="hui"
							style="margin: 16rpx 0 6rpx 0;">课时:{{qrcode.detail.verification_num}}/{{qrcode.detail.limit_num}}</span>
						<span class="hui">日期:{{qrcode.detail.classes_date_text}}</span>
						<span class="hui" style="margin: 6rpx 0 6rpx 0;">时间:{{qrcode.detail.classes_time_text}}</span>
						<span class="hui" v-if="qrcode && qrcode.detail" style="margin-top: 6rpx;">开始时间:{{ formatTimestamp(qrcode.detail.start_time) }}</span>
						<span class="hui" v-if="qrcode && qrcode.detail" style="margin: 6rpx 0 6rpx 0;">结束时间:{{ formatTimestamp(qrcode.detail.end_time) }}</span>
						<span class="hui">地址:{{qrcode.detail.address_detail}}</span>
					</view>
				</view>
				<span class="line-short"></span>
				<image :src="qrcode.codeimage" mode="" style="width: 376rpx;height: 376rpx;"></image>
				<span style="margin: 24rpx 0 64rpx 0;">核销二维码</span>
			</view>
		</u-popup> -->

		<!-- <u-loadmore :status="loadStatus" /> -->


		<!-- 二次确认弹窗 -->
		<u-popup :show="cancelsShow" mode="center" :round="10" :zIndex="99999" :custom-style="popupCancelStyle"
			@close="cancelsClose" @open="cancelsOpen" :safeAreaInsetBottom="false" :closeable="false">
			<view class="popupBox flex justify-start align-items flex-column">
				<view class="pop-header flex align-items flex-column flex-start">
					<span class="name white-space">是否确认删除活动</span>
					<span class="price">
						删除活动后，若再次举办活动，需重新设置活动内容，并提交平台审核
					</span>
					<!-- <image src="../../static/center/buy.png" mode="" style="width: 168rpx; height: 48rpx;">
				</image> -->
				</view>

				<view class="popup-footer flex ">
					<span @click="cancelsClose" class="span1">取消</span>
					<span @click="deleteItem()">确认删除</span>
					<!-- <image src="../../static/center/price.png" mode="" style="width: 642rpx;height: 80rpx;"></image> -->
					<!-- <u-loading-icon :vertical="true" v-if="uloadingShow"></u-loading-icon> -->
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import {
		dateWeek
	} from "../../utils/dateFormat";
	import dayjs from 'dayjs';
	export default {
		data() {
			return {
				popupStyle: {
					width: "690rpx",
					margin: "0 auto", // 水平居中
					display: "flex",
					justifyContent: "center",
					alignItems: "center",
					padding: "0",
					borderRadius: "20rpx",
				},
				popupCancelStyle: {
					width: '640rpx',
					height: '414rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center',
					borderRadius: '50rpx'
				},
				sortList: [{
						text: "按照价格排序",
						value: ["acs", "desc"],
					},
					{
						text: "按照编号排序",
						value: ["acs", "desc"],
					},
				],
				size: 13,
				sortStyle: ["#ff557f", "#3f3f3f"],
				headers: [{
						status: "15",
						name: "草稿箱",
					}, {
						status: "0",
						name: "活动审核",
					},
					{
						status: "1",
						name: "未开始",
					},
					{
						status: "2",
						name: "报名中",
					},
					{
						status: "3",
						name: "待开始",
					},
					{
						status: "4",
						name: "进行中",
					},
					{
						status: "5",
						name: "已结束",
					},
					{
						status: "-1",
						name: "已取消",
					},
					{
						status: "-2",
						name: "售后订单",
					}
				],
				selected: "0",
				page: 1,
				limit: 10,
				loadStatus: "loading",
				keywords: "",
				list: [],
				show: false,
				qrcode: "",
				count: 0,
				// 活动列表
				hotList: [],
				// 审核状态
				auth_status: 0,
				status: 0,
				server_status: '3',
				cancelsShow: false, //取消的二次弹框显示
				deleteId: null, //暂存删除活动的id
				draftList: [], //草稿箱列表
				draftShow: false,
				draftCount: 0,
			};
		},
		onLoad(option) {
			this.list = []
			this.page = 1
			this.draftList = []
			// if (option?.status) {
			// 	this.selected = option.status
			// 	console.log('11');
			// 	this.getHotList(option.status);
			// } else {
			// 	console.log('22');
			// 	this.getHotList('0');
			// }

		},
		onShow() {
			this.list = []
			this.page = 1
			this.hotList = []
			this.draftList = []
			console.log('hotList', this.hotList);
			// this.getHotList(this.selected);
			if (this.status == -2) {
				this.getAfterList();
			} else if (this.status == 15) {
				this.getDraftList('15')
			} else {
				this.getHotList(this.selected);
			}
		},
		onPullDownRefresh() {
			uni.showLoading({
				title: '加载中...'
			});
			this.page = 1;
			this.hotList = [];
			if (this.status == -2) {
				this.getAfterList();
			} else {
				this.getHotList(this.selected);
			}
			setTimeout(() => {
				uni.hideLoading();
				uni.stopPullDownRefresh();
			}, 2000)
		},
		onReachBottom() {
			console.log('onReachBottom',this.status);
			// if (this.list.length < this.count) {
			// 	this.page++;
			// 	this.getList(this.selected);
			// }
			if (this.status == -2) {
				this.page++;
				this.getAfterList();
			}else if(this.status == 15) {
				if(this.draftCount > this.draftList.length){
					this.page++;
					this.getDraftList();
				}
				
			} else {
				this.page++;
				this.getHotList(this.selected);
			}
		},

		methods: {
			goAfterInfo(id) {
				uni.navigateTo({
					url: "/packageA/afterSales/info?id=" + id,
				});
			},
			//待审核+未开始的跳转修改活动
			editItem(id) {
				const type = 0;
				uni.navigateTo({
					url: "/packageB/editAct?id=" + id + '&type=' + type,
				});
				// uni.switchTab({
				// 	url:"/pages/center/index?id=" + id,
				// })//跳转tabbar页面的
			},
			//未通过的跳转重发活动
			copyItem(id) {
				const type = 1
				uni.navigateTo({
					url: "/packageB/editAct?original_activity_id=" + id + '&type=' + type,
				});
			},
			//已取消重发活动
			copyNewItem(id) {
				const type = 2
				uni.navigateTo({
					url: "/packageB/editAct?original_activity_id=" + id + '&type=' + type,
				});
				// uni.switchTab({
				// 	url:"/pages/center/index?id=" + id,
				// })//跳转tabbar页面的
			},
			//未通过的取消按钮实际上删除按钮
			deleteItem() {
				console.log('deleteItem', this.deleteId);
				uni.$u.http.post('/api/school.new_activity/del', {
					id: this.deleteId,
				}).then(res => {
					if (res.code == 1) {
						this.cancelsShow = false
						this.deleteId = null
						uni.showToast({
							title: '删除成功',
							icon: 'success',
							duration: 2000
						})
						// this.getHotList('0');
						this.toShlist('2')
						// setTimeout(() => {
						// 	uni.redirectTo({
						// 		url: "/packageA/my/orderList"
						// 	})
						// }, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			// 取消弹窗的显示
			cancelsOpen(id) {
				this.cancelsShow = true
				if (!this.deleteId) {
					this.deleteId = id
				}
				// this.deleteId = id
				console.log('cancelsOpen', this.deleteId);
			},
			cancelsClose() {
				this.cancelsShow = false
				this.deleteId = ""
			},
			// 跳转详情
			detail(id) {
				uni.navigateTo({
					url: "/packageA/center/detailSys?id=" + id,
				});
			},
			tofb() {
				uni.switchTab({
					url: '/pages/center/index',
				});
			},
			//开始
			dateWeeks(e) {
				return dateWeek(e);
			},
			// 获取售后订单
			getAfterList() {
				var auth_status = '';
				var server_status = this.server_status;
				var status = '4,5,6'
				//-1为拒绝退款
				if (this.server_status == -1) {
					auth_status = '2';
					server_status = '6';
					status = "4,5,6,7";
				} else {

				}
				uni.$u.http
					.get("/api/school.newworker.activity.order/order_list", {
						params: {
							page: this.page,
							limit: 20,
							server_status: server_status,
							status: status,
							auth_status: auth_status,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							const list = res.data.list || [];
							this.hotList.push(...list);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},
			// 获取我发布的活动
			getHotList(val) {
				console.log('跳转接口', val, this.auth_status);
				var auth_status = '';
				if (val == 0) {
					if (this.auth_status == 0) {
						val = '1,2,3,4,5';
					} else {
						val = '1,2,3,4,5,-1';
					}
					// val = '1,2,3,4,5';
					auth_status = this.auth_status;
					console.log('val==0', this.auth_status);
				} else {
					if (val == '-1') {
						// auth_status = '0,1,2';
						auth_status = '0,1';
					} else {
						auth_status = '1';
					}
				}
				//let auth_status = val == '0' ? 0 : '1';
				//let vals = val == '0' ? '' : val;
				uni.$u.http
					.get("/api/school.new_activity/activity_list", {
						params: {
							page: this.page,
							limit: 20,
							order: "normal",
							my: 1,
							auth_status: auth_status,
							status: val, // 5已结束
						},
					})
					.then((res) => {
						if (res.code == 1) {
							const list = res.data.list.data || [];
							this.hotList.push(...list);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},
			formatTimestamp(timestamp) {
				const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, "0");
				const day = String(date.getDate()).padStart(2, "0");
				const hours = String(date.getHours()).padStart(2, "0");
				const minutes = String(date.getMinutes()).padStart(2, "0");
				const seconds = String(date.getSeconds()).padStart(2, "0");
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			},
			// 关闭弹窗
			close() {
				this.show = false;
				// this.qrcode = ''
			},
			open() {
				this.show = true;
			},
			againBuy(id) {
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id,
				});
			},
			toHexiao() {
				// 小程序内调用扫码API
				let that = this;
				wx.scanCode({
					success(res) {
						if (res.path) { // 扫描的是小程序码
							const sceneValue = decodeURIComponent(res.path.split('=')[1]);
							that.navigateToVerifyPage(sceneValue);
						} else if (res.result.startsWith('https://wxaurl.cn')) { // 扫描短链接
							that.navigateToVerifyPage(res.result);
						}
					}
				});
			},

			// 跳转核销页面并解密场景值
			navigateToVerifyPage(e) {
				console.log(e);
				let urlParams = this.parseQueryString(e.split('?')[1]);
				console.log(urlParams);
				uni.navigateTo({
					url: '/packageA/my/cancelActivity?code=' + urlParams.code + '&id=' + urlParams.id
				});
			},
			parseQueryString(query) {
				return query.split('&').reduce((obj, pair) => {
					const [key, value] = pair.split('=');
					obj[key] = decodeURIComponent(value || ''); // 处理空值和URI编码
					return obj;
				}, {});
			},
			// 取消支付
			Cancel(order_no) {
				console.log(order_no);
				uni.$u.http
					.post("/api/school/order/cancel", {
						order_no: order_no,
					})
					.then((res) => {
						if (res.code == 1) {
							this.list = [];
							this.getList(0);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {});
			},


			// 售后
			service(id) {
				uni.navigateTo({
					url: "/packageA/afterSales/index?id=" + id,
				});
			},
			// 核销码
			QR(id) {
				uni.$u.http
					.get("/api/school/order/detail", {
						params: {
							id: id,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.qrcode = res.data.detail;
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {});
				this.show = true;
			},
			switchSort(index, value) {
				console.log(index, value);
			},
			// 搜索
			search() {
				const that = this;
				that.page = 1;
				that.list = [];
				this.getList(this.selected);
			},
			// 获取课程列表
			getList(status) {
				// if (this.loadStatus === 'nomore') return;
				uni.$u.http
					.get("/api/school/order/order_list", {
						params: {
							keywords: this.keywords,
							page: this.page,
							limit: this.limit,
							status: status,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.count = res.data.count;
							this.list = [...this.list, ...res.data.list];
							if (this.list.length >= res.data.count) {
								this.loadStatus = "nomore";
							} else {
								this.loadStatus = "loading";
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
							this.loadStatus = "loading";
						}
					})
					.catch((error) => {
						console.error("请求失败", error);
						this.loadStatus = "loading";
					});
			},
			selectheader(index) {
				console.log(index);
				var status = index.status;
				this.selected = status;
				this.page = 1;
				this.hotList = [];
				this.status = status;
				if (status == -2) {
					this.getAfterList();
				} else if (status == 15) {
					console.log('draftShow');
					this.draftShow = true
					this.hotList = []
					this.draftList = []
					this.getDraftList(status)
				} else {
					this.getHotList(status);
				}
			},
			//获取草稿箱列表
			getDraftList(status) {
				uni.$u.http
					.get("/api/school.newactivity.activity_drafts/drafts_list", {
						params: {
							keywords: this.keywords,
							page: this.page,
							limit: this.limit,
							order: 'normal'
							// status: status,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.draftCount = res.data.count;
							console.log('draft1', res.data.list);
							const list = res.data.list.data || [];
							this.draftList.push(...list);
							
							// this.draftList = [...this.draftList,...res.data.list.data];
							console.log('draft', this.draftList);
							for (let i = 0; i < this.draftList.length; i++) {
								this.draftList[i].createTime = dayjs.unix(this.draftList[i].createtime).format(
									'YYYY-MM-DD HH:mm')
							}
							if (this.draftList.length >= res.data.count) {
								this.loadStatus = "nomore";
							} else {
								this.loadStatus = "loading";
							}
							console.log('draft', this.draftList);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
							this.loadStatus = "loading";
						}
					})
					.catch((error) => {
						console.error("请求失败", error);
						this.loadStatus = "loading";
					});
			},
			toEditDraft(id) {
				console.log('编辑草稿');
				const type = 3
				uni.navigateTo({
					url: '/packageB/editDraft?draftId=' + id + '&type=' + type
				})
			},
			toDeleteDraft(id) {
				console.log('删除草稿');
				console.log('deleteItem', id);
				uni.$u.http.post('/api/school.newactivity.activity_drafts/del', {
					ids: id,
				}).then(res => {
					if (res.code == 1) {
						this.cancelsShow = false
						uni.showToast({
							title: '删除成功',
							icon: 'success',
							duration: 1000
						})
						this.getDraftList2('15')
						// this.toShlist('2')
						// setTimeout(() => {
						// 	uni.redirectTo({
						// 		url: "/packageA/my/orderList"
						// 	})
						// }, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 1000
						})
					}
				}).catch(error => {});
			},
			//获取草稿箱列表
			getDraftList2(status) {
				uni.$u.http
					.get("/api/school.newactivity.activity_drafts/drafts_list", {
						params: {
							keywords: this.keywords,
							page: 1,
							limit: this.limit,
							order: 'normal'
							// status: status,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.count = res.data.count;
							console.log('draft1', res.data.list);
							// const list = res.data.list.data || [];
							// this.draftList.push(...list);
							this.draftList = res.data.list.data;
							console.log('draft', this.draftList);
							for (let i = 0; i < this.draftList.length; i++) {
								this.draftList[i].createTime = dayjs.unix(this.draftList[i].createtime).format(
									'YYYY-MM-DD HH:mm')
							}
							if (this.draftList.length >= res.data.count) {
								this.loadStatus = "nomore";
							} else {
								this.loadStatus = "loading";
							}
							console.log('draft', this.draftList);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
							this.loadStatus = "loading";
						}
					})
					.catch((error) => {
						console.error("请求失败", error);
						this.loadStatus = "loading";
					});
			},
			headerSelected(status) {
				return this.selected === status;
			},
			//待审核和未通过的状态判断和传参
			toShlist(val) {
				this.page = 1;
				this.hotList = [];
				this.auth_status = val
				console.log('toShlist', val, this.auth_status, this.status);
				this.getHotList(this.status);
			},
			//待售后的状态判断和获取列表
			toAfter(val) {
				this.page = 1;
				this.hotList = [];
				this.server_status = val;
				this.getAfterList();
			}
		},
	};
</script>

<style lang="scss" scoped>
	.box {
		.con-center {
			width: 100%;
			// height: 100vh;
			// margin-top: 25rpx;
			// overflow: hidden;
			background: #f7f7f7;

			.tabs {
				background: #ffffff;
				font-family: PingFang SC, PingFang SC;
				position: fixed;
				top: 0;
				z-index: 1000;
			}

			.shenhe {
				width: 94%;
				position: fixed;
				top: 80rpx;
				//background: #f7f7f7;
				background: #f7f7f7;
				z-index: 100;
				padding: 30rpx 0rpx 25rpx 0rpx;

				.span_s {
					width: 134rpx;
					height: 60rpx;
					line-height: 60rpx;
					background: #FFFFFF;
					border-radius: 125rpx;
					color: #9C9C9C;
					text-align: center;
					font-size: 26rpx;

				}

				.span_s.active {
					color: #3D3D3D;
				}
			}




		}
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		margin-top: 12rpx;
		width: 690rpx;
		height: 1rpx;
		background: #008cff;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.centerBox {
		width: 690rpx;

		.box-line {
			width: 400rpx;
			height: 1rpx;
			background: #d9d9d9;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			margin-top: 24rpx;
		}

		.center {
			margin: 32rpx 0 32rpx 0;
		}

		.rightBox {
			width: 50%;

			.line-row {
				width: 382rpx;
				height: 1rpx;
				background: #d9d9d9;
				box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
				margin: 14rpx 0 6rpx 0;
			}

			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #343434;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				/* 设置行数 */
				overflow: hidden;
				text-overflow: ellipsis;
				word-break: break-all;
				margin-bottom: 24rpx;
				/* 防止单词被截断 */
			}

			.minge {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #7a7a7a;
				margin-bottom: 8rpx;
			}

			.money {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #ff2323;

				span {
					font-weight: 500;
					font-size: 24rpx;
					color: #7a7a7a;
				}
			}

			.Cancel {
				width: 138rpx;
				height: 48rpx;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 2rpx solid #008cff;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #008cff;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
			}

			.make {
				width: 138rpx;
				height: 48rpx;
				background: #008cff;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #ffffff;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-left: 24rpx;
			}

			.QR {
				width: 138rpx;
				height: 48rpx;
				background: #4974ff;
				box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #eaeaea;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-top: 50rpx;
			}
		}
	}

	.charge {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 28rpx;
		color: #ff2323;
		line-height: 32rpx;
	}

	.search {
		margin-top: 24rpx;
		width: 690rpx;
		height: 64rpx;
		background: #ffffff;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008cff;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008cff;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #c0c0c0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.searchBtn {
			width: 128rpx;
			height: 64rpx;
			background: #008cff;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #ffffff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.popup {
		.header {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 42rpx;
			color: #008cff;
			margin-top: 34rpx;
			width: 690rpx;
		}

		.line-row {
			width: 690rpx;
			height: 1rpx;
			background: #d9d9d9;
			margin: 11rpx 0 31rpx 0;
		}

		.pop-center {
			margin-top: 50rpx;
			align-items: center;
			width: 650rpx;

			.left {
				image {
					border-radius: 12rpx;
				}
			}

			.right {
				margin-left: 30rpx;

				.title {
					width: 340rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
				}
			}
		}

		.line-short {
			width: 400rpx;
			height: 1rpx;
			background: #d9d9d9;
			margin: 24rpx 0;
		}

		.popList {
			justify-content: space-between;
			width: 600rpx;
			margin-top: 32rpx;

			.hei {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #343434;
			}
		}

		.pop-btn {
			width: 690rpx;
			margin-top: 62rpx;
			justify-content: space-around;

			.Cancel {
				width: 306rpx;
				height: 80rpx;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				border: 2rpx solid #008cff;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #008cff;
			}

			.Confirm {
				width: 306rpx;
				height: 80rpx;
				background: #008cff;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #ffffff;
			}
		}
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7a7a7a;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.invoiceList {
		width: 100%;
		height: auto;
		display: grid;
		justify-content: center;

		.invoiceList-item {
			display: grid;
			justify-content: space-between;
			align-items: center;
			background-color: #ffffff;
			// width: 100%;
			width: 670rpx;
			margin-bottom: 20rpx;
			// margin-left: 10rpx;
			padding: 20rpx 30rpx;
			height: 220rpx auto;
			border-radius: 30rpx;

			.invoiceList-itemInfo {
				// width: 100%;
				width: 670rpx;
				display: flex;
				// justify-content: flex-start;
				align-items: center;

				.item-img {
					width: 170rpx;
					height: 170rpx;
					// margin-left: 40rpx;

				}

				.item-con {
					margin-left: 20rpx;
					width: 100%;
					height: 160rpx;
					position: relative;
					color: #323232;
					display: grid;

					.itenCon-actName {
						// position: absolute;
						// top: 0;
						font-size: 28rpx;
						font-weight: 600;
					}

					.itenCon-actCon {
						// position: absolute;
						// top: 50rpx;
						// margin-top: 60rpx;
						width: 400rpx;
						white-space: nowrap;
						/* 禁止换行 */
						overflow: hidden;
						/* 隐藏超出部分 */
						text-overflow: ellipsis;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actPrice {
						// position: absolute;
						// bottom: 0;
						color: #9c9c9c;
						font-size: 26rpx;
						font-weight: 400;
					}
				}
			}



			.item-draftBtns {
				// width: 100%;
				width: 670rpx;
				display: flex;
				justify-content: flex-end;
				align-items: center;
				margin-top: 20rpx;
			}

		}
	}

	.hot {
		margin-top: 80rpx;
		margin-bottom: 70rpx;
		min-height: 100vh;
		padding: 0rpx 30rpx;

		.hot-top {

			// padding-bottom: 12rpx;
			// border-bottom: 2rpx solid red;
			.left {
				background-image: url("/static/footer.png");
				background-repeat: no-repeat;
				background-position: bottom;

				span {
					width: 160rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 40rpx;
					color: #222222;
					line-height: 47rpx;
				}
			}

			.more {
				width: 104rpx;
				height: 40rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #222222;
				line-height: 30rpx;
			}
		}
	}

	.wsh_hot {
		margin-top: 175rpx;
	}

	.content {

		// height: 462rpx;
		// overflow-x: auto;
		/* 允许横向滚动 */
		// white-space: nowrap;
		.bbb {
			background: #ffffff;
			margin-top: 24rpx;
			border-radius: 36rpx;
		}

		.right {
			width: 100%;
			// padding: 20rpx 0;

			.title {
				width: 93%;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #323232;
			}

			.first-image {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #9c9c9c;

				span {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #222222;
				}
			}

			.row {
				margin: 10rpx 0 26rpx 0;

				.remen {
					// width: 136rpx;
					height: 40rpx;
					background: #ebfaf5;
					border-radius: 4rpx 4rpx 4rpx 4rpx;

					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24rpx;
					color: #14bc84;
					line-height: 28rpx;
					padding: 2rpx 8rpx;
				}

				.line-colum {
					width: 1rpx;
					height: 32rpx;
					background: #7a7a7a;
					margin: 0 13rpx;
				}

				.name {
					width: 110rpx;
					height: 32rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 26rpx;
					color: #7a7a7a;
					line-height: 32rpx;
					margin-left: 12rpx;
				}
			}

			.jigou {
				width: 312rpx;
				// height: 32rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #7a7a7a;
				margin: 4rpx 0 14rpx 0;
			}

			.time {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #323232;
				// line-height: 30rpx;

				.time_tex {
					width: 116rpx;
					height: 42rpx;
					background: #bbfc5b;
					border-radius: 159rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #323232;
					display: flex;
					justify-content: center;
					align-items: center;
					line-height: 30rpx;
				}
			}

			.imgs_con_div {
				width: 202rpx;
				height: 202rpx;
				overflow: hidden;

				&:first-child {
					border-radius: 18rpx 0 0 18rpx;
				}

				&:nth-child(3) {
					border-radius: 0 18rpx 18rpx 0;
				}

				.imgs_con {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
		}

		.bottom {
			margin: 0rpx 0rpx 30rpx 10rpx;
			// margin: 0rpx 0rpx 0rpx 10rpx;

			.number {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #3d3d3d;
				line-height: 30rpx;
				width: 150rpx;
				margin-left: 20rpx;
			}

			.text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 26rpx;
				color: #7a7a7a;
			}

			.money {
				width: 150rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 36rpx;
				color: #ff2323;
				justify-content: flex-end;
			}
		}

		.ovr {
			width: 312rpx;
			background: #ffffff;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 18rpx;
			margin-right: 24rpx;
		}
	}

	.part {
		width: 168rpx;
		height: 70rpx;
		background: url(@/static/index/shangche.png);
		background-size: 168rpx 70rpx;
		background-repeat: no-repeat;
		font-family: "YouSheBiaoTiHei";
		font-weight: 400;
		font-size: 42rpx;
		color: #bbfc5b;
	}

	.part1 {
		width: 168rpx;
		height: 70rpx;
		background: url(@/static/index/hexiao.png);
		background-size: 168rpx 70rpx;
		background-repeat: no-repeat;
		font-family: "YouSheBiaoTiHei";
		font-weight: 400;
		font-size: 42rpx;
		color: #323232;
	}

	.tofb {
		margin-top: 50rpx;
		width: 280rpx;
		height: 70rpx;
		background: #323232;
		border-radius: 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 500;
		font-size: 32rpx;
		color: #BBFC5B;
		display: flex;
		align-items: center;
		justify-content: center;
	}


	.popupBox {
		width: 640rpx;
		height: 414rpx;
		background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/confirm2.png");
		background-size: 100%;
		background-repeat: no-repeat;
		border-radius: 44rpx;

		.pop-header {
			width: 100%;

			background-repeat: no-repeat;
			background-position: left bottom;
			height: 414rpx;
			margin-top: 80rpx;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}

			.name {
				width: 288rpx;
				height: 36rpx;
				font-family: PingFang SC Bold, PingFang SC Bold;
				font-weight: 800;
				font-size: 36rpx;
				color: #202020;
				line-height: 36rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.price {
				width: 520rpx;
				height: 68rpx;
				margin-top: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #202020;
				line-height: 34rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			width: 594rpx;
		}

		.popup-footer {
			position: absolute;
			left: 75rpx;
			bottom: 60rpx;

			span {
				width: 230rpx;
				height: 90rpx;
				background: #323232;
				border-radius: 200rpx 200rpx 200rpx 200rpx;
				font-family: PingFang SC Regular, PingFang SC Regular;
				font-weight: 400;
				font-size: 32rpx;
				color: #BBFC5B;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.span1 {
				background: rgba(193, 193, 193, 0.22);
				color: #202020;
				margin-right: 30rpx;
			}

		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #F0F0F0;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}



		.selectTime.selected {
			width: 288rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;

		}
	}
</style>