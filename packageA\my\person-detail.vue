<template>
	<view class="content">
		<!-- <image src="../../static/my/backImage.png" mode="" class="backImg"></image> -->
		<view class="container">
			<view class="card">
				<view class="list">
					<view class="list-item" @click="imageShow=true">
						<view>
							头像
						</view>
						<view class="right">
							<u-image :src="avatar" shape="circle" width="100rpx" height="100rpx"></u-image>
							<view style="margin-left: 10px;">
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
					</view>
					<u-popup :show="imageShow" mode="center" closeable :closeOnClickOverlay="false" @close="imageShow=false" :custom-style="popupStyRe">
						<view class="popup_box">
							<view class="popup_title">更换图片</view>
							<button class="popup_button" type="default" plain="true" open-type="chooseAvatar"
								@chooseavatar="chooseavatar">
								<u-image shape="circle" width="140rpx" height="140rpx" :src="upAvatar" mode="aspectFill"></u-image>
							</button>
							<view style="width: 80% !important;position: relative;top: 20rpx;margin-top: 60rpx;">
								<u-button shape="circle" @click="confirmImage" :customStyle="avatarShowStyle">
									完成
								</u-button>
							</view>
						</view>
					</u-popup>
					
					<!-- <view class="list-item" @click="setAvator">
						<view class="left">
							头像设置
						</view>
						<view class="right" >
							<view>
								<u-avatar :src="avatar" shape="circle" size="35"></u-avatar>
							</view>
							<view>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
					</view> -->
					<view class="list-item"  @click="toName('nc')">
						<view class="left">
							昵称设置
						</view>
						<view class="right">
							<view>
								<text>{{ niName }}</text>
							</view>
							<view>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
					</view>
					<view class="list-item"  @click="toName('qm')">
						<view class="left">
							签名
						</view>
						<view class="right">
							<view>
								<text>{{ signature }}</text>
							</view>
							<view>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
					</view>
					<view class="list-item" @click="openUrl('/packageB/wallet/add_bank')">
						<view class="left">
							银行卡
						</view>
						<view class="right">
							<view>
								<text v-if="bankInfo.withdrawal == null"
									style="color: #9C9C9C;font-size: 26rpx;">未设置</text>
								<text v-if="bankInfo.withdrawal != null" style="font-size: 26rpx;">已认证</text>
							</view>
							<view><u-icon name="arrow-right"></u-icon></view>
						</view>
					</view>
					<!-- <view class="list-item" @click="openUrl('/packageB/invoice/invoiceCenter')">
						<view class="left">发票中心</view>
						<view class="right">
							<view><u-icon name="arrow-right"></u-icon></view>
						</view>
					</view> -->
					<view class="list-item" @click="openSfz()">
						<view class="left">
							身份信息
						</view>
						<view class="right">
							<view>
								<text v-if="sfInfo.status == -1" style="color: #9C9C9C;font-size: 26rpx;">未认证</text>
								<text v-if="sfInfo.status == 1" style="font-size: 26rpx;">已认证</text>
							</view>
							<view><u-icon name="arrow-right"></u-icon></view>
						</view>
					</view>
					<!-- <view class="list-item">
						<view class="left">
							银行卡
						</view>
						<view class="right">
							<u-icon name="arrow-right" color="#babdc7"></u-icon>
						</view>
					</view> -->


				</view>
			</view>
		</view>
		<view class="bottom">
			<u-button :customStyle="style" @click="out">退出账号</u-button>
			<!-- <u-button v-else :customStyle="style" @click="submit">保存</u-button> -->
		</view>
		<!-- <u-picker :show="show" :columns="columns" keyName='name' @confirm="confirm" @cancel="cancel"></u-picker>
		<u-picker :show="show1" :columns="columns1" keyName='name' @confirm="confirm1" @cancel="cancel"></u-picker>
		<u-picker :show="show2" :columns="columns2" keyName='name' @confirm="confirm2" @cancel="cancel"></u-picker>
		<u-datetime-picker ref="datetimePicker" :show="show3" v-model="value" mode="date" :formatter="formatter"
			@confirm="confirm3" @cancel="cancel" min-date="1970"></u-datetime-picker>
		<u-picker :show="show4" ref="uPicker" :loading="loading" :columns="columns3" @change="changeHandler"
			@cancel="cancel" @confirm="confirm4"></u-picker> -->

		<!-- <u-popup :show="show5" @close="close" :closeOnClickOverlay="false" closeable :customStyle="avatarStyle">
			<view class="avatar_choose">
				<view class="cards" @click="clickAvatar(0)">
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/img1.png" alt="" />
					<view class="choose_btn">
						更换
					</view>
				</view>
				<view class="cards" @click="clickAvatar(1)">
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/img2.png" alt="" />
					<view class="choose_btn">
						更换
					</view>
				</view>
				<view class="cards" @click="clickAvatar(2)">
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/img3.png" alt="" />
					<view class="choose_btn">
						更换
					</view>
				</view>
				<view class="cards" @click="clickAvatar(3)">
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/img4.png" alt="" />
					<view class="choose_btn">
						更换
					</view>
				</view>
			</view>
		</u-popup> -->
		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
	import {
		dateFormat,
		dateFormats
	} from '../../utils/dateFormat'
	// import {
	// 	address
	// } from '../../static/address'
	export default {
		data() {
			return {
				popupStyRe: {
					// width: '540rpx',
					padding: '24rpx 24rpx 24rpx 24rpx',
					// height: '384rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column',
					borderRadius: '30rpx'
				},
				baseUrl: 'https://naweigetetest2.hschool.com.cn/',
				typeop: '',
				avatarStyle: {
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'space-between'
				},
				// origiData: address,
				origiData: [],
				value: Number(new Date()),
				area: '',
				birthday: '',
				show: false,
				show1: false,
				show2: false,
				show3: false,
				show4: false,
				show5: false,
				loading: false,
				columns3: [],
				avatar: "https://cdn.uviewui.com/uview/album/1.jpg",
				upAvatar: '',
				titleStyle: {
					fontSize: '34rpx',
					fontWeight: 600
				},
				imageStyles: {
					width: 59,
					height: 59,
					border: {
						radius: '50%'
					}
				},
				style: {
					width: '690rpx',
					height: '90rpx',
					borderRadius: '148rpx',
					background: '#323232',
					fontSize: '36rpx',
					color: '#BBFC5B',
					lineHeight: '90rpx',
					border: 'none',
					fontFamily: "YouSheBiaoTiHei"
				},
				avatarShowStyle: {
					width: '400rpx',
					height: '90rpx',
					borderRadius: '148rpx',
					background: '#323232',
					fontSize: '36rpx',
					color: '#BBFC5B',
					lineHeight: '90rpx',
					border: 'none',
					fontFamily: "YouSheBiaoTiHei"
				},
				//昵称
				niName: '',
				//签名
				signature: '',
				realname: '',
				gender: '',
				// ages: '',
				zhiYe: '',
				phoneNumber: '',
				columns: [
					[{
							name: '男'
						},
						{
							name: '女'
						}
					]
				],
				columns1: [
					[{
							name: '18~28'
						},
						{
							name: '28~38'
						},
						{
							name: '38~48'
						}
					]
				],
				columns2: [
					[{
						name: '销售 / 客服',
					}, {
						name: '技术'
					}, {
						name: '产品 / 设计 / 运营'
					}, {
						name: '项目管理 / 项目质量'
					}, {
						name: '人力行政'
					}, {
						name: '财务 / 审计 / 税务'
					}, {
						name: '广告 / 设计 / 传媒 / 编辑'
					}, {
						name: '市场 / 公关'
					}, {
						name: '金融'
					}, {
						name: '生产制造'
					}, {
						name: '房地产 / 建筑 / 物业'
					}, {
						name: '采购 / 贸易 / 交通 / 物流'
					}, {
						name: '咨询 / 法律 / 教育 / 翻译'
					}, {
						name: '医疗 / 护理 / 生活服务'
					}, {
						name: '能源 / 矿产 / 环保'
					}, {
						name: '公务员 / 农林牧渔 / 其他'
					}, {
						name: '综合管理'
					}]
				],
				sfInfo: {},
				bankInfo: {},
				imageShow: false,
				profile:'',
			}
		},
		onLoad(options) {
			if (options) {
				this.typeop = options.type;
			}

			// this.$refs.datetimePicker.setFormatter(this.formatter)
			// this.handelData()
			// this.getDetail()
		},
		onShow() {
			const userInfo = uni.getStorageSync('userInfo')
			this.phoneNumber = userInfo.mobile
			this.niName = userInfo.nickname
			this.realname = userInfo.username
			this.avatar = userInfo.avatar
			this.signature = userInfo.bio
			this.getSfInfo();
			this.getBankList();
			this.getDetail();
		},
		methods: {
			confirmImage() {
				console.log('confirmImage11',this.profile);
				if(!this.profile){
					uni.showToast({
						title: "请选择图片",
						icon: "none"
					})
					return
				}
				
				console.log('confirmImage22');
				// this.userInfo.profile = this.profile
				// this.userInfo.avatar = this.profile
				
				this.upAvatar = this.profile;
				this.avatar = this.avatar;
				console.log(this.upAvatar);
				console.log(this.avatar);
				this.imageShow = false
				this.submit();
				
			},
			async chooseavatar(e) {
				console.log('chooseavatar',e,e.detail);
				// this.profile = this.uploadFilePromise(e.detail.avatarUrl, 'user');
				let result = await this.uploadFilePromise(e.detail.avatarUrl, 'user');
				console.log('chooseavatar2222',result,);
				this.profile = result
				this.upAvatar = this.baseUrl + this.profile
				this.avatar = this.profile;
				console.log('chooseavatar3333',this.profile,this.avatar,this.upAvatar);
				
			},
			uploadFilePromise(url) {
				console.log('uploadFilePromise',url);
				// console.log('接口地址', `${baseUrl}/common/upload`);
					console.log('上传-uploadFilePromise');
					// console.log('category', category)
					return new Promise((resolve, reject) => {
						let a = uni.uploadFile({
							url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', // 仅为示例，非真实的接口地址
							filePath: url,
							name: 'file',
							formData: {
								user: 'test',
								category: 'category'
							},
							header: {
								"token": uni.getStorageSync("token")
							},
							success: (res) => {
								console.log('uploadFilePromise成功',res.data);
								// setTimeout(() => {
								// 	resolve(JSON.parse(res.data).url);
								// }, 1000);
								var js = JSON.parse(res.data);
								console.log(js.data.errcode);
								if (js.data.errcode == '30002') {
									uni.showToast({
										title: '请登录...',
										icon: 'none',
										duration: 1000
									});
									setTimeout(() => {
										uni.switchTab({
											url: '/pages/my/index',
										})
									}, 1000)
									resolve('');
								}
								resolve(js.data.url);
							},
							fail: (err) => {
								reject(err);
							}
						});
				
						// console.log('');
					});
				// return new Promise((resolve, reject) => {
				// 	let a = uni.uploadFile({
				// 		url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', // 接口地址
				// 		filePath: url,
				// 		name: 'file',
				// 		header: {
				// 			"token": uni.getStorageSync("token")
				// 		},
				// 		formData: {
				// 			'user': 'test'
				// 		},
				// 		success: (res) => {
				// 			console.log('res', JSON.parse(res.data), JSON.parse(res.data).url);
				// 			setTimeout(() => {
				// 				resolve(JSON.parse(res.data).url);
				// 			}, 1000);
				// 		},
				// 		fail: (res) => {
				// 			setTimeout(() => {
				// 				console.log("上传失败", res)
				// 				reject(res)
				// 			}, 1000);
				// 		}
				// 	});
				// });
			},
			
			closeImagePopup() {
				this.imageShow = false
			},
			
			
			getBankList() {
				uni.$u.http
					.get("/api/school.newactivity.settle_log/detail")
					.then((res) => {
						console.log(res);
						this.bankInfo = res.data;
					});
			},
			openSfz() {
				uni.navigateTo({
					url: '/packageB/card/index'
				})
			},
			toName(e) {
				uni.navigateTo({
					url: '/packageB/names/index?type=' + e
				})
			},
			getSfInfo() {
				uni.$u.http.get('/api/school.real_name/info').then(res => {
					console.log(res);
					this.sfInfo = res.data;
				})
			},
			out() {
				uni.clearStorageSync()
				uni.reLaunch({
					url: '/pages/my/index'
				})
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			submit() {
				console.log(this.avatar, 'this.avatar')
				uni.$u.http.post('/api/user/profile', {
					avatar: this.avatar,
					nickname: this.niName,
					// work: this.zhiYe,
					// birthday: this.birthday,
					// realname: this.realname,
					// gender: this.gender == '男' ? '1' : '0',
					update_fields: [
						'nickname',
						// 'birthday', 
						// 'realname', 
						// 'gender', 
						// 'work', 
						'avatar'
					]
				}).then(res => {
					if (res.code == 1) {
						uni.setStorageSync('userInfo', {
							...uni.getStorageSync('userInfo'),
							avatar: this.avatar
						});
						this.getDetail()
						this.$refs.uToast.show({
							type: 'success',
							message: '保存成功',
							duration: '1000',
							complete: function() {
								// setTimeout(function() {
								// 	uni.navigateBack(1)
								// }, 1000);
							}
						})
					} else if (res.code == 0) {
						this.$refs.uToast.show({
							type: 'error',
							message: res.msg + ',' + '请完善全部信息'
						})
						this.getDetail()
					}
				})
			},
			getDetail() {
				uni.$u.http.post('/api/user/index').then(res => {
					if (res.code == 1) {
						const userinfo = res.data.user_info
						uni.setStorageSync('userInfo', res.data.user_info)
						uni.setStorageSync("niName", res.data.user_info.nickname)
						// if (userinfo.birthday !== null) {
						// 	this.birthday = userinfo.birthday
						// }
						// if (userinfo.work !== null) {
						// 	this.zhiYe = userinfo.work
						// }
						// if (userinfo.diqu !== null) {
						// 	this.area = userinfo.diqu
						// }
						if (userinfo.avatar !== null) {
							this.avatar = userinfo.avatar
							this.upAvatar = userinfo.avatar
							this.profile = userinfo.avatar
							uni.setStorageSync('avatar', this.avatar)
						}
						if (userinfo.nickname !== null) {
							this.niName = userinfo.nickname
							uni.setStorageSync('niName', this.niName)
						}
						if (userinfo.bio !== null) {
							this.signature = userinfo.bio
						}
						// if (userinfo.gender !== null) {
						// 	this.gender = userinfo.gender == '1' ? '男' : '女'
						// }
						// if (userinfo.realname !== null) {
						// 	this.realname = userinfo.realname
						// }
					}
				})
			},
			//初始化开始数据
			handelData() {
				// console.log(this.origiData.value))
				let sheng = [];
				let shi = [];
				let qu = [];
				this.origiData.forEach(item => {
					sheng.push(item.value);
					// 设置出初始化的数据
					if (item.value == '北京市') {
						item.children.forEach(child => {
							shi.push(child.value);

							if (child.value == '北京市') {
								child.children.forEach(el => {
									qu.push(el.value);
								});
							}
						});
					}
				});
				this.columns3.push(
					JSON.parse(JSON.stringify(sheng)),
					JSON.parse(JSON.stringify(shi)),
					JSON.parse(JSON.stringify(qu))
				);
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			clickLeft() {
				uni.navigateBack(-1)
			},
			//上传头像
			upload() {
				// 示例代码
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					success: (chooseImageRes) => {
						console.log(chooseImageRes);
						const tempFilePaths = chooseImageRes.tempFiles[0].tempFilePath;
						const that = this
						uni.uploadFile({
							url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', //仅为示例，非真实的接口地址
							filePath: tempFilePaths,
							name: 'file',
							header: {
								"token": uni.getStorageSync("token")
							},
							formData: {
								'user': 'test'
							},
							success: (res) => {
								let group = JSON.parse(res.data)
								that.upAvatar = group.data.url
								that.avatar = group.data.fullurl
								console.log(group.data)
								console.log(that.avata)
								that.submit();
							},
							fail(red) {
								console.log(red);
							}
						});
					},
					fail(res) {
						console.log(res);
					}
				});
				// this.show5 = true
			},
			
			//跳转页面  注意参数一定要带
			setAvator() {
				uni.navigateTo({
					url: "/packageA/cropper?destWidth=200&rectWidth=200&fileType=jpg"
				})
				//接收cropper页面传递的图片路径
				uni.$on('uAvatarCropper', path => {
					// 上传图片方法
					this.updates(path);
				});
		
			},
			// 上传图片
			updates(filePath) {
				const that = this
				const token = uni.getStorageSync('token')
				uni.uploadFile({
					url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', //targetUrl上传的地址
					filePath: filePath, //传递进来的图片
					name: 'file',
					header: {
						'token': token
					},
					formData: {
						'user': 'test'
					},
					success(res) {
						let data = JSON.parse(res.data).data
						that.upAvatar = data.url
						that.avatar = data.fullurl
						console.log(data)
						console.log(that.avata)
						that.submit();
					},
					fail(res) {
						console.log(res, '失败的上传')
					}
 
				})
			},

			close() {
				this.show5 = false
			},
			clickAvatar(index) {
				let avatar = ''
				if (index == 0) {
					avatar = 'https://naweigetetest2.hschool.com.cn/dyqc/img1.png'
				} else if (index == 1) {
					avatar = 'https://naweigetetest2.hschool.com.cn/dyqc/img2.png'
				} else if (index == 2) {
					avatar = 'https://naweigetetest2.hschool.com.cn/dyqc/img3.png'
				} else if (index == 3) {
					avatar = 'https://naweigetetest2.hschool.com.cn/dyqc/img4.png'
				}
				this.avatar = avatar
				console.log(this.avatar, 'this.avatar')
				uni.$u.http.post('/api/user/profile', {
					update_fields: ['avatar'],
					avatar: avatar
				}).then(res => {
					console.log(res);
					if (res.code == 1) {
						this.$refs.uToast.show({
							type: 'success',
							message: '修改成功'
						})
						uni.setStorageSync('userInfo', {
							...uni.getStorageSync('userInfo'),
							avatar: avatar
						});
						this.show5 = false
						this.getDetail()
					}
				})
			},
			confirm(e) {
				console.log(e)
				this.gender = e.value[0].name
				this.show = false
			},
			confirm1(e) {
				this.ages = e.value[0].name
				this.show1 = false
			},
			confirm2(e) {
				this.zhiYe = e.value[0].name
				this.show2 = false
			},
			confirm3(e) {
				this.birthday = dateFormats(e.value)
				this.show3 = false
			},
			confirm4(e) {
				console.log(e);
				this.area = e.value[0] + e.value[1] + e.value[2]
				this.show4 = false
			},
			cancel() {
				this.show = false
				this.show1 = false
				this.show2 = false
				this.show3 = false
				this.show4 = false
			},
			changeHandler(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e;
				// console.log('测试数据', e);
				// 当第一列值发生变化时，变化第二列(后一列)对应的选项
				if (columnIndex === 0) {
					// console.log(value)
					// picker为选择器this实例，变化第二列对应的选项
					this.origiData.forEach(item => {
						if (value[0] == item.value) {
							let shi = [];
							let flag = item.children[0].value;
							item.children.forEach((val, ol) => {
								shi.push(val.value);
								if (shi[0] == flag) { //设置默认开关（选择省份后设置默认城市）
									flag = '';
									let qu = [];
									val.children.forEach(vol => {
										qu.push(vol.value);
									});
									picker.setColumnValues(2, qu);
								}
							});
							picker.setColumnValues(1, shi);
						}
					});
				}
				//当第二列变化时，第三列对应变化
				if (columnIndex === 1) {
					this.origiData.forEach(item => {
						if (value[0] == item.value) {
							let shi = [];
							item.children.forEach((val, ol) => {
								shi.push(val.value);
								if (value[1] == val.value) {
									let qu = [];
									val.children.forEach(vol => {
										qu.push(vol.value);
									});
									picker.setColumnValues(2, qu);
								}
							});
						}
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	.left {
		font-weight: 600;
	}

	.content {
		.backImg {
			position: fixed;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			z-index: -1;
		}

		.container {
			box-sizing: border-box;
			padding: 20rpx 30rpx;
			// background: #F1F2F8;
			min-height: calc(100vh - 166rpx - 88rpx - 44rpx);

			.card {
				width: 100%;
				background: #fff;
				border-radius: 30rpx;
				box-sizing: border-box;

				.list {
					.list-item {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 30rpx;
						color: #181818;
						height: 110rpx;
						line-height: 110rpx;
						border-bottom: 1rpx solid #eeeeee;

						.right {
							display: flex;
							align-items: center;
							font-size: 30rpx;
							color: #202020;
							gap: 10rpx;
						}
					}
				}
			}
		}

		.bottom {
			position: fixed;
			left: 0;
			bottom: 0;
			padding-bottom: constant(safe-area-inset-bottom);
			/*兼容 IOS<11.2*/
			padding-bottom: env(safe-area-inset-bottom);
			/*兼容 IOS>11.2*/
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 166rpx;
			background: #FFFFFF;
		}

		.avatar_choose {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 100rpx;
			margin-bottom: 100rpx;

			.cards {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;

				img {
					width: 150rpx;
					height: 150rpx;
					margin-bottom: 20rpx;
					border-radius: 50%;
				}

				.choose_btn {
					width: 100rpx;
					height: 50rpx;
					border-radius: 10rpx;
					background-color: #009379;
					color: #FFFFFF;
					text-align: center;
					line-height: 50rpx;
				}
			}
		}
		
		
		.popup_box {
			border-radius: 30rpx;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			padding: 15px;
			
		
			.popup_title {
				width: 500rpx;
				text-align: center;
				margin: 10px auto;
			}
		
			button {
				box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0);
			}
		
			::v-deep button[type=default][plain] {
				border: none;
			}
		
			.nickname {
				border: none;
				font-size: 32rpx;
				margin: 0;
			}
		}
		
		
	}
</style>