<template>
	<view class="box flex justify-start align-items flex-column">
		<view class="con-center flex flex-column justify-center align-items">
			<view class="search flex  align-items" style="width: 99%;">
				<span class="dashed flex align-items justify-center">
					<image src="../../static/center/search.png" mode=""></image>
				</span>
				<span class="line-search"></span>
				<input type="text" placeholder="查找问题" v-model="keywords" class="input" placeholder-class="plasty" />
				<span class="searchBtn" @click.stop="search()">搜索</span>
			</view>
		</view>
		<view class="centerBox" v-if="!keywordShow">
			<view class="hot" v-if="list.length>0">
				<view class="item--list" v-for="(item, index) in list" :key="index">
					<view class="item--title" hover-class="tree__hover-class"
						@click="handleNodeClick(item);handleOpenClose(item, index)">
						<view :class="['ellipsis-multiline', `level-${item.level}`]">{{ item.name}}</view>
						<view v-if="item.children && item.children.length" class="open__and--close">
							<image v-if="!item.isOpen" src="/static/my/Fold.png" mode=""
								style="width: 24rpx;height: 24rpx;"></image>
							<image v-if="item.isOpen" src="/static/my/Spread.png" mode=""
								style="width: 24rpx;height: 24rpx;"></image>
						</view>
					</view>
					<view v-if="item.isOpen && item.children && item.children.length" class="">
						<xtx-treeNode :list="item.children" :level="item.level + 2"
							@change="selectIndex"></xtx-treeNode>
					</view>
				</view>
			</view>
			<xtx-treeNode :list="newList" :level='1' @change="treeChange"></xtx-treeNode>
		</view>
		<view class="centerBox" v-if="keywordShow">
			<div v-for="(item,index) in searchList" :key="index" style="padding: 30rpx 30rpx 0 30rpx;" class="flex flex-column ">
				<span class="text ellipsis-multiline" @click.stop="selectIndex(item)">{{item.title}}</span>
				<span class="line"></span>
			</div>
		</view>
		<view class="service flex justify-center align-items" style="padding-bottom: 0;"
			@click="callPhone(init.mobile)">
			<image src="../../static/my/server.png" mode="" style="width: 64rpx;height: 64rpx;"></image>
			<span class="flex justify-center align-items">联系客服:{{init.mobile}}</span>
		</view>



		<!-- <u-loadmore :status="loadStatus" /> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				lismit: 99999,
				keywords: '',
				oneList: [],
				twoList: [],
				data_list: [],
				list: [],
				init: {},
				keywordShow: false,
				searchList: []
			};
		},
		 watch: {
		    keywords(newVal) {
		      // 去除前后空格后检查是否为空
		      if (newVal.trim() === '') {
		        this.keywordShow = false;
		      }
		    }
		  },
		onLoad(option) {
			this.getInit()
			this.getHotList()
			this.init = uni.getStorageSync('init')
		},
		computed: {
			newTwoList() {
				// 接口数组转树形结构
				return this.twoList.map(item => {
					return {
						...item,
						id: "##" + item.id + "##",
						pid: item.help_cate_ids,
						name: item.title,

						//children: item.children.length ? this.newTwoList.filter(v => v.pid === item.id) : []
					}
				})
			},
			newList() {
				// 接口数组转树形结构
				// console.log([...this.newTwoList,...this.data_list])
				return this.arrayToTree()([...this.newTwoList, ...this.oneList], {
					id: 'id',
					pid: 'pid',
					children: 'children',
					parent: 0,
				})
			},
		},
		onReachBottom() {

		},

		methods: {
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},
			// 详情
			treeChange(item) {
				const id = item.id.match(/\d+/)[0];
				uni.navigateTo({
					url: `/packageA/my/helpDetail?id=${id}`
				})
			},
			selectIndex(item) {
				uni.navigateTo({
					url: `/packageA/my/helpDetail?id=${item.id}`
				})
			},
			handleOpenClose(item, index) {
				if (!item.hasOwnProperty('isOpen')) {
					item.isOpen = false
				}
				item.isOpen = !item.isOpen
				this.$forceUpdate()
			},
			handleNodeClick(item) {
				if (item.help_cate_ids) {
					this.selectIndex(item)
				}
			},
			arrayToTree() {
				// 接口数组转成与list相同的树形结构，其中children为子级
				return function(list, options = {}) {
					//为每行代码加详细注释
					// map用来存储id为键，整行数据为值的对象
					let {
						id = 'id', pid = 'pid', children = 'children', parent = 0
					} = options
					let result = []
					let map = {}
					// 将数据转成键值对，方便查找
					list.forEach(item => {
						map[item[id]] = item
					})
					// 遍历数据，将数据转成树形结构
					list.forEach(item => {
						// 如果pid为0，则直接添加到result中，否则根据pid找到父级数据
						let parentItem = map[item[pid]]
						// 如果父级数据不存在，则直接添加到result中
						if (parentItem) {
							// 如果父级数据不存在children属性，则创建一个children属性
							if (!parentItem[children]) {
								// 创建children属性
								parentItem[children] = []
							}
							//如果已经push，则不重复push
							if (parentItem[children].findIndex(v => v[id] === item[id]) === -1) {
								parentItem[children].push(item)
							}

							// 将子级数据添加到父级数据中
							//parentItem[children].push(item)
						} else {
							// 如果父级数据不存在，则直接添加到result中
							result.push(item)
						}
					})
					return result
				}
			},
			// 搜索
			search() {
				uni.$u.http.get('/api/school.help/article_list', {
					params: {
						keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						help_cate_ids: '',
						hot: '',
					}
				}).then(res => {
					if (res.code == 1) {
						this.searchList = res.data.list
						this.keywordShow = true
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			getInit() {
				uni.$u.http.get('/api/school.help/cate_list', {
					params: {
						page: this.page,
						limit: this.limit,
						pid: '',
					}

				}).then(res => {
					if (res.code == 1) {
						this.oneList = res.data.list
						this.getTwoList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			getTwoList() {
				uni.$u.http.get('/api/school.help/article_list', {
					params: {
						page: this.page,
						limit: this.limit,
						help_cate_ids: '',
						hot: ''
					}
				}).then(res => {
					if (res.code == 1) {
						this.twoList = res.data.list
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 获取热门问题
			getHotList() {
				uni.$u.http.get('/api/school.help/article_list', {
					params: {
						page: this.page,
						limit: this.limit,
						help_cate_ids: '',
						hot: 1
					}
				}).then(res => {
					if (res.code == 1) {
						if (res.data.list) {
							res.data.list = res.data.list.map((item) => {
								return {
									name: item.title,
									id: item.id,
									help_cate_ids: item.help_cate_ids,
									level: 3
								};
							});
							this.list = [{
								name: '热门问题',
								level: 1,
								children: [...res.data.list]
							}]
						} else {
							this.list = [{
								name: '热门问题',
								level: 1,
								children: []
							}]
						}
						console.log('this.list', this.list)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		// background: linear-gradient(to bottom, #F1F2F8 0%, #FFFFFF 5%, #FFFFFF 100%);
		background: #F1F2F8;
		min-height: 100vh;

		.con-center {
			width: 690rpx;
			// margin-top: 25rpx;

			.centerBack {
				position: fixed;
				width: 100%;
				height: 100%;
				top: 25rpx;
				left: 0;
				z-index: -1;
			}

			.header {
				height: 50rpx;
				margin-top: 37rpx;

				.s-header {
					width: 104rpx;
					height: 50rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #9E9E9E;
					line-height: 26rpx;
				}

				.s-header.selected {
					width: 104rpx;
					height: 50rpx;
					background: #008CFF;
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 28rpx;
					color: #FFFFFF;
					line-height: 26rpx;
				}
			}
		}

	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		margin-top: 12rpx;
		width: 690rpx;
		height: 1rpx;
		background: #008CFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}




	.search {
		margin-top: 24rpx;
		width: 690rpx;
		height: 64rpx;
		background: #FFFFFF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008CFF;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008CFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

		}

		.searchBtn {
			width: 128rpx;
			height: 64rpx;
			background: #008CFF;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}



	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.centerBox {
		height: 70vh;
		width: 690rpx;
		background: #FFFFFF;
		border-radius: 12px 12px 12px 12px;
		margin-top: 24rpx;
		overflow-y: auto;
		/* 添加这一行 */
	}
	.text{
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #008CFF;
	}
	.line{
		width: 602rpx;
		height: 1rpx;
		background: #F1F2F8;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.item--list {
		padding-left: 25rpx;
	}

	.item--title {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		border-bottom: 1rpx solid #f7f7f7;
		padding: 25rpx;
	}

	.open__and--close {
		margin-left: auto;
		font-size: 24rpx;
	}

	.tree__hover-class {
		background-color: #f7f7f7;
	}

	.ellipsis-multiline.level-1 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 28rpx;
		color: #343434;
	}

	.ellipsis-multiline.level-2 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #343434;
	}

	.ellipsis-multiline.level-3 {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #008CFF;
	}

	.ellipsis-multiline {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.service {
		margin-top: 24rpx;
		width: 690rpx;
		height: 94rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 30rpx;
		color: #008CFF;
	}
</style>