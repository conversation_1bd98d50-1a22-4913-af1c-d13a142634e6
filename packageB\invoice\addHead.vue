<template>
	<!-- <view>添加抬头</view> -->
	<view class="container">

		<!-- 表单 -->
		<view style="height: auto;">
			<u-form :model="form" ref="uForm" label-width="auto" labelPostition="left">
				<view class="formBox">
					<u-form-item label="发票类型" prop="invoice_type">
						<view class="typeBOx" style="width: 300px;justify-content: flex-end;">
							<view class="normalInv" @click="handleHeadTp1()">
								<view class="typeImgs">
									<image v-if="form.invoice_type == 'ordinary'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">普通发票</view>
							</view>
							<view class="specialInv" @click="handleHeadTp2()">
								<view class="typeImgs">
									<image v-if="form.invoice_type == 'special'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">专用发票</view>
							</view>
						</view>

					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>
					
					<u-form-item label="发票内容" prop="">
						<view class="typeBOx" style="width: 296px;justify-content: space-between;">
							<view class="typeImgs" @click="explainShow = true">
								<image style="width: 38rpx;height: 38rpx;position: relative;top: 6rpx;"
									src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/conRecord.png" mode="">
								</image>
							</view>
							<view class="normalInv">
								<view class="typeImgs">
									<image style="width: 44rpx;height: 44rpx;position: relative;top: 6rpx;"
										src="/static/fabu/check.png" mode=""></image>
								</view>
								<view class="typeText">商品明细</view>
							</view>
						</view>
						<!-- <image style="width: 38rpx;height: 38rpx;" src="/static/tips.png" mode=""></image> -->
						<!-- <u-input inputAlign="right" v-model="form.password"  placeholder="请输入密码" border="none"/> -->
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="抬头类型" prop="head_type">
						<view class="typeBOx" style="width: 219px;justify-content: flex-end;">
							<view class="normalInv" v-if="form.invoice_type == 'ordinary' || form.invoice_type == ''"
								@click="handlePerson()">
								<view class="typeImgs">
									<image v-if="form.head_type == 'personal'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">个人或事业单位</view>
							</view>
							<view class="specialInv" @click="handleCompany()">
								<view class="typeImgs">
									<image v-if="form.head_type == 'corporate'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">企业</view>
							</view>
						</view>

					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="抬头名称" prop="invoice_header">
						<u-input inputAlign="right" v-model="form.invoice_header" placeholder="请输入抬头名称"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate' || form.head_type == ''"></view>
					
					<u-form-item label="单位税号" prop="tax_id"
						v-if="form.head_type == 'corporate' || form.head_type == ''">
						<u-input inputAlign="right" v-model="form.tax_id" placeholder="请输入单位税号"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate' || form.head_type == ''"></view>
					
					<u-form-item label="注册地址" prop="enterprise_address"
						v-if="form.head_type == 'corporate' || form.head_type == ''">
						<u-input inputAlign="right" v-model="form.enterprise_address" placeholder="必填"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate' || form.head_type == ''"></view>
					
					<u-form-item label="注册电话" prop="enterprise_phone"
						v-if="form.head_type == 'corporate' || form.head_type == ''">
						<u-input inputAlign="right" v-model="form.enterprise_phone" type="number" placeholder="必填"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate' || form.head_type == ''"></view>
					
					<u-form-item label="开户银行" prop="bank_deposit"
						v-if="form.head_type == 'corporate' || form.head_type == ''">
						<u-input inputAlign="right" v-model="form.bank_deposit" placeholder="必填"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate' || form.head_type == ''"></view>
					
					<u-form-item label="银行账号" prop="bank_number"
						v-if="form.head_type == 'corporate' || form.head_type == ''">
						<u-input inputAlign="right" v-model="form.bank_number" type="number" placeholder="必填"
							border="none" />
					</u-form-item>
				</view>
				<!-- <view style="background-color: #f5f5f5;height: 40rpx;"></view> -->

				<view class="recipientInfo">
					<u-form-item label="手机号" prop="invoice_reservation_phone">
						<u-input inputAlign="right" v-model="form.invoice_reservation_phone" type="number"
							placeholder="请输入发票预留手机号" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>
					
					<u-form-item label="邮箱地址" prop="invoice_reservation_email">
						<u-input inputAlign="right" v-model="form.invoice_reservation_email"
							placeholder="请输入发票预留邮箱" border="none" />
					</u-form-item>
				</view>

				<view class="defaultInfo" @click="handleDefault()">
					<view class="defaultLeft">
						<view class="leftText1">设为默认发票</view>
						<view class="leftText2">设置为默认，后续下单优先使用以上发票信息</view>
					</view>
					<view class="defaultRight">
						<image v-if="is_default == 1" style="width: 44rpx;height: 44rpx;"
							src="../../static/fabu/check.png" mode=""></image>
						<image v-else style="width: 44rpx;height: 44rpx;" src="../../static/fabu/nocheck.png" mode="">
						</image>
					</view>
				</view>


				<!--<u-form-item>
							<u-button type="primary" @click="submit">申请开票</u-button>
						</u-form-item>-->



			</u-form>

			<view v-if="form.head_type == 'corporate' || form.head_type == ''" style="width: 100%;height: 300rpx;">
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="footer-btn">
			<view class="invoiceBtn" @click.stop="submit()">
				<view>提交保存</view>
			</view>
			<view v-if="deleteShow" class="invoiceDeleteBtn" @click.stop="deleteHead()">
				<view>删除</view>
			</view>
		</view>



		<u-popup :show="explainShow" :round="22" mode="center" @close="explainShow = false" @open="explainShow = true"
			:custom-style="popupStyRe">
			<view class="popup_tkall" style="">
				<view class="popup_tk">发票内容说明</view>
				<view style="font-size: 28rpx;font-weight: 400;color: #3D3D3D;">
					<view>·发票内容将显示详细商品名称与价格信息</view>
					<view style="margin-top: 20rpx;">·部分商家可能开具发票内容为商品所属类别及价格信息，如有特殊需求，请向商家客服咨询。</view>
				</view>

				<view class="popup-footer"
					style="display: flex;justify-content: center;align-items: center;margin-top: 40rpx;">
					<view class="headBtn" style="width: 45%;height: 80rpx;background-color: #ffffff;border-radius: 148rpx;color: #999999;font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						border: 2rpx solid #999999" @click="explainShow = false">
						<view>取消</view>
					</view>
					<view class="headBtn" style="width: 45%;height: 80rpx;background-color: #323232;border-radius: 148rpx;color: #BBFC5B;font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						margin-left: 20rpx;" @click="explainShow = false">
						<view>我已知晓</view>
					</view>
				</view>
			</view>
		</u-popup>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupStyRe: {
					width: '540rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '384rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				style: {
					// 字符串的形式
					img: 'width: 100%'
				},
				id: 0,
				order_no: '',
				order_nos: '',
				form: {
					order_nos: '',
					head_type: '',
					invoice_type: '',
					invoice_header: '',
					tax_id: '',
					bank_deposit: '',
					bank_number: '',
					enterprise_address: '',
					enterprise_phone: '',
					invoice_reservation_phone: '',
					invoice_reservation_email: '',
					is_default: null
				},
				
				detail: '', //第二个detail
				detailAny: '', //第一个detail
				qrimages: '',
				headShow: false, //预设抬头的遮罩
				title: '',
				deleteShow: false,
				explainShow: false, //发票内容显隐
				is_default: 0, //0是否，1是设置为默认
			}
		},
		onLoad(options) {
			console.log('options:', options.id);
			const id = options.id
			const ids = options.id
			console.log('ids:', ids);
			if (ids) {
				this.id = ids
				console.log('ids1', this.id, ids);
				this.title = '编辑发票抬头信息'
				this.deleteShow = true
				this.getHeadInfo();
			} else {
				this.deleteShow = false
				this.title = '添加发票抬头信息'
			}
		},
		onReady() {

		},
		methods: {
			handleHeadTp1() {
				this.form.invoice_type = 'ordinary'
				this.form.head_type = ''
				console.log('1-1', this.form.invoice_type);
			},
			handleHeadTp2() {
				// this.$set(this.form.invoice_type = 'special')
				this.form.invoice_type = 'special'
				this.form.head_type = 'corporate'
				console.log('2-2', this.form.invoice_type);
			},
			handlePerson() {
				console.log('1');
				this.form.head_type = 'personal'
			},
			handleCompany() {
				console.log('2');
				this.form.head_type = 'corporate'
			},
			handleDefault() {
				console.log('handleDefault');
				const index = this.is_default;
				console.log('index', index);
				if (index == 0) {
					this.is_default = 1
				} else {
					this.is_default = 0
				}
				this.form.is_default = this.is_default
				console.log('this.is_default', this.is_default, this.form.is_default);
			},
			//抬头详情信息
			getHeadInfo() {
				console.log('详情', this.id);
				uni.$u.http.get('/api/school.header/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						console.log('详情返值', res.data);
						this.form = res.data.detail
						this.is_default = res.data.detail.is_default
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			submit() {
				// 校验发票类型
				if (this.form.invoice_type == '') {
					uni.showToast({
						title: '请选择发票类型！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				// 校验抬头名称
				if (this.form.invoice_header == '') {
					uni.showToast({
						title: '请输入抬头名称！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				// 校验手机号
				if (this.form.invoice_reservation_phone == '') {
					uni.showToast({
						title: '请输入手机号！',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				// 校验邮箱地址
				if (this.form.invoice_reservation_email == '') {
					uni.showToast({
						title: '请输入邮箱地址！',
						icon: 'none',
						duration: 2000
					});
					return;
				}


				//先判断有没有选择抬头类型
				if (this.form.head_type == '') {
					uni.showToast({
						title: '请选择抬头类型',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				//再校验不同类型下的数据
				if (this.form.head_type == 'special') {
					//企业类型
					console.log('抬头类型——企业');
					//校验单位税号
					if (this.form.tax_id == '') {
						uni.showToast({
							title: '请输入单位税号！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					//校验注册地址
					if (this.form.enterprise_address == '') {
						uni.showToast({
							title: '请输入注册地址！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					//校验注册电话
					if (this.form.enterprise_phone == '') {
						uni.showToast({
							title: '请输入注册电话！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					//校验开户银行
					if (this.form.bank_deposit == '') {
						uni.showToast({
							title: '请输入开户银行！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					// 校验银行账号
					if (this.form.bank_number == '') {
						uni.showToast({
							title: '请输入银行账号！',
							icon: 'none',
							duration: 2000
						});
						return;
					}
					
					//正则判断银行卡不低于13位
					const regBank = /^[1-9]\d{12,}$/;
					if(!regBank.test(this.form.bank_number)){
						uni.showToast({
							title: '请输入正确的银行卡号',
							icon: "none",
							duration: 1500,
						});
						return;
					}
				}
				console.log('submit');
				
				if (this.id) {
					//编辑
					console.log('编辑');
					uni.$u.http.post('/api/school.header/edit', {
						ids: this.id,
						order_nos: this.form.order_nos,
						head_type: this.form.head_type,
						invoice_type: this.form.invoice_type,
						invoice_header: this.form.invoice_header,
						tax_id: this.form.tax_id,
						bank_deposit: this.form.bank_deposit,
						bank_number: this.form.bank_number,
						enterprise_address: this.form.enterprise_address,
						enterprise_phone: this.form.enterprise_phone,
						invoice_reservation_phone: this.form.invoice_reservation_phone,
						invoice_reservation_email: this.form.invoice_reservation_email,
						is_default: this.form.is_default

					}).then(res => {
						if (res.code == 1) {
							
							uni.showToast({
								title: '保存成功！',
								icon: 'none',
								duration: 1000,
							});
							//置空
							this.is_default = null;
							
							this.form = {
								order_nos: '',
								head_type: '',
								invoice_type: '',
								invoice_header: '',
								tax_id: '',
								bank_deposit: '',
								bank_number: '',
								enterprise_address: '',
								enterprise_phone: '',
								invoice_reservation_phone: '',
								invoice_reservation_email: '',
								is_default: null
							}
							setTimeout(() => {
								uni.navigateBack()
								// this.getInfo();
								// uni.hideLoading();
							}, 1000)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							})
						}
					}).catch(error => {});
				} else {
					//新增
					console.log('新增');
					uni.$u.http.post('/api/school.header/add', {
						order_nos: this.form.order_nos,
						head_type: this.form.head_type,
						invoice_type: this.form.invoice_type,
						invoice_header: this.form.invoice_header,
						tax_id: this.form.tax_id,
						bank_deposit: this.form.bank_deposit,
						bank_number: this.form.bank_number,
						enterprise_address: this.form.enterprise_address,
						enterprise_phone: this.form.enterprise_phone,
						invoice_reservation_phone: this.form.invoice_reservation_phone,
						invoice_reservation_email: this.form.invoice_reservation_email,
						is_default: this.form.is_default

					}).then(res => {
						if (res.code == 1) {
							
							uni.showToast({
								title: '添加成功！',
								icon: 'none',
								duration: 1000,
							});
							//置空
							this.is_default = null;
							
							this.form = {
								order_nos: '',
								head_type: '',
								invoice_type: '',
								invoice_header: '',
								tax_id: '',
								bank_deposit: '',
								bank_number: '',
								enterprise_address: '',
								enterprise_phone: '',
								invoice_reservation_phone: '',
								invoice_reservation_email: '',
								is_default: null
							}
							setTimeout(() => {
								uni.navigateBack()
								// this.getInfo();
								// uni.hideLoading();
							}, 1000)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							})
						}
					}).catch(error => {});
				}
			},
			deleteHead() {
				console.log('deleteHead');
				uni.$u.http.post('/api/school.header/del', {
					ids: this.id,
				}).then(res => {
					if (res.code == 1) {
						// this.cancelsShow = false
						uni.showToast({
							title: '删除成功',
							icon: 'success',
							duration: 2000
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 2000)
						// this.getDraftList2('15')
						// this.toShlist('2')
						// setTimeout(() => {
						// 	uni.redirectTo({
						// 		url: "/packageA/my/orderList"
						// 	})
						// }, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 1000
						})
					}
				}).catch(error => {});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.form_right {
		display: flex;
		align-items: center;
		margin-left: auto;
		color: #848484;
		width: 300rpx;
		padding: 0rpx 0rpx 0rpx 20rpx;
		position: relative;
		left: 2rpx;
		font-size: 26rpx;
		font-weight: 400;
		text-align: right;
		justify-content: flex-end;
	}

	.typeBOx {
		display: flex;
		align-items: center;
		margin-left: auto;

		.normalInv {
			display: flex;
			align-items: center;
			justify-content: center;

			.typeImgs {
				width: 44rpx;
				height: 44rpx;
			}

			.typeText {
				font-size: 30rpx;
				font-weight: 400;
				color: #999999;
				margin-left: 20rpx;
			}

			.typeText2 {
				font-size: 30rpx;
				font-weight: 400;
				color: #323232;
				margin-left: 20rpx;
			}
		}

		.specialInv {
			margin-left: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.typeImgs {
				width: 44rpx;
				height: 44rpx;
			}

			.typeText {
				font-size: 30rpx;
				font-weight: 400;
				color: #999999;
				margin-left: 20rpx;
			}

			.typeText2 {
				font-size: 30rpx;
				font-weight: 400;
				color: #323232;
				margin-left: 20rpx;
			}
		}
	}

	.container {
		min-height: 90vh;
		padding: 30rpx 0;
		background-color: #f5f5f5;
		// justify-content: center;
		// display: grid;

		.formBox {
			// width: 100%;
			margin-top: 0rpx;
			background-color: #ffffff;
			padding: 10rpx 30rpx;
			border-radius: 20rpx;
			
			.line-row {
				width: 700rpx;
				height: 1rpx;
				background: #F0F0F0;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
			
		}

		.recipientInfo {
			margin-top: 30rpx;
			background-color: #ffffff;
			padding: 10rpx 30rpx;
			border-radius: 20rpx;
			
			.line-row {
				width: 700rpx;
				height: 1rpx;
				background: #F0F0F0;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
			
		}

		.defaultInfo {
			margin-top: 30rpx;
			// margin-left: 30rpx;
			background-color: #ffffff;
			padding: 30rpx 30rpx 30rpx 20rpx;
			border-radius: 20rpx;
			// width: 690rpx;
			width: 93%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.defaultLeft {
				display: grid;
				justify-content: flex-start;
				align-items: center;

				.leftText1 {
					font-weight: 600;
					font-size: 32rpx;
					color: #3d3d3d;
					font-family: PingFang SC, PingFang SC;
					line-height: 36rpx;
					font-style: normal;
					text-transform: none;
				}

				.leftText2 {
					font-weight: 400;
					font-size: 24rpx;
					color: #3d3d3d;
					margin-top: 20rpx;
					font-family: PingFang SC, PingFang SC;
					line-height: 36rpx;
					font-style: normal;
					text-transform: none;
				}

			}
		}

		.footer-btn {
			width: 95%;
			padding: 20rpx 20rpx;
			display: grid;
			justify-content: center;
			position: fixed;
			bottom: 60rpx;
			// left: 0;
			right: -2rpx;
			z-index: 99;
			background-color: #f5f5f5;
			color: #bbfc5b;

			.invoiceBtn {
				width: 690rpx;
				height: 90rpx;
				background-color: #323232;
				border-radius: 148rpx;
				color: #BBFC5B;
				font-size: 36rpx;
				font-weight: 400;
				line-height: 50rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				text-transform: none;
				font-style: normal;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				// position: fixed;
				// bottom: 66rpx;

				// margin-left: 2%;
			}

			.invoiceDeleteBtn {
				margin-top: 30rpx;
				width: 690rpx;
				height: 90rpx;
				background-color: #ffffff;
				border-radius: 148rpx;
				color: #999999;
				border: 2rpx solid #999999;
				font-size: 36rpx;
				font-weight: 400;
				line-height: 50rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				text-transform: none;
				font-style: normal;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				// position: fixed;
				// bottom: 40rpx;
				// margin-left: 2%;
			}

		}

		.popup_tkall {
			background-color: #ffffff;

			.popup_tk {
				font-size: 32rpx;
				font-weight: 500;
				margin: 12rpx 0 24rpx 0;
				text-align: center;
				color: #3D3D3D;
			}
		}

	}
</style>