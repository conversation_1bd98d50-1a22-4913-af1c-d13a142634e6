// 静态数据
const staticData = [{
		name: '区域',
		type: 'hierarchy',
		submenu: []
	},
	{
		name: '类型',
		type: 'radio',
		submenu: [{
			name: "",
			submenu: [{
					name: "形势政策类",
					value: "形势政策类"
				}, {
					name: "文化艺术类",
					value: "文化艺术类"
				},
				{
					name: "职业发展类",
					value: "职业发展类"
				},
				{
					name: "社会融入类",
					value: "社会融入类"
				}
			]
		}]
	},
	{
		name: '热门',
		type: 'filter',
		submenu: [{
			name: "",
			submenu: []
		}]
	},
	
	{
		name: '标签',
		type: 'filter',
		submenu: [{
			name: "",
			submenu: []
		}]
	},
	// {
	// 	name: '更多',
	// 	icon: 'settings',
	// 	type: 'filter',
	// 	submenu: [{
	// 			name: "类型",
	// 			submenu: []
	// 		},
	// 		{
	// 			name: "热门",
	// 			submenu: []
	// 		},
	// 		{
	// 			name: "场地",
	// 			submenu: [{
	// 				name: "室内",
	// 				value: "室内"
	// 			}, {
	// 				name: "室外",
	// 				value: "室外"
	// 			}]
	// 		}
	// 	]
	// }
];

// 通用请求函数
const fetchDataFromApi = async (url, params) => {
	try {
		const res = await uni.$u.http.get(url, {
			params
		});
		if (res.code === 1) {
			return res.data;
		} else {
			uni.showToast({
				title: res.msg,
				icon: 'none',
				duration: 2000
			});
			throw new Error(res.msg);
		}
	} catch (error) {
		uni.showToast({
			title: '请求失败，请稍后再试',
			icon: 'none',
			duration: 2000
		});
		throw error;
	}
};

// 获取分类数据
export const fetchData = async () => {
	return await fetchDataFromApi('/api/school/classes/cate_list', {});
};

// 获取区县数据
export const fetchArea = async () => {
	return await fetchDataFromApi('/api/index/get_area', {
			city:410300
	});
};

// 获取分类数据
export const fetchtype= async () => {
	return await fetchDataFromApi('/api/school/classes/type_list', {});
};

// 获取标签数据
export const fetchDate = async () => {
	return await fetchDataFromApi('/api/school/classes/label_list', {});
};

// 更新静态数据
export const updateStaticData = async () => {
	try {
		// 获取分类数据
		const categories = await fetchData();
		// 获取标签数据
		const labels = await fetchDate();
		// 获取类型数据
		const types = await fetchtype();
		
		const areas = await fetchArea()

		console.log(labels, 'labels')
		console.log(categories, 'categories')
		console.log(areas,'areas')

		// 更新标签数据
		const LabelIndex = staticData.findIndex(item => item.name === '标签');
		if (LabelIndex !== -1) {
			staticData[LabelIndex].submenu[0].submenu = categories.list.map(category => ({
				name: category.name,
				value: category.id
			}));
		}
		
		// 更新区县数据
		const areaIndex = staticData.findIndex(item => item.name === '区域');
		if (areaIndex !== -1) {
			staticData[areaIndex].submenu = areas.map(category => ({
				name: category.label,
				value: category.value
			}));
		}
		
		
		// 更新类型数据
		const typeIndex = staticData.findIndex(item => item.name === '类型');
		if (typeIndex !== -1) {
			staticData[typeIndex].submenu[0].submenu = types.list.map(category => ({
				name: category.name,
				value: category.id
			}));
		}

		// 更新热门数据
		const hotIndex = staticData.findIndex(item => item.name === '热门');
		if (hotIndex !== -1) {
			staticData[hotIndex].submenu[0].submenu = labels.list.map(label => ({
				name: label.name,
				value: label.id
			}));
		}

		// 更新更多里面的类型和热门数据
		const moreIndex = staticData.findIndex(item => item.name === '更多');
		if (moreIndex !== -1) {
			const typeMoreIndex = staticData[moreIndex].submenu.findIndex(item => item.name === '标签');
			if (typeMoreIndex !== -1) {
				staticData[moreIndex].submenu[typeMoreIndex].submenu = categories.map(category => ({
					name: category.name,
					value: category.id
				}));
			}

			const hotMoreIndex = staticData[moreIndex].submenu.findIndex(item => item.name === '热门');
			if (hotMoreIndex !== -1) {
				staticData[moreIndex].submenu[hotMoreIndex].submenu = labels.map(label => ({
					name: label.name,
					value: label.id
				}));
			}
		}

		// 返回更新后的静态数据
		return staticData;
	} catch (error) {
		console.error('更新静态数据失败:', error);
		throw error;
	}
};

// 导出静态数据
export default staticData;