<template>
	<view style="height: 100vh;background: #F1F2F8;padding: 30rpx;">
		<view style="background-color: #ffffff;border-radius: 20rpx;padding:0rpx 30rpx">
			<ren-calendar ref='ren' :markDays='markDays' :headerBar='true' @onDayClick='onDayClick'></ren-calendar>
		</view>
		<!-- 上午 -->
		<view style="background-color: #ffffff;border-radius: 20rpx;padding:30rpx;margin-top: 20rpx;"
			v-if="morning.length != 0">
			<view style="font-size: 28rpx;color: #C2C2C2;text-align: center;margin: 20rpx;">上午</view>
			<view style="display: flex;align-self: center;align-items: center; margin-top: 32rpx;"
				v-for="(item,index) in morning" :key="index"
				@click.stop="openUrl('/packageA/center/applyDetail?id='+item.id)">
				<view style="width: 45%;">
					<image :src="item.detail.headimage" style="width: 280rpx; height: 200rpx;border-radius: 12rpx;" mode="aspectFill">
					</image>
				</view>
				<view style="padding-left: 20rpx; height: 200rpx;">
					<view style="font-size: 36rpx;font-weight: 600;margin-bottom: 24rpx;" class="ellipsis-two-lines">
						{{item.detail.title}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 20rpx;">
						上课时间:{{item.startTime}}-{{item.endTime}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 12rpx;" class="ellipsis-two-lines">
						{{item.detail.address_detail}}号</view>
				</view>
			</view>
		</view>

		<!-- 下午 -->
		<view style="background-color: #ffffff;border-radius: 20rpx;padding:30rpx;margin-top: 20rpx;"
			v-if="afternoon.length != 0">
			<view style="font-size: 28rpx;color: #C2C2C2;text-align: center;margin: 20rpx;">下午</view>
			<view style="display: flex;align-self: center;align-items: center; margin-top: 32rpx;"
				v-for="(item,index) in afternoon" :key="index">
				<view style="width: 45%;">
					<image :src="item.detail.headimage" style="width: 280rpx; height: 200rpx;border-radius: 12rpx;" mode="aspectFill">
					</image>
				</view>
				<view style="padding-left: 20rpx; height: 200rpx;">
					<view style="font-size: 36rpx;font-weight: 600;margin-bottom: 24rpx;" class="ellipsis-two-lines">
						{{item.detail.title}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 20rpx;">
						上课时间:{{item.startTime}}-{{item.endTime}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 12rpx;" class="ellipsis-two-lines">
						{{item.detail.address_detail}}号</view>
				</view>
			</view>
		</view>

		<!-- 晚上 -->
		<view style="background-color: #ffffff;border-radius: 20rpx;padding:30rpx;margin-top: 20rpx;"
			v-if="night.length != 0">
			<view style="font-size: 28rpx;color: #C2C2C2;text-align: center;margin: 20rpx;">晚上</view>
			<view style="display: flex;align-self: center;align-items: center; margin-top: 32rpx;"
				v-for="(item,index) in night" :key="index">
				<view style="width: 45%;">
					<image :src="item.detail.headimage" style="width: 280rpx; height: 200rpx;border-radius: 12rpx;" mode="aspectFill">
					</image>
				</view>
				<view style="padding-left: 20rpx; height: 200rpx;">
					<view style="font-size: 36rpx;font-weight: 600;margin-bottom: 24rpx;" class="ellipsis-two-lines">
						{{item.detail.title}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 20rpx;">
						上课时间:{{item.startTime}}-{{item.endTime}}</view>
					<view style="font-size: 26rpx;color: #7A7A7A;margin-top: 12rpx;" class="ellipsis-two-lines">
						{{item.detail.address_detail}}号</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import RenCalendar from '@/packageA/ren-calendar.vue'
	export default {
		components: {
			RenCalendar
		},
		data() {
			return {
				curDate: '',
				 markDays: ['2025-01-05'],
				morning: [],
				afternoon: [],
				night: []
			}
		},
		onReady() {
			let today = this.$refs.ren.getToday().date;
			this.curDate = today;
			console.log('today',today)
			this.markDays.push(today);
			this.getTime()
			this.getList(this.curDate)
		},
		methods: {
			onDayClick(data) {
				this.curDate = data.date;
				this.getList(this.curDate)
			},
			// 时间转换
			getFormattedDates(date) {
				const today = date ? new Date(date) : new Date();
				const startOfDay = new Date(today);
				startOfDay.setHours(0, 0, 0, 0);

				const endOfDay = new Date(today);
				endOfDay.setHours(23, 59, 59, 999);

				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');

					return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
				};

				return {
					today: `${formatDate(startOfDay)}---${formatDate(endOfDay)}`
				};
			},
			//跳转详情
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			// 获取时间
			getTime() {
				uni.$u.http.get('/api/school.hour_order/get_month_by_time', {
					params: {
						time: this.curDate,
						status: '-1,0'
					}
				}).then(res => {
					if (res.code == 1) {
						const filteredList = res.data.filter(item => item.number !== 0);

						const list = filteredList.map(item => {
							item.markDay = `${item.year}-${item.month}-${item.day}`;
							return item;
						});

						// 提取 markDay 字段并赋值给 this.markDays
						this.markDays = list.map(item => item.markDay);
						console.log(this.markDays);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			//获取课程
			getList(time) {
				const formattedDates = this.getFormattedDates(time).today;
				console.log(formattedDates)
				uni.$u.http.get('/api/school/hour_order/order_list', {
					params: {
						time: formattedDates,
						status: '-1,0'
					}
				}).then(res => {

					if (res.code == 1) {
						const morning = [];
						const afternoon = [];
						const night = [];

						res.data.list.forEach(item => {
							const startTimeText = item.start_time_text;
							const endTimeText = item.end_time_text;

							// 提取时分部分
							const startTime = startTimeText.split(' ')[1].split(':').slice(0, 2).join(':');
							const endTime = endTimeText.split(' ')[1].split(':').slice(0, 2).join(':');

							// 创建一个新的对象，包含原始数据和新的 startTime 和 endTime
							const newItem = {
								...item,
								startTime,
								endTime
							};

							const startDateTime = new Date(item.start_time *
							1000); // 假设 start_time 是以秒为单位的时间戳
							const hours = startDateTime.getHours();

							// 分类
							if (hours < 12) {
								morning.push(newItem);
							} else if (hours >= 12 && hours < 18) {
								afternoon.push(newItem);
							} else {
								night.push(newItem);
							}
						});

						this.morning = morning;
						this.afternoon = afternoon;
						this.night = night;

						console.log('Morning:', morning);
						console.log('Afternoon:', afternoon);
						console.log('Night:', night);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.ellipsis-two-lines {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		/* 设置行数 */
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-all;
		/* 防止单词被截断 */
	}
</style>