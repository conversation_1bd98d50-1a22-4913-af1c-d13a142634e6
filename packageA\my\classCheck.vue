<template>
	<view class="box flex align-items justify-center flex-column">
		<view class="header flex align-items justify-center flex-column">
			<view class="search flex   align-items" style="width:690rpx;">
				<span class="dashed">
					<image src="../../static/center/search.png" mode=""></image>
				</span>
				<span class="line-search"></span>
				<input type="text" placeholder="搜索您的审核课程" v-model="keywords" class="input" placeholder-class="plasty" />
				<span class="searchBtn" @click.stop="search()">搜索</span>
			</view>
		</view>

		<view class="center">
			<view class="center-box flex flex-column" v-for="(item,index) in list" :key="index">
				<span class="title white-space w-100">{{item.detail.title}}</span>
				<view class="flex align-items w-100" style="margin-top: 29rpx;">
					<view class="left">
						<image :src="item.detail.headimage" mode="" style="width: 200rpx;height: 140rpx;"></image>
					</view>
					<view class="right flex flex-column w-100">
						<span class="yyTime hui flex align-items white-space">
							机构:
							<span class="hui white-space" style="font-weight: 800;">{{shopName}}</span>
						</span>
						
						<span class="yyRen hui white-space">报名人信息:{{item.user.realname ||item.user.nickname}}</span>
						<span class="yyTime hui flex align-items white-space">
							课时预约:
							<span class="hui white-space" style="font-weight: 800;">{{item.name}}</span>
						</span>
						<!-- <span class="tel hui white-space flex align-items" @click.stop="handlePhone(detail.tel)">{{item.user.mobile}}
						 <image src="../../static/tel.png" mode="" style="width: 36rpx;height: 36rpx;"></image>
						</span> -->
						<span class="line-row"></span>
					</view>
				</view>
				
				<view class="flex  align-items hui w-100 space-between" style="margin-top: 24rpx;">
					<span>课时人数:</span>
					<span style="color: #FF6D6D;">{{item.sign_num}}/{{item.limit_num}}</span>
				</view>
				<view class="flex  align-items hui w-100 space-between" style="margin-top: 24rpx;"  @click.stop="handlePhone(item.user.mobile)">
					<span>联系方式:</span>
					<span class="flex text-blue">{{item.user.mobile}}
					 <image src="../../static/tel.png" mode="" style="width: 36rpx;height: 36rpx;"></image>
					</span>
				</view>
				<view class="flex m-top-8 align-items hui w-100 space-between">
					<span>订单编号:</span>
					<span>{{item.order_no}}</span>
				</view>
				<view class="flex align-items hui w-100 space-between" style="margin-top: 30rpx;">
					<span>预约时间:</span>
					<span>{{item.reservation_time_text}}</span>
				</view>
				<view class="flex  align-items hui w-100 space-between" style="margin-top: 30rpx;">
					<span>开始时间:</span>
					<span>{{item.start_time_text}}</span>
				</view>
				<view class="flex  align-items hui w-100 space-between" style="margin-top: 30rpx;">
					<span>结束时间:</span>
					<span>{{item.end_time_text}}</span>
				</view>
				<view class="btn-box flex w-100">
					<span class="Refuse flex justify-center align-items" @click="Refuse(item.order_no)"> 拒绝 </span>
					<span class="disable flex justify-center align-items"
						v-if=" item.limit_num != 0 && item.limit_num == item.sign_num">通过</span>
					<span class="pass flex justify-center align-items" v-else @click="Pass(item.order_no)">通过</span>

				</view>
			</view>
		</view>
		<!-- 拒绝原因 -->
		<u-popup :show="show" mode="bottom" :round="10" :zIndex="999999" :custom-style="popupStyle" @close="close"
			@open="open">
			<view class="popupBox flex justify-start align-items flex-column">
				<view class="pop-header flex align-items" style="justify-content: space-between;">
					<span class="selectTime">拒绝原因</span>
				</view>
				<!-- <span class="line"></span> -->
				<view class="times flex align-items justify-center" style="width: 690rpx;margin-top: 43rpx;">
					<u-radio-group v-model="radiovalue1" placement="column" @change="groupChange">
						<u-radio :customStyle="{marginBottom: '8px'}" v-for="(item, index) in radiolist1" :key="index"
							:label="item.name" :name="item.name" @change="radioChange">
						</u-radio>
					</u-radio-group>
				</view>
				<view class="" style="width: 690rpx;margin-bottom: 65rpx;">
					<u--textarea v-model="textTarea" placeholder="请输入内容" count height="100"
						maxlength="50"></u--textarea>
				</view>
				<view class="pos flex justify-center align-items">
					<span class="flex justify-center align-items" @click="confirm()">确认拒绝</span>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				limit: 10,
				status: '-1',
				keywords: '',
				list: [],
				textTarea: '',
				selected: null,
				show: false,
				radiolist1: [{
						name: '课程人数已满',
						disabled: false
					},
					{
						name: '不符合上课资格',
						disabled: false
					},
					{
						name: '不满足开课标准',
						disabled: false
					}, {
						name: '其他',
						disabled: false
					}
				],
				// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
				radiovalue1: '苹果',
				popupStyle: {
					// bottom: '166rpx',
					width: '100%',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center'
				},
				order_no: '',
				shop_id:'',
				shopName:'',

			};
		},
		onLoad() {
			this.shop_id = uni.getStorageSync('shop_id')
			this.shopName = uni.getStorageSync('apply_info').name
			this.getList()
		},
		methods: {
			// 拒绝弹窗
			Refuse(order_no) {
				this.order_no = order_no
				this.show = true
			},
			open() {

			},
			// 搜索
			search(){
				this.getList()
			},
			close() {
				this.selected = null
				this.show = false
			},
			groupChange(n) {
				console.log('groupChange', n);
			},
			radioChange(n) {
				console.log('radioChange', n);
			},
			handlePhone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			getList() {
				uni.$u.http.get('/api/school/worker/hour_order/order_list', {
					params: {
						page: this.page,
						limit: this.limit,
						status: this.status,
						keywords:this.keywords,
						// shop_id:this.shop_id
					}
				}).then(res => {
					if (res.code == 1) {
						this.list = res.data.list
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 通过
			Pass(order_no) {
				uni.$u.http.post('/api/school/worker/hour_order/examine', {
					order_no: order_no,
					auth_status: 1,
					reason: ""
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '通过',
							icon: 'none',
							duration: 2000
						});
						this.show = false
						this.getList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 拒绝
			confirm() {
				uni.$u.http.post('/api/school/worker/hour_order/examine', {
					order_no: this.order_no,
					auth_status: 2,
					reason: this.textTarea
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '拒绝已通过',
							icon: 'none',
							duration: 2000
						});
						this.show = false
						this.getList()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.w-100 {
		width: 100%;
	}

	.w-400 {
		width: 400rpx;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}

	.w-100 {
		width: 100%;
	}

	.m-top-8 {
		margin-top: 8rpx;
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
	}

	.box {
		height: 100vh;
		background-color: #F1F2F8;

		.header {
			width: 750rpx;
			height: 110rpx;
			background-color: white;
		}
	}

	.search {
		// margin-top: 27rpx;
		width: 690rpx;
		height: 64rpx;
		background: #FFFFFF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008CFF;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008CFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

		}
		.searchBtn{
			width: 128rpx;
			height: 64rpx;
			background: #008CFF;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.center {
		width: 690rpx;
		background-color: #F1F2F8;
		margin-top: 14rpx;

		.center-box {
			align-items: self-start;
			padding: 30rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 24rpx;

			.title {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 32rpx;
				color: #343434;
			}

			.right {
				width: 414rpx;
				margin-left: 16rpx;

				.yyRen {
					margin: 8rpx 0;
				}

				.line-row {
					margin-top: 27rpx;
					width: 414rpx;
					height: 1rpx;
					background: #eeeeee;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
				}
			}
		}
	}

	.btn-box {
		justify-content: flex-end;
		align-items: center;
		margin-top: 40rpx;

		span {
			width: 100rpx;
			height: 48rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
		}

		.Refuse {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #008CFF;
			border: 2rpx solid #008CFF;
			margin-right: 24rpx;
		}

		.disable {
			background: #D9D9D9;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
		}

		.pass {
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
		}

	}

	.popupBox {
		// height: 572rpx;
		width: 750rpx;
		position: relative;

		.pos {
			position: absolute;
			bottom: 0;
			width: 750rpx;
			height: 122rpx;
			background: #FFFFFF;
			box-shadow: 0rpx -6rpx 12rpx 0rpx rgba(111, 190, 255, 0.1);
			position: fixed;
			bottom: 0;

			span {
				width: 642rpx;
				height: 80rpx;
				background: #008CFF;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 14px;
			}
		}

		.pop-header {
			width: 93%;
			padding-top: 32rpx;
			padding-bottom: 17rpx;

			border-bottom: 2rpx solid #D9D9D9;

			.selectTime {
				width: 168rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 42rpx;
				color: #008CFF;
			}

			.quxiao {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			padding-top: 21rpx;

			.popur-right {
				align-items: self-start;
				margin-left: 24rpx;
				width: 418rpx;

				.name {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 28rpx;
					color: #343434;
				}

				.address {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
					line-height: 32rpx;
					margin: 16rpx 0 12rpx 0;
					overflow: hidden;
					/* 确保超出容器的文本被隐藏 */
					white-space: nowrap;
					/* 确保文本在一行内显示 */
					text-overflow: ellipsis;
					/* 使用省略号表示被截断的文本 */
					width: 100%;
				}

				.date {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
					line-height: 32rpx;
				}

				.time {
					margin: 12rpx 0 73rpx 0;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
					line-height: 32rpx;
				}

				.line-row {
					width: 418rpx;
					height: 1rpx;
					background: #008CFF;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
				}

				.price {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
					line-height: 32rpx;
					margin-top: 23rpx;
					align-self: flex-end;

					span {
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 36rpx;
						color: #FF2323;
						line-height: 32rpx;
					}
				}
			}
		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #D9D9D9;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}


	}
</style>