<template>
	<view class="box flex flex-column align-items">

		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'"
			:titleStyle="{color:'#000000',fontSize:'34rpx',fontWeight:'bold'}"></u-navbar>
		</view>

		<view class="center flex flex-column align-items">
			<view class="center_box">
			<view class="third-top flex align-items">
				<span>主理人申请
					<image class="icons" src="/static/detail/xiangqing.png"></image>
				</span>
			</view>	
			<view style="padding:60rpx 30rpx 30rpx 30rpx;">
				<!-- <view class='text_fl'>
					<view class="text_lab">
						类型
					</view>
					<view class="flex align-items" @click="show = true">
						<input type="text" placeholder="请选择类型" disabled class="input" v-model="formData.type"
							placeholder-class="plasty" />
						<u-icon name="arrow-right" color="#323232"></u-icon>
					</view>
				</view> -->
				<view class='text_fl' v-if="formData.type == '机构'">
					<view class="text_lab">
						机构名称
					</view>
					<view>
						<input type="text" placeholder="请输入名称" class="input" v-model="formData.name"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row" v-if="formData.type == '机构'"></view>
				<view class='text_fl'>
					<view class="text_lab">
						机构联系人
					</view>
					<view>
						<input type="text" placeholder="请输入机构联系人姓名" class="input" v-model="formData.realname"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view class='text_fl'>
					<view class="text_lab">
						机构联系电话
					</view>
					<view>
						<input type="text" placeholder="请输入机构联系电话" class="input" v-model="formData.mobile"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view class='text_fl'>
					<view class="text_lab">
						机构地址
					</view>
					<view class="flex align-items" @click="chooseMap()">
						<input type="text" placeholder="请选择" class="input" v-model="formData.address" disabled
							placeholder-class="plasty" />
						<u-icon name="arrow-right" color="#202020"></u-icon>
					</view>
				</view>
				<view class="line-row"></view>
				<view class='text_fl'>
					<view class="text_lab">
						详细地址
					</view>
					<view style="width: 400rpx;">
						<input type="text" placeholder="请输入详细地址" class="input" v-model="formData.desc"
							style="width: 400rpx;" placeholder-class="plasty" />
					</view>
				</view>
				
				<view class="btn_1" @click="submitaply()">立即申请</view>
			</view>
			</view>
		</view>
		<!-- 机构类型 -->
		<u-picker :show="show" :columns="columns" keyName='name' @confirm="confirm" @cancel="cancel"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				option:{},
				show:false,
				columns: [
					[{
						name: '机构'
					},
					{
						name: '个人'
					}]
				],
				formData:{
					name: '',
					mobile: '',
					realname: '',
					address: '',
					type: '机构',
					desc:'',
				}
			};
		},
		onLoad(option) {
			this.option = option;
		},
		methods:{
			chooseMap() {
				const that = this
				uni.chooseLocation({
					success: function(res) {
						console.log(res)
						that.formData.address = res.name
						// that.form.address_detail = res.address
						// that.form.latitude = res.latitude
						// that.form.longitude = res.longitude
					},
					fail: function(rot) {
						console.log(rot)
					}
				});
			},
			confirm(e) {
				console.log(e)
				this.formData.type = e.value[0].name
				this.show = false
			},
			cancel() {
				this.show = false
			},
			submitaply() {
			const token = uni.getStorageSync('token');
			if (!token) {
				uni.showToast({
					title: '请登录',
					icon: 'none',
					duration: 2000,
					complete: function () {
						setTimeout(function () {
							uni.switchTab({
								url: '/pages/my/index',
							});
						}, 2000);
					}
				});
				return;
			}
			if (this.formData.type == '') {
				uni.showToast({
					title: '请选择类型',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			if (this.formData.realname == '') {
				uni.showToast({
					title: '请填写联系人！',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			if (this.formData.mobile == '') {
				uni.showToast({
					title: '请填写联系电话！',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			// 手机号正则
			const phoneReg = /^1[3-9]\d{9}$/;
			if (!phoneReg.test(this.formData.mobile)) {
				uni.showToast({
					title: '请输入正确的手机号！',
					icon: "none",
				})
				return;
			}
			
			if (this.formData.name == '' && this.formData.type == '机构') {
				uni.showToast({
					title: '请填写机构名称！',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			if (this.formData.address == '') {
				uni.showToast({
					title: '请选择地址！',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			if (this.formData.desc == '') {
				uni.showToast({
					title: '请填写详细地址！',
					icon: "none",
					duration: 2000,
				});
				return;
			}
			let formparms  = { 
				type: this.formData.type == '个人' ? '1' : '2',
				name: this.formData.name,
				realname: this.formData.realname,
				mobile: this.formData.mobile,
				address: this.formData.address+this.formData.desc
			};
			uni.$u.http
				.post("/api/school/shop/apply", formparms)
				.then((res) => {
					console.log(res);
					if (res.code == 1) {
						uni.showToast({
							title: '提交成功，等待审核！',
							icon: "none",
							duration: 2000,
						});
						this.formShow = false;
						this.formData = {
							name: '',
							mobile: '',
							realname: '',
							address: '',
							desc:'',
							type: '机构',
						};
						setTimeout(()=>{
							uni.navigateBack();
						},1500)
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
				.catch((error) => {
					uni.showToast({
						title: "请求失败，请稍后再试",
						icon: "none",
						duration: 2000,
					});
				});
			},
			// 返回我的
			gomy() {
				uni.switchTab({
					url: "/pages/my/index"
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.w-100 {
		width: 100%;
	}
	.w-400{
		width: 400rpx;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}
	.hui{
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}
	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
	}
	.box{
		width: 750rpx;
		height: 100vh;
		background-color: #FFFFFF;
		.center{
			width: 750rpx;
			height: 1300rpx;
			background-image: url('https://naweigetetest2.hschool.com.cn/dyqc/zlrbg.png');
			background-size: 100%;
			background-repeat: no-repeat;
			.center_box{ 
				width: 750rpx;
				height: 986rpx;
				background-image: url('https://naweigetetest2.hschool.com.cn/dyqc/zlrform.png');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				margin-top: 660rpx;
			}
			.third-top {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #323232;
				line-height: 50rpx;
				margin-top: 60rpx;
				margin-left: 30rpx;
				position: relative;
				z-index: 10;
				span {
					position: relative;

					.icons {
						width: 43rpx;
						height: 20rpx;
						position: absolute;
						left: 0;
						bottom: 0;
						z-index: -1;
					}
				}
			}
			.text_fl{
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			.text_lab{
				color: #323232;
				font-weight: 600;
				font-family: PingFang SC, PingFang SC;
				font-size: 32rpx;
			}
		
			.line-row {
				margin: 40rpx 0rpx;
				height: 1px;
				width: 100%;
				background: #EEEEEE;
			}

			.input {
				text-align: right;
				font-family: PingFang SC, PingFang SC;
				font-size: 32rpx;
				color: #323232;
			}

			.plasty {
				color: #9C9C9C;
				font-weight: 300;
				font-size: 32rpx;
				font-family: PingFang SC, PingFang SC;
			}

			

			.btn_1 {
				width: 100%;
				height: 90rpx;
				background: #323232;
				border-radius: 198rpx 198rpx 198rpx 198rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #BBFC5B;
				line-height: 90rpx;
				text-align: center;
				margin-top: 50rpx;
			}
		}
	}

</style>
