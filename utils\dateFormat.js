const dayjs = require('../node_modules/dayjs');
const localeData = require('dayjs/plugin/localeData');
const zhLocale = require('dayjs/locale/zh-cn');

dayjs.extend(localeData); // 使用插件
dayjs.locale(zhLocale); // 设置本地化为中文

export const dateJs=(time)=>{
	return dayjs.unix(time). format('YYYY-MM-DD HH:mm')
}

export const dateFormat = time=>{
	return dayjs.unix(time).format('YYYY-MM-DD')
}

export const dateFormats = time=>{
	return dayjs(time).format('YYYY-MM-DD')
}

export const dateJsing = time =>{
	return dayjs(time).format('YYYY-MM-DD HH:mm')
}

export const dateWeek = time =>{
	return dayjs.unix(time).format('ddd MM-DD HH:mm')
}
export const dateWeekData = time =>{
	return dayjs(time).format('MM-DD HH:mm')
}
export const dateWeekbeg = time =>{
	return dayjs.unix(time).format('MM-DD(ddd) HH:mm')
}
export const dateWeekend = time =>{
	return dayjs.unix(time).format('HH:mm')
}