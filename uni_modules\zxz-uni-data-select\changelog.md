## 1.0.20（2024-05-09）
1.修复APP点击下拉框无法关闭
## 1.0.19（2024-04-29）
1.修复点击多个下拉框无法关闭
2.优化多选类型支持一次选择多个
3.修复小程序点击有个蓝色底
4.优化下拉菜单溢出屏幕底部时改为向上弹出
## 1.0.15（2023-11-24）
1.优化多选选中样式（tianheng20**qq.com网友提供）
2.优化chang事件（chang事件中将返回所选中的对象）
## 1.0.14（2023-10-25）
优化vue3延时添加未渲染问题，处理37；分号警告问题
## 1.0.13（2023-10-12）
优化mixinDatacomResData报错和defValue报错
## 1.0.12（2023-09-27）
修复搜索输入内容的时候下拉框的箭头会跑到文本框前面去
优化当有选中项时不显示清除按钮
## 1.0.11（2023-09-05）
更换change事件执行顺序
修复多选更改值时未即时更改下拉框选项
修复单选搜索框选中了
修复多选筛选输入时点击其他未清空筛选值
## 1.0.10（2023-08-29）
修复单选搜索回显问题
## 1.0.9（2023-08-28）
更新文档
## 1.0.8（2023-08-28）
更新文档
## 1.0.7（2023-08-16）
修复组件禁用bug
修复数据回显问题
添加多选搜索功能
## 1.0.6（2023-08-05）
修复清空值多选下拉列表还是被选中bug
## 1.0.5（2023-07-10）
修复多选初始化异步数据不显示问题
## 1.0.4（2023-07-07）
修复微信小程序多选显示兼容问题
## 1.0.3（2023-07-06）
修复bug
多选情况下   初始化之后重新选择第一个不显示
## 1.0.2（2023-07-06）
更新VUE3兼容
## 1.0.1（2023-06-30）
添加多选合并功能
## 1.0.0（2023-06-16）
添加下拉框检索，多选功能，自定义数据
## 1.0.4（2023-06-16）
 添加下拉框检索，多选功能，自定义数据
