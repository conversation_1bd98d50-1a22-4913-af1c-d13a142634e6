import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex); // vue的插件机制

// Vuex.Store 构造器选项
const store = new Vuex.Store({
  state: {
    number: 0,
  },
  mutations: {
    setNumber(state, newValue) {
      state.number = newValue; // 修改正确的 state 属性
    }
  },
  actions: {
    setNumber(context, value) {
      context.commit('setNumber', value); // 使用正确的 mutation 名称
    },
  },
  getters: {
    number: state => state.number // 定义 number getter
  },
  modules: {}
})

export default store