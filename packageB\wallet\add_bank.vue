<template>
	<view class="page">
		<view style="margin-top: 20rpx;padding: 0 30rpx;">
			<view style="display: flex;align-items: center;justify-content: space-between;padding: 20rpx 0;">
				<view style="font-size: 32rpx;font-weight: 600;">
					开户名
				</view>
				<view style="width: 80%;">
					<input placeholder-class="plasty" placeholder="请输入您的姓名" class="input" v-model="form.name" />
				</view>
			</view>
			<view style="height: 1px;background-color: #eeeeee;margin: 20rpx 0;"></view>
			<view style="display: flex;align-items: center;justify-content: space-between;padding: 20rpx 0;">
				<view style="font-size: 32rpx;font-weight: 600;">
					身份证
				</view>
				<view style="width: 80%;">
					<input type="idcard" placeholder-class="plasty" placeholder="请输入您的身份证号" class="input" v-model="form.id_number" />
				</view>
			</view>
			<view style="height: 1px;background-color: #eeeeee;margin: 20rpx 0;"></view>
			<view style="display: flex;align-items: center;justify-content: space-between;padding: 20rpx 0;">
				<view style="font-size: 32rpx;font-weight: 600;">
					开户行
				</view>
				<view style="width: 80%;">
					<input placeholder-class="plasty" placeholder="请输入开户行例:(工商银行南昌路支行)" class="input"
						v-model="form.bank_name" />
				</view>
			</view>
			<view style="height: 1px;background-color: #eeeeee;margin: 20rpx 0;"></view>
			<view style="display: flex;align-items: center;justify-content: space-between;padding: 20rpx 0;">
				<view style="font-size: 32rpx;font-weight: 600;">
					卡号
				</view>
				<view style="width: 80%;">
					<input placeholder-class="plasty" placeholder="银行卡号" class="input" v-model="form.bank_user_name" />
				</view>
			</view>
			<view style="height: 1px;background-color: #eeeeee;margin: 20rpx 0;"></view>
		</view>
		<view class="btn_1" @click="save()">保存</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			form: {
				name: '',
				bank_name: '',
				bank_user_name: '',
				id_number: '',
			},
		}
	},
	onLoad() {
		this.getBankList();
	},
	methods: {
		getBankList() {
			uni.$u.http
				.get("/api/school.newactivity.settle_log/detail")
				.then((res) => {
					console.log(res);
					if(res.data.withdrawal!=''&&res.data.withdrawal!=null){
						this.form.name = res.data.withdrawal.name;
						this.form.id_number = res.data.withdrawal.id_number;
						this.form.bank_name = res.data.withdrawal.bank_name;
						this.form.bank_user_name = res.data.withdrawal.bank_user_name;
					}
				});
		},
		save() {
			if (this.form.name == '') {
				uni.showToast({
					title: '请输入开户名',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			if (this.form.bank_name == '') {
				uni.showToast({
					title: '请输入开户行',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			if (this.form.bank_user_name == '') {
				uni.showToast({
					title: '请输入银行卡号',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			if (this.form.id_number == '') {
				uni.showToast({
					title: '请输入身份证号',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			//正则判断身份证
			const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
			if(!reg.test(this.form.id_number)){
				uni.showToast({
					title: '请输入正确的身份证号',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			//正则判断银行卡不低于13位
			const regBank = /^[1-9]\d{12,}$/;
			if(!regBank.test(this.form.bank_user_name)){
				uni.showToast({
					title: '请输入正确的银行卡号',
					icon: "none",
					duration: 1500,
				});
				return;
			}
			uni.$u.http
				.post("/api/school.newactivity.settle_log/save", {
					name: this.form.name,
					bank_name: this.form.bank_name,
					bank_user_name: this.form.bank_user_name,
					id_number: this.form.id_number,
				})
				.then((res) => {
					console.log(res);
					if (res.code == 1) {
						uni.showToast({
							title: '保存成功',
							icon: "none",
							duration: 1500,
							complete: function () {
								setTimeout(function () {
									uni.navigateBack();
								}, 1500);
							}
						});
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	font-family: PingFang SC Bold, PingFang SC Bold;
}

.input {
	text-align: right;
	width: 100%;
}

.plasty {
	font-weight: 300;
	font-size: 28rpx;
	color: #9C9C9C;
}

.btn_1 {
	width: 95%;
	height: 90rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 32rpx;
	color: #BBFC5B;
	line-height: 90rpx;
	text-align: center;
	margin: 0 auto;
	position: fixed;
	bottom: 5%;
	left: 0;
	right: 0;
}
</style>
