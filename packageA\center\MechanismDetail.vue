<template>
	<view class="box flex align-items justify-center flex-column">
		<view class="header flex  align-items flex-column">
			<view class="hea-top flex align-items">
				<image :src="detail.logo" mode=""
					style="width: 160rpx; height: 160rpx;margin: 48rpx 32rpx 48rpx 30rpx;border-radius: 20rpx;"></image>
				<view class="hea-top-right flex flex-column" style="align-items: flex-start;">
					<span class="name  white-space">{{detail.name}}</span>
					<span class="phone flex align-items" @click.stop="handlePhone(detail.tel)">
						<image src="../../static/tel.png" mode=""
							style="width: 28rpx; height: 28rpx;margin-right: 14rpx;"></image>
						联系方式: {{detail.tel}}
					</span>
					<span class="address flex align-items"
						@click.stop="toMap(detail.latitude,detail.longitude,detail.address_detail)">
						<image src="../../static/address.png" mode=""
							style="width: 28rpx; height: 28rpx;margin-right: 14rpx;">
						</image>
						{{detail.address_detail}}

					</span>
				</view>
			</view>
			<!-- <span class="line"></span> -->
			<!-- 专家团队 -->
			<view class="teacher flex flex-column">
				<view class="teacher-top">
					<view class="left flex align-items">
						<span>专家团队</span>
					</view>
				</view>

				<view class="teaBox flex align-items" style="overflow-y: hidden;">
					<view class="tea flex  align-items" style="align-items: self-start;"
						v-for="(item,index) in teacherList" :key="index" @click="toTeacher(item.id)">
						<view class="teaDetail flex align-items flex-column">
							<view class="tea-top flex align-items">
								<image :src="item.head_image" mode=""
									style="width: 112rpx;height: 112rpx;border-radius: 100%;"></image>
								<view class="tea-right flex flex-column ">
									<span class="name">{{item.name}}</span>
									<!-- <span class="mechanism">{{item.shop.name}}</span> -->
								</view>
							</view>
							<view class="detail">
								<view class="" style="margin-top: 40rpx;">
									<image src="/static/index/polygon.png" mode="" style="width: 20rpx;height: 16rpx;">
									</image>
									<span>本科学习数字媒体艺术专业</span>
								</view>
								<view class="white-space" style="margin-top: 12rpx; margin-bottom: 12rpx;">
									<image src="/static/index/polygon.png" mode="" style="width: 20rpx;height: 16rpx;">
									</image>
									<span>带领学生参加全国中职技能大赛获得市级视频剪带领学生参加全国中职技能大赛获得市级视频剪...</span>
								</view>
								<view class="" style="margin-bottom: 40rpx;">
									<image src="/static/index/polygon.png" mode="" style="width: 20rpx;height: 16rpx;">
									</image>
									<span>参与省级教师培训活动并获得考核培训证书</span>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 	<view class="teaBox flex align-items" style="overflow-y: hidden;">
					<view class="tea flex  align-items" style="align-items: self-start;" v-for="(item,index) in teacherList"
						:key="index">
						<view class="teaDetail flex align-items" @click="toTeacher(item.id)">
							<image :src="item.expert_image" mode="" style="width: 200rpx;height: 250rpx;"></image>
							<view class="tea-right flex flex-column ">
								<span class="name white-space">{{item.name}}</span>
								<span class="line-row"></span>
								<span class="pingfen">教师评分</span>
								<span class="rate">
									<u-rate :count="count" active-color="#FFBC2C" v-model="item.teacher_star" allowHalf="true" readonly></u-rate>
								</span>
							</view>
						</view>
					</view>
				</view> -->
			</view>

			<!-- 全部课程 -->
			<view class="totalClass flex flex-column">
				<view class="total-top">
					<view class="left flex align-items">
						<span>全部课程</span>
					</view>
				</view>
				<view class="totalBox">
					<view class="flex" v-for="(item,index) in list" :key="index"
						style="margin-top: 30rpx; height: 304rpx;width: 330rpx;background: #FFFFFF;border-radius: 16rpx;">
						<view class="con-center justify-start flex  flex-column" style="align-items: flex-start;"
							@click="toDetail(item.id)">
							<image :src="item.headimage" mode=""
								style="width: 330rpx; height: 168rpx;border-radius: 20rpx;"></image>
							<span class="jita white-space">{{item.title}}</span>
							<span class="price" v-if="item.feel == 0">￥{{item.price}}</span>
							<span class="price" v-if="item.feel == 1">公益</span>
							<view class="mingePos flex justify-center align-items">
								<image src="/static/index/fire.png" mode=""
									style="width: 22rpx;height: 24rpx;margin: 0 8rpx 0 12rpx;">
								</image>
								<span class="minge">{{ Number(item.sign_num + item.virtual_num)}} /
									{{item.limit_num}}</span>
							</view>
						</view>
					</view>
				</view>
				<view class="more flex flex-column align-items">
					<span class="line-lone"></span>
					<span class="line-erect"></span>
				</view>
			</view>
			<span class="jigouDetail flex flex-column">
				<view class="jigou-top">
					<view class="left flex align-items">
						<span>机构详情</span>
					</view>
				</view>
				<u-parse :content="detail.content"></u-parse>
			</span>
		</view>


		<!-- <view class="centerBox flex align-items flex-column">
			<view class="center-header">
				<image src="../../static/my/Expert.png" mode="" style="width: 168rpx; height: 32rpx;"></image>
			</view>
			<view class="center-center flex align-items">
				<view class="flex align-items center-detail" v-for="(item,index) in detail.teachers" :key="index">
					<image :src="item.head_image" mode="" class="img"></image>
					<view class="flex flex-column text" >
						<span class="name">{{item.name}}</span>
						<span class="line-row"></span>
						<span class="detail flex" v-html="item.expert_content"></span>
						<span class="more">更多 <image src="../../static/my/right.png" mode="" style="width: 24rpx; height: 24rpx;"></image> </span>
					</view>
				</view>
			</view>
		</view> -->


		<u-loadmore :status="loadStatus" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 1,
				detail: {},
				teacherList: [],
				list: [],
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				last_page: 0,
				count: 5
			};
		},
		onShareTimeline() {
			return {
				title: this.detail.name, //分享的标题
				imageUrl: this.detail.logo, //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
				query: `id=${this.id}`
			}
		},
		onLoad(option) {
			this.id = option.id
			this.getDetail(this.id)
			this.getList(this.id)
		},
		onReachBottom() {
			if (this.page < this.last_page) {
				this.page++;
				this.getList(this.id);
			}
		},
		methods: {
			// 获取机构详情
			getDetail() {
				uni.$u.http.get('/api/school/shop/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
						this.teacherList = res.data.detail.teachers
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 导航
			toMap(latitude, longitude, name) {
				uni.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					success: function() {
						console.log('success');
					}
				});
			},
			// 拨号
			handlePhone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			// 机构全部课程
			getList(id) {
				uni.$u.http.get('/api/school/classes/classes_list', {
					params: {
						page: this.page,
						limit: this.limit,
						shop_id: id,
						order: 'normal'
					}
				}).then(res => {
					if (res.code == 1) {
						this.last_page = res.data.list.last_page
						this.list = [...this.list, ...res.data.list.data];
						if (this.list.length >= res.data.list.total) {
							this.loadStatus = 'nomore';
						} else {
							this.loadStatus = 'loading';
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						this.loadStatus = 'nomore';
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {
					this.loadStatus = 'nomore';
				});
			},
			// 跳转详情
			toDetail(id) {
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id
				})
			},
			toTeacher(id) {
				uni.navigateTo({
					url: "/packageA/center/teacherDetail?id=" + id
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: #F2F2F2;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		padding-top: env(safe-area-inset-top);

	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.header {
		width: 750rpx;

		.hea-top {
			width: 750rpx;
			height: 256rpx;
			background: #FFFFFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;

			.hea-top-right {
				width: 498rpx;

				.name {
					height: 48rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 32rpx;
					color: #000000;
				}

				.phone {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #A4A4A4;
					margin: 20rpx 0 12rpx 0;
				}

				.address {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #A4A4A4;
				}
			}
		}

		.line {
			width: 690rpx;
			height: 1rpx;
			background: #FFFFFF;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			margin: 36rpx 0 17rpx 0;
		}

		.jigouDetail {
			width: 690rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;
			line-height: 32rpx;
			margin-top: 64rpx;
			.jigou-top {
				.left {
					background-image: url("/static/footer.png");
					background-repeat: no-repeat;
					background-position: left bottom;
			
					span {
						width: 160rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: bold;
						font-size: 40rpx;
						color: #222222;
						line-height: 47rpx;
					}
			
				}
			}
		}
	}

	.hot {
		width: 690rpx;
	}

	.hot-header {
		width: 100%;
		height: 42rpx;
		background: #4974FF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		margin-top: 24rpx;

		image {}
	}

	.h-left {
		margin-left: 14rpx;
	}

	.content {
		width: 100%;
		height: 379rpx;
		overflow-x: auto;
		/* 允许横向滚动 */
		white-space: nowrap;
		background: #EAEAEA;
		border-radius: 8rpx 8rpx 8rpx 8rpx;

		.line {
			height: 316rpx;
			margin: 0 34rpx;
			width: 1rpx;
			background: #D9D9D9;
			margin: 0rpx 34rpx 0 34rpx;
			box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
		}
	}

	.ovr:first-child {
		margin-left: 34rpx;
	}

	.ovr:last-child {
		margin-right: 34rpx;
	}

	.teacher {
		margin-top: 64rpx;
		width: 690rpx;

		.teacher-top {
			.left {
				background-image: url("/static/footer.png");
				background-repeat: no-repeat;
				background-position: left bottom;

				span {
					width: 160rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 40rpx;
					color: #222222;
					line-height: 47rpx;
				}

			}
		}

		.teaBox {
			margin-top: 30rpx;
			// height: 462rpx;
			// overflow-x: auto;
			/* 允许横向滚动 */
			// white-space: inherit;
		}

		.tea {
			width: 640rpx;
			height: 396rpx;
			background: #FFFFFF;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-right: 24rpx;

			.teaDetail {
				width: 100%;
				height: 100%;

				// padding: 32rpx 24rpx;
				.tea-top {
					image {
						margin: 32rpx;
					}
				}

				.tea-right {

					.name {
						width: 432rpx;
						height: 60rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: bold;
						font-size: 40rpx;
						color: #000000;
						line-height: 47rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
					}

					.mechanism {
						width: 432rpx;
						height: 42rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 28rpx;
						color: #000000;
						line-height: 33rpx;
						text-align: left;
						font-style: normal;
						text-transform: none;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;

					}
				}

				.detail {
					width: 100%;
					height: 100%;
					background: url("/static/index/mask.png") no-repeat left bottom, rgba(190, 238, 3, 0.14);

					view {
						height: 36rpx;
						width: 608rpx;
						margin-left: 32rpx;

						span {
							width: 548rpx;
							height: 36rpx;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 24rpx;
							color: #222222;
							line-height: 28rpx;
						}
					}
				}

			}
		}
	}

	.totalClass {
		width: 690rpx;
		margin-top: 66rpx;

		.total-top {
			.left {
				background-image: url("/static/footer.png");
				background-repeat: no-repeat;
				background-position: left bottom;

				span {
					width: 160rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					font-size: 40rpx;
					color: #222222;
					line-height: 47rpx;
				}

			}
		}

		.totalBox {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: center;

			.con-center {
				position: relative;

				.imgPos {
					position: absolute;
					top: 0;
					right: 0;
					z-index: 999;
				}

				.jita {
					width: 244rpx;
					height: 44rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 30rpx;
					color: #000000;
					margin: 24rpx 32rpx 8rpx 32rpx;
				}

				.line-row {
					width: 280rpx;
					height: 1rpx;
					background: #008CFF;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					margin: 12rpx 0 12rpx 0;
				}

				.mingePos {
					min-width: 118rpx;
					height: 40rpx;
					background: rgba(0, 0, 0, 0.7);
					border-radius: 8rpx 8rpx 8rpx 8rpx;
					position: absolute;
					top: 40%;
					right: 3%;
					display: flex;
					align-items: center;

					span {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
					}
				}

				.iconPos {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
					line-height: 32rpx;
					margin-top: 14rpx;
				}

				.mingePos {

					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #474747;
					line-height: 32rpx;
				}

				.price {
					width: 212rpx;
					height: 36rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24rpx;
					color: #FF4810;
					margin-left: 32rpx;
				}

			}
		}

	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}
</style>