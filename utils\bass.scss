/* 弹性布局 */
.flex{
	display: flex;
}
.a-c{
	align-items: center;
}
.a-s{
	align-items: flex-start;
}
.a-e{
	align-items: flex-end;
}
.j-c{
	justify-content: center;
}
.j-sb{
	justify-content: space-between;
}
.j-s{
	justify-content: flex-start;
}
.j-e{
	justify-content: flex-end;
}
.j-sa{
	justify-content: space-around;
}
.w-100{
	width: 100%;
}
.f-w {
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}
/* 文本对齐 */
.t-c{
	text-align: center;
}
.t-l{
	text-align: left;
}
.t-r{
	text-align: right;
}
/* 文本超出省略号 */
.o-1{
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
}
.o-2{
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-line-clamp:2;
	overflow:hidden;
}
.o-3{
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-line-clamp:3;
	overflow:hidden;
}
/* 文字效果 */
.bold{
	font-weight: bold;
}
.text-u{
	text-decoration: underline;
}
.text-l{
	text-decoration: line-through;
}
/* 字号 */
.f18{
	font-size: 18rpx;
}
.f20{
	font-size: 20rpx;
}
.f22{
	font-size: 22rpx;
}
.f24{
	font-size: 24rpx;
}
.f28{
	font-size: 28rpx;
}
.f30{
	font-size: 30rpx;
}
.f32{
	font-size: 32rpx;
}
.f34{
	font-size: 34rpx;
}
.f36{
	font-size: 36rpx;
}
.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

$color-blue: #008CFF;
$color-hui:#D9D9D9;

.bg-blue {
  background: $color-blue;
}
.text-blue{
	color: $color-blue;
}
.bg-hui{
	background: $color-hui;
}