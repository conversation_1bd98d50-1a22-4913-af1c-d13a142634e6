# lsl-protocol-popup

> 目前自测微信小程序. 其他平台可自行测试

## 介绍

1. 支持 居中弹出,底部弹出
2. 支持自定义触发条件
3. 支持自定义主题色
4. 弹窗时可使用配置参数隐藏tabar,避免弹窗出现时可以跳转页面,关闭弹窗自动展示tabbar
5. 自动获取隐私协议名称
6. 支持自行添加 其他协议，通过数组传入other
7. 底部弹出时自动设配安全距离
8. 不依赖第三方弹窗组件
9.  需要在在 微信 配置项 中 添加 "__usePrivacyCheck__" : true 

## 为什么需要隐私协议

###为规范开发者的用户个人信息处理行为，保障用户的合法权益，自2023年9月15日起，对于涉及处理用户个人信息的小程序开发者，微信要求，仅当开发者主动向平台同步用户已阅读并同意了小程序的隐私保护指引等信息处理规则后，方可调用微信提供的隐私接口。

## 使用方法

导入 `uni_modules` 后直接使用即可

### 组件内已处理相关逻辑,开发者仅需要在小程序后台填写所用户隐私保护指引,然后说明引入组件即可

<br/>

- **使用方法  （功能很全，认真看完 参数说明 配置项）** 

```html
<template>
    <view class="container">
		<!-- 隐私协议 -->
		<lsl-protocol-popup title="用户协议和隐私政策提示" predesc="感谢您使用so处cp。为保护您的个人信息安全，在您使用so处cp的服务前，请务必仔细阅读" subdesc='以了解详细内容。如您同意，请点击“同意并继续”并开始使用我们的服务。' top_img='https://xxx.xxx.com/images/home/<USER>' color="#C9935C" :onNeed='false' @agree="getList" :other="other" :title_style="padding-top:60rpx;" open_type='getPhoneNumber|agreePrivacyAuthorization'></lsl-protocol-popup>
    </view>
</template>
```

## 参数说明

|参数|类型|默认值|描述|
|--|--|--|--|
|top_img|String| |顶部图标|
|top_img_style|String| |顶部图标 自定义样式|
|position|String|center|可选 `bottom`,从底部弹出|
|bd_radius|String|18rpx|弹窗圆角|
|color|String|#0396FF|主颜色: 协议名和同意按钮的背景色|
|bgcolor|String|#ffffff|弹窗背景色|
|onNeed|Boolean|true|使用到隐私相关api时触发弹窗,设置为false时初始化弹窗将判断是否需要隐私授权,需要则直接弹出|
|hideTabBar|Boolean|false|是否需要隐藏tabbar,在首页等tabbar页面使用改弹窗时建议改为true|
|title|String|#ffffff|用户隐私保护提示|
|title_style|String| |自定义样式|
|predesc|String|使用前请仔细阅读|协议名称`前`的内容|
|subdesc|String|当您点击同意后，即表示您已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用该服务。|协议名称`后`的内容|
|privacyContractNameCustom|String|''|自定义协议名称,不传则由小程序自动获取|
|agree_btn_text|String|同意并继续|同意按钮文案|
|agree_btn_back_color|String|#333333|同意按钮背景色|
|refuse_tbn_text|String||拒绝按钮文案|
|refuse_pop_close|Boolean|true|点击拒绝是否 关闭协议|
|refuse_tbn_exit|Boolean|true|点击拒绝是否 退出小程序|
|symbol|String| 和 |协议中间的分割符号|
|open_type|String| 'agreePrivacyAuthorization' | `基础库3.0.0 不支持多个` 自定义 Button open-type 有效值 多个进行竖线隔开 ``` open-type="getPhoneNumber|agreePrivacyAuthorization" ``` |
|is_force_phone|Boolean| false |是否强制授权手机号 open_type 参数中需存在 getPhoneNumber 才会生效 |
|show_toast_phone|String|  |是否强制授权手机号 点击拒绝时提醒内容，不填则不提醒 |
|other|Array| |其他协议列表 数据格式如下|

other 数据格式如下：

```javascript
[
    {
        {
           tit:'《用户协议》',
           type:'doc', // doc自行下载打开文档 page跳转页面
           content:'https://cdn.baidu.com/14_dbd7dcc9.docx', // 文档地址/页面跳转地址
        },
        {
           tit:'《用户协议》',
           type:'page', // doc自行下载打开文档 page跳转页面
           content:'/page/xieyi', // 文档地址/页面跳转地址
        },
    }
]
```

<br/>

#### `predesc` 和 `subdesc` 的自定义内容,需要主动换行时在内容中添加实体字符 `&#10;` 即可

**事件组件回调说名**

|参数|描述|
|--|--|
|@agree_call|同意协议|
|@disagree_call|点击拒绝,可以自行处理退出小程序等操作|
|@other_call|点击其他协议回调|
|@get_phone_number|获取授权手机号信息|

