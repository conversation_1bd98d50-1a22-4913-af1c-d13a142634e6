<template>
	<view class="detail_all">
		<!-- <view class="nav">
			<u-navbar :is-back="true" leftIconColor="#FFFFFF" :autoBack='true' title="" :bgColor="background"
				:titleStyle='titleStyle'> </u-navbar>
		</view> -->
		<view class="box flex justify-start flex-column align-items">
			<!-- <view class="swiper">
			<u-swiper :list="detail.images" indicator indicatorActiveColor="#323232" indicatorMode="dot" :indicator-style="{ bottom: '60rpx',zIndex: 999}"
			 height="580rpx" circular></u-swiper>
		</view> -->

			<view class="con-center w-100 flex justify-center flex-column align-items flex-start">


				<view class="first-box flex flex-start flex-column justify-start">

					<view class="flex flex-column">
						<view class="flex">
							<image v-if="detail.images" :src="detail.images[0]" mode=""
								style="width: 159rpx;height: 159rpx;border-radius: 18rpx;"></image>
							<view class="flex align-items flex-column" style="margin-left: 20rpx;">
								<span class="first-name" style="font-weight: 600;">{{ detail.title }}</span>
								<view class="rbot flex align-items">
									<image src="/static/center/address.png" mode=""
										style="width: 32rpx;height: 32rpx;margin-right: 20rpx;"></image>
									<span class="white-space">{{ detail.address }}{{ detail.address_detail }}</span>
								</view>
							</view>
						</view>
					</view>

					<span class="line"></span>
					<span class="flex align-items" style="width: 100%;justify-content: space-between;">
						<view class="tt1">活动时间</view>
						<span style="font-size: 14px;">{{ formattedTime() }}</span>
					</span>
					<span class="line"></span>
					<span class="flex align-items" style="width: 100%;justify-content: space-between;">
						<view class="tt1">数量<span>
								(剩余{{ detail.join_info.stock - detail.join_info.people_number }}张)</span></view>
						<span class="flex align-items">
							<image style="width: 42rpx;height: 42rpx;" src="/static/detail/jian.png" @click="removeMon">
							</image>
							<span style="margin: 0 20rpx;">{{ nummoney }}</span>
							<image style="width: 42rpx;height: 42rpx;" src="/static/detail/jia.png" @click="addMon">
							</image>
						</span>
					</span>
					<span class="line"></span>
					<span class="flex align-items" style="width: 100%;justify-content: space-between;">
						<view class="tt1">支付金额</view>
						<span v-if="detail.offline == 1" style="color: #FF4810;font-weight: 900;">线下活动</span>
						<span v-if="detail.offline == 2 && detail.feel==1" style="color: #FF4810;font-weight: 900;">免费</span>
						<span v-if="detail.offline == 2 && detail.feel==0" style="color: #FF4810;font-weight: 900;">￥{{ priceGem }}</span>
					</span>
					<span class="line"></span>
					<span class="flex align-items" style="width: 100%;justify-content: space-between;">
						<view class="tt1">支付方式</view>
						<span class="flex align-items">
							<image style="width: 44rpx;height: 44rpx;" src="/static/detail/weixin.png"></image>
							<span style="margin-left: 20rpx;">微信支付</span>
						</span>
					</span>
					<span class="line"></span>
					<view
						style="width: 100%;display: flex;align-items: center;justify-content: space-between;padding-bottom:30rpx;">
						<view style="font-size: 28rpx;">
							备注
						</view>
						<view style="width: 75%;">
							<input type="text" placeholder="订单备注" class="input" v-model="desc" />
						</view>
					</view>
				</view>
			</view>
			<view :class="showYes ? 'third flex flex-column yes' : 'third flex flex-column no'"
				style="justify-content: flex-start;" :style="detail.feel==1?'margin-bottom: 280rpx;':''">
				<view class="flex align-items" style="padding: 30rpx;justify-content: space-between;">
					<view class="third-top flex align-items" style="margin: 0px;">
						<span>报名信息
							<image class="icons" src="/static/detail/xiangqing.png"></image>
						</span>
					</view>
					<view style="display: flex;align-items: center;" @click="openUrl('/packageB/team/index')">
						<view>
							<u-icon name="plus" color="#323232"></u-icon>
						</view>
						<view style="font-size: 28rpx;margin-left: 10rpx;">管理报名人</view>
					</view>
				</view>
				<view style="font-size: 24rpx;color: #999999;margin-left: 30rpx;">请填写真实的报名人认证信息</view>

				<view v-for="(item, index) in displayedList" @click="chickTeam(item, index)"
					style="padding:0rpx 30rpx;">
					<view style="padding-top: 40rpx;">
						<view style="display: flex;align-items: center;justify-content: space-between;">
							<view>
								<!-- <view style="font-size: 28rpx;color: #323232;">{{ item.name }} {{item.mobile}}</view> -->
								<view style="font-size: 28rpx;color: #323232;">{{ item.name }}</view>
								<view style="color: #9C9C9C;font-size: 24rpx;margin-top: 10rpx;">身份证 {{ item.idnum }}
								</view>
							</view>
							<view>
								<image v-if="!item.is" src="/static/fabu/nocheck.png"
									style="width: 40rpx;height: 40rpx"></image>
								<image v-else src="/static/fabu/check.png" style="width: 40rpx;height: 40rpx"></image>
							</view>
						</view>
					</view>
					<view style="height: 1px;background-color: #F0F0F0;width: 100%;margin-top: 40rpx;"></view>
				</view>
				<view style="padding: 30rpx;">
					<view v-if="!down" @click="down = true"
						style="padding: 15rpx 0rpx;display: flex;align-items: center;border-radius: 118rpx;width: 260rpx;background: #F7F7F7;margin: 0 auto;justify-content: center;">
						<view style="font-size: 28rpx;">收起全部</view>
						<view style="margin-left: 20rpx;">
							<u-icon name="arrow-up"></u-icon>
						</view>
					</view>
					<view v-if="down" @click="down = false"
						style="padding: 15rpx 0rpx;display: flex;align-items: center;border-radius: 118rpx;width: 260rpx;background: #F7F7F7;margin: 0 auto;justify-content: center;">
						<view style="font-size: 28rpx;">展开全部</view>
						<view style="margin-left: 20rpx;">
							<u-icon name="arrow-down"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="third flex flex-column" style="margin-bottom: 300rpx;justify-content: flex-start;"
				v-if="priceGem > 0">
				<view @click="tipsShow = true" class="flex align-items"
					style="padding: 30rpx;justify-content: space-between;">
					<view class="third-top flex align-items" style="margin: 0px;">
						<span>退款政策
							<image class="icons" src="/static/detail/xiangqing.png"></image>
						</span>
					</view>
					<view style="display: flex;align-items: center;">
						<view
							style="background: #323232;border-radius: 8rpx;text-align: center;padding: 2rpx 10rpx 10rpx 10rpx;">
							<text style="font-size: 22rpx;color: #BBFC5B;">{{ detail.refund_info.title }}</text>
						</view>
						<!-- <view style="margin-left: 10rpx;"><u-icon name="arrow-right"></u-icon></view> -->
					</view>
				</view>
				<view class="third-center" style="padding:0rpx 30rpx 50rpx 30rpx;">
					<view class="refund-policy-table">
						<view class="table-container">
							<!-- 表头 -->
							<view class="table-header">
								<view class="th-item">申请退款时间</view>
								<view class="th-item">退款比例</view>
								<view class="th-item">退款金额</view>
							</view>

							<!-- 表格内容 -->
							<view class="table-body">
								<!-- 随时退 -->
								<view class="table-row" v-for="(item,index) in detail.refund_desc" :key="index">
									<view class="td-item">{{item.refund_time}}</view>
									<view class="td-item">{{item.refund_scale}}</view>
									<view class="td-item">{{item.refund_price}}</view>
								</view>
							</view>

						</view>
					</view>
				</view>
				<!-- <view class="third-center">
				<uni-table border stripe emptyText="暂无更多数据" >
					<uni-tr>
						<uni-th width="160" align="left">申请退款时间</uni-th>
						<uni-th width="80" align="left">退款比例</uni-th>
						<uni-th width="80" align="left">退款金额</uni-th>
					</uni-tr>
					<uni-tr v-if="detail.refund_info.status == 1">
						<uni-td>报名开始后</uni-td>
						<uni-td>0%</uni-td>
						<uni-td>0</uni-td>
					</uni-tr>
					<uni-tr v-if="detail.refund_info.status == 5">
						<uni-td>报名开始至{{ fomartertime }}</uni-td>
						<uni-td>需协商</uni-td>
						<uni-td>需协商</uni-td>
					</uni-tr>
				</uni-table> 
			</view> -->
			</view>
			<u-popup @touchmove.native.stop.prevent :custom-style="{
				width: '690rpx',
				height: 'auto',
				margin: '0 auto',
				display: 'flex',
				justifyContent: 'flex-start',
				alignItems: 'center'
			}" :closeable="false" :show="tipsShow" :round="10" mode="center" :closeOnClickOverlay="false">
				<view style="font-size: 32rpx;font-weight: 400;margin: 20rpx 0 24rpx 0;">退款政策</view>
				<view class="third-center" style="padding: 30rpx;">
					<view class="refund-policy-table">
						<view class="table-container">
							<!-- 表头 -->
							<view class="table-header">
								<view class="th-item">申请退款时间</view>
								<view class="th-item">退款比例</view>
								<view class="th-item">退款金额</view>
							</view>

							<!-- 表格内容 -->
							<view class="table-body">
								<!-- 随时退 -->
								<view class="table-row" v-for="(item,index) in detail.refund_desc" :key="index">
									<view class="td-item">{{item.refund_time}}</view>
									<view class="td-item">{{item.refund_scale}}</view>
									<view class="td-item">{{item.refund_price}}</view>
								</view>
							</view>

						</view>
					</view>
				</view>
				<view class="popup-footer" style="margin-top:30rpx;">
					<view class="zhixiao shows_zhidao" @click="tipsShow = false">我已知晓</view>
				</view>
			</u-popup>
			<!-- <view class="third flex flex-column" style="justify-content: flex-start;margin-bottom: 300rpx;">
				<view class="third-top flex align-items">
					<span>免责声明
						<image class="icons" src="/static/detail/xiangqing.png"></image>
					</span>
				</view>

				<view class="third-center">
					<span style="color: #9C9C9C; font-size: 24rpx;">
						活动均为主理人自行发布并组织的活动，请您注意，我们仅为活动提供平台技术支持，活动后续事项有主理人负责组织，费用均为主理人收取。
						如您因活动发起、组织、撤销、下线、退款等引起的纠纷须由主理人自行解决并承担后果。
					</span>
				</view>

			</view> -->


			<view class="footer flex align-items" style="justify-content: space-between;">
				<span style="margin-bottom: 170rpx; width: 750rpx;margin-left: 20rpx;">
					<cc-protocolBox :agree="agree" @click="protocolClick" :name="protocolArr"
						@clickOne="protocolClick"></cc-protocolBox>
				</span>
				<view class="footer-right flex justify-center align-items">
					<view v-if="detail.feel == 0 && agree" @click="buy()"> 立即支付 </view>
					<view v-if="detail.feel == 1 && agree" @click="buy()"> 确认报名 </view>
					<view class="flex align-items justify-center" v-if="detail.feel == 1 && agree == false"
						style="color: #FFFFFF;background: #C1C1C1;">确认报名</view>
					<view class="flex align-items justify-center" v-if="detail.feel == 0 && agree == false"
						style="color: #FFFFFF;background: #C1C1C1;">立即支付</view>
				</view>
			</view>





			<!-- 购买弹窗 -->
			<u-popup :show="buyShow" mode="center" :round="10" :zIndex="99999" :custom-style="popupStyle"
				@close="buyClose" @open="buyOpen" :safeAreaInsetBottom="false" :closeable="true">
				<view class="popupBox flex justify-start align-items flex-column">
					<view class="pop-header flex align-items flex-column flex-start">
						<span class="name white-space">{{ detail.title }}</span>
						<span class="price">
							<span v-if="detail.feel == 0">￥{{ detail.price }}</span>
							<span v-if="detail.feel == 1">免费</span>
						</span>
						<!-- <image src="../../static/center/buy.png" mode="" style="width: 168rpx; height: 48rpx;">
					</image> -->
					</view>
					<view class="popup flex-column">
						<!-- <span class="first-image flex align-items" style="margin: 16rpx 0;">
						<image src="../../static/center/Alarm.png" mode="" class="icon-size"></image>
						<span>{{formattedTime.formattedTime}} (共计{{formattedTimeList.length}}节)</span>
					</span>
					<span class="first-image flex align-items">
						<image src="../../static/center/address.png" mode="" class="icon-size"></image>
						<span>{{detail.address_detail}}</span>
					</span> -->
						<!-- <image :src="detail.headimage" mode="" style="width: 200rpx; height: 140rpx;"></image>
					<view class="popur-right flex flex-column">
						<span class="name white-space">{{detail.title}}</span>
						<span class="address">地址:{{detail.address_detail}}</span>
						<span class="date">开始时间:{{detail.start_time_text}}</span>
						<span class="time">结束时间:{{detail.end_time_text}}</span>
						<span class="line-row"></span>
						<span class="price">
							课程价格:
							<span v-if="detail.feel == 0">￥{{detail.price}}</span>
							<span v-if="detail.feel == 1">免费</span>
						</span>
					</view> -->
					</view>
					<view class="popup-footers flex " @click="buy()">
						<span v-if="type == 1 && detail.feel == 0">立 即 支 付</span>
						<span v-if="type == 1 && detail.feel == 1">确 认 报 名</span>
						<!-- <image src="../../static/center/price.png" mode="" style="width: 642rpx;height: 80rpx;"></image> -->
						<u-loading-icon :vertical="true" v-if="uloadingShow"></u-loading-icon>
					</view>
				</view>
			</u-popup>


			<!-- 声明 -->
			<u-popup @touchmove.native.stop.prevent :closeOnClickOverlay="false" :closeable="false" :show="show"
				:round="10" mode="center" @close="close" @open="open" :custom-style="popupStylezf">
				<span style="font-size: 40rpx;font-weight: 800;height: 120rpx;">《用户参与须知》</span>
				<scroll-view ref="scrollView" :scroll-top="scrollTop" @scrolltolower="handleScroll" :scroll-y="true"
					style="height: 800rpx;margin-bottom: 24rpx;">
					<view class="popup flex align-items flex-column">
						<rich-text style="text-align: justify;" :nodes="Negotiate"></rich-text>
					</view>
				</scroll-view>
				<view class="popup-footer">
					<!-- <span class="zhixiao" v-if="agreeShow == false">我同意 （{{timeLog}}）</span>
					<span class="zhixiao shows_zhidao" v-if="agreeShow == true" @click="change">我同意</span> -->
					<view
						style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
						<view class="btn_4" @click="show = false">取消</view>
						<view class="btn_3" v-if="agreeShow == false">我同意（{{timeLog}}）</view>
						<view class="btn_2" v-if="agreeShow == true" @click="change">我同意</view>
					</view>
				</view>
			</u-popup>
			<u-popup @touchmove.native.stop.prevent :closeable="false" :show="qunShow" :round="10" mode="bottom">
				<view style="text-align: center;font-size: 32rpx;color: #3D3D3D;padding: 30rpx;font-weight: 600;">活动二维码
				</view>
				<view style="text-align: center;">
					<image :show-menu-by-longpress="true" :src="detail.image"
						style="width: 400rpx;border: 1rpx solid #D5FD99;" mode="widthFix"></image>
				</view>
				<view style="padding: 0rpx 40rpx;">
					<view
						style="font-size: 28rpx;font-weight: 300;color: #9C9C9C;text-align: center;margin-top: 20rpx;">
						长按识别二维码进群</view>
					<view style="font-size: 28rpx;color: #3D3D3D;text-align: center;margin-top: 30rpx;">
						如果无法加入或者开启了群验证，可能是成员已满您即将加入由用户自发组织的户外活动，请知悉</view>
					<view style="font-size: 28rpx;color: #0CA013;text-align: center;margin-top: 30rpx;">
						确认您已知晓《用户协议》的用户义务与责任平台不对活动真实性作担保，请入群自行辨别</view>
				</view>
				<view @click="openUrlSuccess()" class="btn_1">我已知晓</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import {
		dateWeek,
		dateWeekend
	} from '../../utils/dateFormat'
	export default {
		computed: {
			// formattedTitle() {
			// 	if (this.detail.title.length > 9) {
			// 		return this.detail.title.slice(0, 9) + '..';
			// 	}
			// 	return this.detail.title;
			// },

			fomartertime() {
				return dayjs(this.detail.last_time * 1000).format('YYYY-MM-DD HH:mm:ss');
			},
			// formattedTime() {
			// 	const startTime = dateWeek(this.detail.start_time);
			// 	const endTime = dateWeekend(this.detail.end_time);
			// 	return {
			// 		formattedTime: `${startTime} - ${endTime}`
			// 	};
			// }
		},

		data() {
			return {
				timer: null,
				timeLog: 0,
				showYes: true,
				order_no: {},
				qunShow: false,
				style: {
					// 字符串的形式
					img: 'width: 100%'

				},
				tipsShow: false,
				nummoney: 1,
				priceGem: null,
				Negotiate: null, // 入驻协议
				agree: false,
				agreeShow: false,
				protocolArr: "《用户参与须知》",
				value_slide: 0,
				scrollTop: 0,
				userInfo: {},
				path: 'https://testy.hschool.com.cn//uploads/20241219/3406baf51fcc28c63c31ebcee5c9c75e.jpg',
				uloadingShow: false,
				show: false,
				shows: false,
				buyShow: false,
				type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
				id: 1,
				count: 5,
				value: 5,
				order_no: '',
				PayPirce: 0,
				detail: {},
				people: {},
				qrUrl: '',
				is_collect: 0,
				popupStylezf: {
					width: '620rpx',
					padding: '50rpx 40rpx 40rpx 40rpx',
					height: '984rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				popupStyle: {
					width: '690rpx',
					height: '716rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'flex-start',
					alignItems: 'center'
				},
				timeList: [],
				selectedTime: null,
				// indexBackgroundImage: indexBackgroundImage,
				orderId: "",
				classes_lib_spec_id: '',
				order_no2: '',
				mobile: '',
				is_show_model: false, //是否显示分享模态窗
				background: '#ffffff00',
				titleStyle: {
					color: '#FFFFFF'
				},
				oper_data: null,
				teamList: [],
				down: false,
				desc: '',
				teamListJson: ''
			};
		},

		onShareTimeline() {
			return {
				title: this.detail.title, //分享的标题
				imageUrl: this.detail.headimage, //展示的图片，这里是本地路径的写法，也可以写http或https开头的图片路径
				query: `id=${this.id}`
			}
		},

		onLoad(options) {
			// this.loginShow();
			this.userInfo = uni.getStorageSync("userInfo")
			this.id = options.id
			if (options.type == 2) {
				this.type = 2
				this.orderId = options.orderId
			}
			if (options.type == 1) {
				this.type = 1
				this.order_no = options.order_no
				this.create()
			}
			console.log(options.id)
			this.getDetail()
			// this.getShare()
			this.getAgreement();
			this.getMoneyGetm();

		},
		onShow() {
			this.getTeam();
		},
		computed: {
			displayedList() {
				return this.down ?
					this.teamList.filter((_, i) => i < 2) // 保持响应式 
					:
					this.teamList
			}
		},
		methods: {
			formattedTime() {
				const startTime = dateWeek(this.detail.start_time);
				const endTime = dateWeekend(this.detail.end_time);
				return `${startTime} - ${endTime}`
			},
			chickTeam(item, index) {
				console.log(item);
				this.showYes = true;
				this.$set(this.teamList[index], 'is', !this.teamList[index].is);
				//获取this.teamList中is为true的并且拼接成 josn 格式为：[{name:"小明",idnum:"410303199501220515"}]
				let teamList = this.teamList.filter(item => item.is).map(item => ({
					name: item.name,
					idnum: item.idnum,
					mobile:item.mobile,
					open:item.open
				}));
				console.log(teamList);
				var key = JSON.stringify(teamList);
				//再进行url编码
				key = encodeURIComponent(key);
				this.teamListJson = key;
				this.getTeamGetm();
			},
			getTeamGetm() {
				let num = this.nummoney;
				uni.$u.http.post('/api/school.newactivity.order/confirm', {
					activity_id: this.id,
					order_no: this.order_no,
					is_compute: 1,
					num: num || 1,
					desc: this.desc,
					people: this.teamListJson
				}).then(res => {
					console.log(res);
				}).catch(error => {

				});
			},
			getTeam() {
				uni.$u.http.get('/api/school.newactivity.activity_join/people_list').then(res => {
					console.log(res)
					if (res.code == 1) {
						//增加一个字段 is 是否选中
						this.teamList = res.data.list;
						this.teamList = res.data.list.map(item => ({
							...item,
							is: false
						}))
						if (res.data.list.length > 2) {
							this.down = true;
						}
					} else {
						this.teamList = [];
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				})
			},
			openUrlSuccess() {
				uni.showToast({
					title: '订单处理中...',
					icon: 'none',
					duration: 1000
				});
				uni.navigateTo({
					url: '/packageA/my/success?id=' + this.orderInfo.id + '&order_no=' + this.orderInfo.order_no
				})

				// setTimeout(()=>{
				// 	uni.redirectTo({
				// 		url: '/packageA/my/success?id=' + this.orderInfo.id + '&order_no=' + this.orderInfo.order_no
				// 	})
				// },1500)

			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
			addMon() {
				if (this.detail.feel == 1) {
					if (this.nummoney < Number(this.oper_data.free_activity_max_people) &&
						this.nummoney < (this.detail.join_info.stock - this.detail.join_info.people_number)) {
						this.nummoney++
						this.getMoneyGetm();
					} else {
						uni.showToast({
							title: '不能超过免费限制数量和剩余可报名人数',
							icon: 'none',
							duration: 5000
						})
						return;
					}
				} else {

					if (this.nummoney < (this.detail.join_info.stock - this.detail.join_info.people_number)) {
						this.nummoney++
						this.getMoneyGetm();
					} else {
						uni.showToast({
							title: '不能超过剩余数量',
							icon: 'none',
							duration: 5000
						})
						return;
					}
				}
			},
			removeMon() {
				if (this.nummoney > 1) {
					this.nummoney--
					this.getMoneyGetm();
				}
			},
			change() {
				this.agree = true
				this.show = false
			},
			//同意
			handleScroll() {
				this.agreeShow = true
			},
			protocolClick(tag) {
				//timeLog 开始倒计时
				this.timeLog = 5;
				this.timer = setInterval(() => {
					this.timeLog--;
					if (this.timeLog === 0) {
						this.agreeShow = true;
						clearInterval(this.timer);
					}
				}, 1000);
				this.show = true
			},
			loginShow() {
				const token = uni.getStorageSync('token')
				if (token) {
					return;
				} else {
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function() {
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/my/index',
								});
							}, 2000);
						}
					});
				}
			},
			getShare() {
				uni.$u.http.post('/api/wechat_util/link', {
					path: 'packageA/center/detail',
					query: `id=${this.id}`,
				}).then(res => {
					if (res.code == 1) {
						this.qrUrl = res.data.url_link
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			// 时间转换函数
			timeago(timestamp) {
				const now = new Date().getTime(); // 当前时间（毫秒）
				const diff = (now - timestamp * 1000) / 1000; // 时间差（秒）

				if (diff < 60) {
					return `${Math.floor(diff)}秒前`;
				} else if (diff < 3600) {
					return `${Math.floor(diff / 60)}分钟前`;
				} else if (diff < 86400) {
					return `${Math.floor(diff / 3600)}小时前`;
				} else if (diff < 2592000) { // 30天
					return `${Math.floor(diff / 86400)}天前`;
				} else {
					return `${Math.floor(diff / 2592000)}个月前`;
				}
			},

			// 获取订单详情
			getDetail() {
				uni.$u.http.get('/api/school.new_activity/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
						this.priceGem = res.data.detail.price
						this.value_slide = res.data.detail.join_info.percent;
						if (res.data.detail.is_collect != 0) {
							this.is_collect = 1
						} else {
							this.is_collect = 0
						}
						if (this.detail.user.realname) {
							this.detail.user.realname = this.detail.user.realname.slice(0, 1) + 'XX';
						}
						this.mobile = this.detail.user.mobile.slice(0, 3) + 'XXXX' + this.detail.user.mobile.slice(
							7);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			// 报名  0 支付 1 立即购买 2 预约  3确认时间

			sign() {
				this.buyShow = true
				this.type = 3;

				// uni.navigateTo({
				// 	url: '/packageA/center/orderDetail?id=' + this.id
				// })

			},
			buy() {
				this.uloadingShow = true
				this.getMoney()
			},
			// 购买弹窗 type =  0 支付 1 立即购买 2 预约  3确认时间
			buyOpen() {
				this.buyShow = true
				this.type = 1
			},
			// 弹窗 type = 2 0 支付 1 立即购买 2 预约  3确认时间
			open() {
				this.show = true
				this.type = 3
			},


			close() {
				this.type = 0
				this.selectedTime = null
				this.show = false;
				clearInterval(this.timer);
			},
			buyClose() {
				this.type = 0
				this.selectedTime = null
				this.buyShow = false
			},
			// 返回首页
			toIndex() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},

			//分享发布
			sharePoster() { //分享图片给好友按钮的点击事件函数
				let that = this
				this.base64ToFilePath(this.path, (filePath) => {
					console.log(filePath);
					wx.showShareImageMenu({ //分享给朋友
						path: filePath,
						success: (res) => {
							console.log("分享成功：", res);
						},
						fail: (err) => {
							console.log("分享取消：", err);
						},
					})
				})
			},


			base64ToFilePath(base64data, callback) {
				const time = new Date().getTime();
				const imgPath = `${wx.env.USER_DATA_PATH}/addFriends${time}share_qrcode.png`;
				const imageData = base64data.replace(/^data:image\/\w+;base64,/, "");
				const fileSystemManager = uni.getFileSystemManager();

				fileSystemManager.writeFile({
					filePath: imgPath,
					data: imageData,
					encoding: 'base64',
					success: () => {
						callback(imgPath);
					},
					fail: (err) => {
						console.error('Write file failed:', err);
						uni.showToast({
							title: '写入文件失败',
							icon: 'none'
						});
					}
				});
			},

			// 收藏和取消
			Collect(number) {
				uni.$u.http.post('/api/school/classes/collect', {
					id: this.id,
					is_collect: number
				}).then(res => {
					if (res.code == 1) {
						this.is_collect = number
						if (number == 0) {
							uni.showToast({
								title: '取消收藏',
								icon: 'none',
								duration: 2000
							})
						} else {
							uni.showToast({
								title: '收藏成功',
								icon: 'none',
								duration: 2000
							})
						}

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},

			// 取消选择
			cancel() {
				this.selectedTime = null
			},
			selectTime(time) {
				this.selectedTime = time;
				this.classes_lib_spec_id = time.id

			},
			timeSelected(time) {
				return this.selectedTime === time;
			},
			moveScroll() {},
			//获取价格计算
			getMoneyGetm() {
				let num = this.nummoney;
				uni.$u.http.post('/api/school.newactivity.order/confirm', {
					activity_id: this.id,
					order_no: this.order_no,
					is_compute: 1,
					num: num || 1,
					people: this.teamListJson,
					desc: this.desc
				}).then(res => {
					if (res.code == 1) {
						this.priceGem = res.data.order_data.totalprice
						this.order_no = res.data.order_no
						this.oper_data = res.data.oper_data
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
					this.uloadingShow = false
				}).catch(error => {

				});
			},
			// 获取价格并下单
			getMoney() {
				let num = this.nummoney;
				uni.$u.http.post('/api/school.newactivity.order/confirm', {
					activity_id: this.id,
					order_no: this.order_no,
					is_compute: 1,
					num: num || 1,
					desc: this.desc,
					people: this.teamListJson
				}).then(res => {
					if (res.code == 1) {
						this.PayPirce = res.data.order_data.totalprice
						this.order_no = res.data.order_no
						this.create(this.order_no, this.PayPirce)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
					this.uloadingShow = false
				}).catch(error => {

				});
			},
			// 创建订单
			create(order_no, PayPirce) {

				uni.$u.http.post('/api/school.newactivity.order/create', {
					order_no: order_no,
				}).then(res => {
					console.log(res);
					this.orderInfo = res.data.order_info;
					if (res.code == 1) {
						if (PayPirce != 0) {
							this.pament()
						} else {
							this.uloadingShow = false
							uni.showToast({
								title: '报名成功',
								icon: 'success',
								duration: 2000,
							});
							this.getDetail();
							this.qunShow = true;
						}
					} else {
						console.log(res.msg == '请选择正确的报名人数！');
						console.log(res.msg);
						if (res.msg == '请选择正确的报名人数！') {
							this.showYes = false;
						}
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						that.uloadingShow = false
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 支付
			pament() {
				let that = this
				that.uloadingShow = false
				uni.$u.http.post('/api/school.newactivity.pay/payment', {
					type: 'wechat',
					order_no: that.order_no,
					platform: 'miniapp'
				}).then(res => {
					if (res.code == 1) {
						wx.requestPayment({
							timeStamp: res.data.paydata.timeStamp, //时间戳
							nonceStr: res.data.paydata.nonceStr, //随机字符串
							package: res.data.paydata.package, //prepay_id
							signType: res.data.paydata.signType, //签名算法MD5
							paySign: res.data.paydata.paySign, //签名
							success(res) {
								if (res.errMsg == "requestPayment:ok") {
									that.order_no = ''
									// uni.redirectTo({
									// 	url: "/packageA/my/success?status=" + '2,3'
									// })
									//that.orderInfo = res.data.order_info;
									that.getDetail();
									that.qunShow = true;
									console.log('支付成功', res)
								} else {
									that.uloadingShow = false
									console.log('支付失败')
								}
							},
							fail(res) {
								that.uloadingShow = false
								console.log('支付失败', res)
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 获取支付协议文章
			getAgreement() {
				uni.$u.http.get('/api/index/agreement', {
					params: {

					}
				}).then(res => {
					if (res.code == 1) {

						this.Negotiate = (res.data.user_participation_notice).replace(/\<img/gi,
							'<img style="max-width:100%;height:auto" ');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.detail_all {
		background-color: #f7f7f7;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.con-center {
		background: white;
		border-radius: 0 0 44rpx 44rpx;
		position: relative;
	}



	.space-between {
		justify-content: space-between;
	}

	.swiper {
		width: 100%;
		height: 580rpx;
	}

	.box {
		position: relative;
	}

	.topimgs_rmb {
		position: absolute;
		top: -168rpx;
		right: 30rpx;
		width: 201rpx;
		height: 118rpx;
		z-index: 0;
	}

	.topimgs {
		position: absolute;
		top: -120rpx;
		width: 100%;
		z-index: 0;
	}

	.top_texts {
		position: absolute;
		top: -156rpx;
		right: 68rpx;
		z-index: 3;
		color: #ffffff;

		.xiao {
			margin-left: 4rpx;
			font-size: 22rpx;
			font-weight: 400;
		}
	}

	.first-box {
		width: 690rpx;
		background: #FFFFFF;
		// background: url('@/static/detail/conbg.png');
		// padding-left: 24rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;

		// z-index: 1;
		.sigh {
			width: 88rpx;
			height: 40rpx;
			background: #BEEE03;
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 24rpx;
			color: #222222;
		}

		.first-name {
			display: flex;
			justify-content: flex-start;
			width: 510rpx;
			// height: 77rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 28rpx;
			color: #323232;
		}

		.rbot {
			width: 510rpx;
			height: 52rpx;
			font-size: 26rpx;
			color: #323232;
			margin-top: 36rpx;
		}


		.first-mine {
			height: 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

		}

		.first-txt {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;
			line-height: 32rpx;
			margin: 0 6rpx;
		}

		.tt1 {
			font-size: 28rpx;

			span {
				padding-left: 15rpx;
				font-size: 22rpx;
				color: #9C9C9C;
			}
		}

		.first-image {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

			span {
				width: 600rpx;
				// height: 40rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #222222;
			}

			.xieyi {
				background-color: #BBFC5B;
				width: 30%;
				height: 40rpx;
				border-radius: 8rpx;
			}
		}
	}

	.second-box {
		width: 690rpx;
		height: 64rpx;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		margin: 20rpx 0 16rpx 0;


		.number {
			height: 40rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #323232;
			margin-left: 32rpx;
		}

		view {
			span {
				width: 24rpx;
				height: 24rpx;
				background: rgba(255, 255, 255, 0.4);
				border-radius: 24rpx;
				margin: 0 22rpx 0 4rpx;
			}
		}

		.value_slide {
			width: 50%;

			::v-deep .uni-slider-handle-wrapper {
				height: 10rpx;
			}

			::v-deep .uni-slider-handle {
				background: url('@/static/detail/qiu.png') !important;
				border-radius: 0;
				background-size: 52rpx 52rpx !important;
			}

			::v-deep .uni-slider-value {
				color: #323232;

				&::after {
					content: '%';
				}
			}
		}

	}

	.third {
		width: 100%;
		background: #ffffff;
		margin-top: 20rpx;
		border-radius: 44rpx;

		.third-top {
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #323232;
			line-height: 50rpx;
			margin-top: 30rpx;
			margin-left: 30rpx;
			position: relative;
			z-index: 10;
		}

		span {
			position: relative;

			.icons {
				width: 37rpx;
				height: 20rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				z-index: -1;
			}
		}

		.third-center {
			padding: 30rpx;
			overflow: hidden;

			::v-deep .uni-table {
				min-width: 690rpx;
			}

			.v_html {
				word-wrap: break-word;
				word-break: break-all;
			}

			.imgs {
				width: 690rpx;
			}
		}

		.mgbot {
			margin-bottom: 210rpx;
		}



	}

	.line {
		width: 690rpx;
		height: 1rpx;
		background: #eeeeee;
		margin: 30rpx 0;
	}

	.icon-size {
		width: 32rpx;
		height: 32rpx;
		margin-right: 12rpx;
	}


	.footer {
		width: 100%;
		height: 250rpx;
		background: #ffffff;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: fixed;
		z-index: 99;
		/* 绝对定位 */
		bottom: 0;
		/* 定位在底部 */
		left: 0;
		/* 定位在左边 */


		.footer-right {
			position: absolute;
			width: 95%;
			height: 102rpx;
			background: #C1C1C1;
			border-radius: 200rpx;
			margin: 0 auto;
			left: 0;
			right: 0;

			view {
				width: 100%;
				height: 102rpx;
				line-height: 102rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 48rpx;
				color: #BBFC5B;
				background: #323232;
				border-radius: 200rpx;
				text-align: center;
			}
		}
	}


	.popupBox {
		width: 690rpx;
		height: 716rpx;

		.pop-header {
			width: 100%;
			background-image: url("/static/center/bg.png");
			background-repeat: no-repeat;
			background-position: left bottom;
			height: 265rpx;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 20rpx;
				color: #343434;
			}

			.name {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #222222;
				margin-top: 80rpx;
			}

			.price {
				width: 594rpx;
				height: 66rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 44rpx;
				color: #FF4810;
				margin-top: 16rpx;

				span {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 36rpx;
					color: #FF2323;
					line-height: 32rpx;
				}
			}
		}

		.popup {
			display: flex;
			align-items: self-start;
			justify-content: center;
			width: 594rpx;
		}

		.popup-footers {
			position: absolute;
			left: 48rpx;
			bottom: 48rpx;

			span {
				width: 594rpx;
				height: 100rpx;
				background: #222222;
				border-radius: 200rpx 200rpx 200rpx 200rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 32rpx;
				color: #BEEE03;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}


		.line {
			width: 642rpx;
			height: 1rpx;
			background: #eeeeee;
			//box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
			//border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.times {
			width: 93%;

			.selectTime {
				width: 288rpx;
				height: 50rpx;
				background: #FFFFFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 1rpx solid #D9D9D9;
				color: #4B4B4B;
				font-family: 'PingFang SC', 'PingFang SC';
				font-weight: 500;
				font-size: 24rpx;
				padding-left: 15rpx;
				cursor: pointer;
				margin: 24rpx 32rpx 0 0;
				white-space: nowrap;
				/* 防止文本换行 */
				text-overflow: ellipsis;
				/* 超出部分显示省略号 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-align: left;
				/* 文字靠左对齐 */
				line-height: 50rpx;
				/* 垂直居中对齐 */
				box-sizing: border-box;
				/* 确保 padding 和 border 不影响宽度和高度 */
				display: inline-block;
				/* 确保容器内文字正确对齐 */
			}

		}

		.selectTime.selected {
			width: 288rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			background: #008CFF;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 24rpx;
			color: #FFFFFF;
			cursor: pointer;
			margin: 24rpx 32rpx 0 0;

		}
	}

	.share {
		position: fixed;
		color: #FFFFFF;
		right: 0;
		bottom: 190rpx;
		background: linear-gradient(to bottom right, #FE726B, #FE956B);
		padding: 10rpx 10rpx 10rpx 20rpx;
		border-top-left-radius: 50px;
		border-bottom-left-radius: 50px;
		box-shadow: 0 0 20upx rgba(0, 0, 0, .09);
	}

	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;

		// margin: 30rpx 0;
		.zhixiao {
			height: 80rpx;
			background: #E8E8E8;
			//border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #9C9C9C;
			line-height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 100%;
			bottom: 0;
			border-radius: 0rpx 0rpx 20rpx 20rpx;
		}

		.shows_zhidao {
			background-color: #323232;
			color: #BBFC5B;
			font-weight: 400;
			font-size: 36rpx;
		}
	}

	.cancel {
		width: 100vw;
		padding: 30rpx;
		text-align: center;
		background: #FFFFFF;
		color: red;
		font-weight: bold;
		font-size: 30rpx;
	}

	.md-content {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;
		background-color: white;
	}

	.md-content-item {
		margin: 0 70rpx;
		position: relative;
	}

	.md-content-item image {
		width: 100rpx;
		height: 100rpx;
	}

	.md-content-item view {
		margin-top: 15rpx;
		font-size: 28rpx;
	}

	.sharebtn {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0;
	}

	.cu-modal {
		position: fixed;
		bottom: 166rpx;
		left: 0;
		z-index: 999999;
	}

	.gj {
		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 32rpx;
			color: #4B4B4B;

			span {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #4B4B4B;
			}
		}

		.scroll {
			width: 642rpx;
			max-height: 340rpx;

			view {
				margin: 24rpx;
				width: 600rpx;
				height: 56rpx;
				background: #E8E8E8;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #7A7A7A;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #FF5F5F;
				}

				.lan {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 28rpx;
					color: #008CFF;
				}
			}
		}
	}

	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}

	.Poster {
		position: relative;
		top: 21rpx;
		left: 30rpx;
		width: 690rpx;
	}

	.posterClose {
		position: absolute;
		right: 8rpx;
		top: 8rpx;
	}

	.pos {
		position: relative;
	}

	.btnList {
		width: 690rpx;
		position: absolute;
		bottom: 150rpx;
		left: 30rpx;
		display: flex;
		justify-content: space-evenly;

		span {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	.no-scroll {
		overflow: hidden;
		height: 100vh;
	}

	::v-deep ._root {
		padding: 0 10rpx;
	}

	.no-border-button {
		background-color: transparent;
		/* 去掉背景色 */
		border: none;
		/* 去掉边框 */
		padding: 0;
		/* 去掉内边距 */
		margin: 0;
		/* 去掉外边距 */
		display: inline-flex;
		/* 使按钮内容居中 */
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		flex-flow: column;
		height: 80rpx;
		line-height: inherit;

		span {
			width: 250rpx;
			height: 80rpx;
			background: #FFFFFF;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.input {
		text-align: right;
		font-size: 28rpx;
	}

	.btn_1 {
		z-index: 1000;
		width: 95%;
		height: 90rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 90rpx;
		text-align: center;
		margin: 0 auto;
		margin-top: 30rpx;
	}

	.yes {
		background: linear-gradient(180deg, #EFFFD6 0%, #FFFFFF 30%);
	}

	.no {
		background: linear-gradient(180deg, #FFF1D6 0%, #FFFFFF 30%);
	}

	.refund-policy-table {
		width: 100%;

		.table-container {
			width: 100%;
			border: 1px solid #C1C1C1;
			border-radius: 20rpx;
			overflow: hidden;
		}

		.table-header {
			display: flex;
			background-color: #E8E8E8;

			.th-item {
				flex: 1;
				text-align: center;
				font-size: 26rpx;
				color: #323232;
				padding: 24rpx 10rpx;
				border-right: 1px solid #C1C1C1;
				border-bottom: 1px solid #C1C1C1;

				&:first-child {
					flex: 2;
					/* 第一列宽度为其他列的2倍 */
				}

				&:last-child {
					border-right: none;
				}
			}
		}

		.table-body {
			.table-row {
				display: flex;
				border-top: 1px solid #C1C1C1;



				&:first-child {
					border-top: none;
				}

				.td-item {
					flex: 1;
					text-align: center;
					font-size: 26rpx;
					color: #323232;
					padding: 24rpx 10rpx;
					border-right: 1px solid #C1C1C1;
					background-color: #F7F7F7;

					&:first-child {
						flex: 2;
						/* 第一列宽度为其他列的2倍 */
					}

					&:last-child {
						border-right: none;
					}
				}
			}
		}
	}

	.btn_2 {
		width: 50%;
		height: 80rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}

	.btn_3 {
		width: 50%;
		height: 80rpx;
		background: #E2E2E2;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}

	.btn_4 {
		width: 50%;
		height: 80rpx;
		background: #ffffff;
		border: 1px solid #999999;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}
</style>