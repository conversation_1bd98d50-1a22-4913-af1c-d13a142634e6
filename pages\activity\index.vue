<template>
	<view class="box flex flex-column align-items">
		<view class="title_logo">
			<image
			src="/static/index/xiaoxilogo.png"
			mode=""
			style="width: 237rpx; height: 56rpx;"
			></image>
		</view>
		<view class="box_center flex flex-column align-items">
			<view class="top flex align-items justify-between" @click="toSystem">
				<view class="flex align-items">
					<image src="/static/index/xitongxiaoxi.png" mode="" style="width: 70rpx;height: 70rpx;margin-right: 20rpx;"></image>
					<span class="xtxiaoxi">系统消息</span>
				</view>
				<view class="flex align-items">
					<span class="xtNum">{{ numbers }}</span>
					<image src="/static/detail/zidong.png" mode="" style="width: 20rpx;height: 20rpx;margin-left: 20rpx;" ></image>
				</view>
			</view>
			<span class="lines"></span>
			<scroll-view @touchmove.prevent scroll-y="true" class="center flex flex-column">
				<!-- <span class="hui" v-if="unread.length != 0">未读消息</span> -->
				<!-- @click="detail(item.id,item.params)" -->
				<view class="list flex flex-column" v-for="(item,index) in alllist" :key="index">

					<view class="flex align-items left" v-if="item.params.images.length > 0">
						<image :src="item.params.images[0]" mode="" style="width: 80rpx;height: 80rpx;border-radius: 8rpx;"></image>
						<span class="white-space title1">{{ item.params.title }}</span>
					</view>

					<view class="right flex flex-column">
						<view class="flex align-items justify-between" style="margin-top: 30rpx;">
							<span class="white-space title">{{item.title}}</span>
							<span class="time">{{item.createtime_text}}</span>
						</view>
						<span class="text">{{item.desc}}</span>
					</view>

					<!-- <span class="jiao">
						<u-badge :isDot="true" type="success"></u-badge>
					</span> -->
				</view>
			</scroll-view>
			<!-- <u-loadmore :status="loadStatus" /> -->
			<view class="flex flex-column flex-start align-items" v-if="alllist.length == 0" style="margin-top: 240rpx;">
				<image src="/static/message/message.png" mode="" style="width: 256rpx;height: 240rpx;">
				</image>
				<view>暂无活动消息~</view>
			</view>
		</view>
		<tab-bar :tabBarShow="tabBarShow"></tab-bar>
	</view>
</template>

<script>
	import { mapActions } from 'vuex';
	export default {
		 ...mapActions(['number']),
		data() {
			return {
				tabBarShow:null,
				isFetching: false,
				// selected: 'system,classes,order,activity',
				selected: 'order',
				alllist: [],// 活动订单所有消息
				unread_number: '',
				unread_system_number: '',
				unread_activity_number: '',
				keywords: '',
				page: 1,
				limit: 20,
				loadStatus: 'loading',
				status: '',
				mini_type: '',
				type: '',
				list: [],
				show: false,
				id: '',
				title: '标题'
			};
		},
		onShow() {
			this.resetLists();
			this.number()
			this.getinit()
			this.fetchMessages(this.selected);
		},
		computed: {
			numbers() {
				return this.unread_system_number + this.unread_activity_number;
			}
		},
		onReachBottom() {
			this.page++;
			this.fetchMessages(this.selected);
		},
		onPullDownRefresh() {
			uni.showLoading({
				title: '加载中...'
			});
			this.resetLists();
			this.fetchMessages(this.selected);
			setTimeout(()=>{
				uni.hideLoading();
				uni.stopPullDownRefresh();
			}, 2000)
		},
		methods: {
			getinit() {
				uni.$u.http.get('/api/index/init', {}).then(res => {
					if (res.code == 1) {
						if (res.data.base_info.miniapp_activity_swtich == 1) {
							this.tabBarShow = 3;
						} else {
							this.tabBarShow = 2;
						}
					} 
				}).catch(error => {
					console.log(error);
				});
			},
			// 获取时间
			getFormattedDates() {
				const today = new Date();
				const startOfDay = new Date(today);
				startOfDay.setHours(0, 0, 0, 0);

				const endOfDay = new Date(today);
				endOfDay.setHours(23, 59, 59, 999);

				const yesterday = new Date(today);
				yesterday.setDate(today.getDate() - 1);
				const startOfYesterday = new Date(1970, 0, 1, 0, 0, 0, 0); // 1970/01/01 00:00:00
				const endOfYesterday = new Date(yesterday);
				endOfYesterday.setHours(23, 59, 59, 999);

				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');

					return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
				};

				return {
					today: `${formatDate(startOfDay)}-${formatDate(endOfDay)}`,
					yesterday: `${formatDate(startOfYesterday)}-${formatDate(endOfYesterday)}`
				};
			},
			fetchMessages(status) {
				// const dates = this.getFormattedDates();
				this.getList(status, "alllist" ,'1,2','');

				// this.getList(status, 'read', '2', dates.today);
			},
			toSystem(){
				uni.navigateTo({
					url:"/pages/activity/stationStemys"
				})
			},
			// 消息详情
			// detail(id,params){
			// 	const message = JSON.parse(params);
			// 	if (message.event == 'user_auth_need_after'){
			// 		uni.navigateTo({
			// 			url:"/packageA/my/settleIn?shop_id=" + message.shop_id + "&id=" + id
			// 		})
			// 	} else {
			// 		uni.navigateTo({
			// 			url:"/packageA/my/stationDetail?id=" + id
			// 		})
			// 	}
			// },
		
			// 消息未读
			number() {
				uni.$u.http.get('/api/school/message/message_count').then(res => {
					if (res.code == 1) {
						this.unread_system_number = res.data.unread_system_number;
						this.unread_activity_number = res.data.unread_activity_number;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 一键已读
			one() {
				uni.$u.http.get('/api/school/message/batch', {
					params: {
						id: '*',
						status: this.selected,
						mini_type: '',
						event: ''
					}
				}).then(res => {
					if (res.code == 1) {
						this.resetLists();
						this.number();
						this.fetchMessages('system,classes,order');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 获取消息列表
			async getList(status,zt,type,time) {
				uni.$u.http.get('/api/school/message/message_list', {
					params: {
						page: this.page,
						limit: this.limit,
						status: status,
						// mini_type: '',
						type: type,
					},
				}).then(res => {
					if (res.code == 1) {
						this.updateList(zt, res.data.list, res.data.count);
						
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});


			},
			// 更新列表
			updateList(type, list, count) {
				console.log(type, list, count)
				const targetList = this[type];
				this[type] = [...targetList, ...list];
				console.log('this.loadStatus ',this.loadStatus )
				this.loadStatus = targetList.length >= count ? 'nomore' : 'loading';
			},
			// 重置列表
			resetLists() {
				this.page = 1;
				this.alllist = [];
				this.loadStatus = 'loading'
			}
		}
	};
</script>

<style lang="scss" scoped>
	.box {
		background-color: #F1F2F8;
		background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/bgx2.png");
		background-size: 100%;
		background-repeat: no-repeat;
		position: fixed;
		height: 100vh;
		width: 100%;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.justify-center {
		justify-content: center;
	}
	.justify-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}
	.title_logo{
		width: 690rpx;
		margin-top: 110rpx;
		display: flex;
	}
	.box_center{
		margin-top: 30rpx;
		width: 750rpx;
		background: linear-gradient( 180deg, #FFFFFF 0%, #F7F7F7 23%);
		// background: #F7F7F7;
		min-height: 80vh;
		border-radius: 44rpx;
		margin-bottom: 240rpx;
		.lines{
			width: 690rpx;
			height: 1rpx;
			background: #F0F0F0;
		}
		.top{
			width: 690rpx;
			padding: 30rpx;
			.xtxiaoxi{
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 32rpx;
				color: #3D3D3D;
				line-height: 45rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			.xtNum{
				width: 56rpx;
				height: 32rpx;
				line-height: 32rpx;
				background: #FF4810;
				border-radius: 84rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #FFFFFF;
				font-family: PingFang SC, PingFang SC;
				text-align: center;
			}
		}
		.center {
		    width: 690rpx;
			// margin-bottom: 200rpx;
			position: fixed;
			margin-top: 130rpx;
			height: 1140rpx;
			overflow-y: auto;
		.list {
			width: 690rpx;
			background: #FFFFFF;
			border-radius: 18rpx 18rpx 18rpx 18rpx;
			margin-top: 30rpx;
			position: relative;

			.left {
				font-size: 24rpx;
				font-weight: bold;
				margin-top: 40rpx;
				margin-left: 30rpx;
				.title1 {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #9C9C9C;
					margin-left: 20rpx;
					width: 460rpx;
				}
			}

			.right {
				margin-left:30rpx;
				margin-right: 30rpx;
				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 600;
					font-family: PingFang SC, PingFang SC;
					font-size: 30rpx;
					color: #202020;
					line-height: 34rpx;
					width: 360rpx;
				}

				.time {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #999999;
					
				}

				.text {
					margin-top: 20rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 26rpx;
					color: #3D3D3D;	
					margin-bottom: 40rpx;
				}
			}

			// .jiao {
			// 	position: absolute;
			// 	left: 0;
			// 	top: 0;
			// }
		}
	}
	}
	.header {
		width: 690rpx;
		height: 50rpx;
		margin-top: 30rpx;

		.s-header {
			width: 152rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #9E9E9E;
			position: relative;

			.numberPos {
				// width: 45rpx;
				// height: 50rpx;
				position: absolute;
				right: -10rpx;
				top: -4px;
			}
		}

		.s-header.selected {
			width: 152rpx;
			height: 50rpx;
			background: #008CFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 26rpx;
		}
	}

	.yidu {
		border-top: #008CFF 1rpx solid;
		width: 690rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 66rpx;
		margin-top: 18rpx;

		.news {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #9E9E9E;
		}

		.One {
			width: 132rpx;
			height: 40rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 1rpx solid #9E9E9E;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #9E9E9E;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	

	.hui {
		width: 132rpx;
		height: 40rpx;
		background: #E7E7E7;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #9E9E9E;
		margin: 24rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>