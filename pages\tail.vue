<template>
	<view class="app">
		<view class="countdown" :style="{ top: navHeight }" v-if="countdownNum > 0" @click="jump">{{ countdownNum }}s{{ countTip }}</view>
		<image src="https://naweigetetest2.hschool.com.cn/dyqc/kaiping.jpg" mode=""></image>
	</view>
</template>
 
<script>
export default {
	data() {
		return {
			countdownNum: 5,
			timer: null,
			countTip: '跳过',
			navHeight: 200 //胶囊高度
		};
	},
	onLoad() {
		this.computeHeight();
	},
	mounted() {
		this.getCode();
	},
	beforeDestroy() {
		clearInterval(this.timer); // 清除定时器
		this.timer = null;
	},
	methods: {
		//计算胶囊的高度
		computeHeight() {
			// #ifdef APP-PLUS
			this.navHeight = 120 + 'rpx';
			uni.getSystemInfo({
				success: function (res) {
					console.log(res)
				}
			});
			// #endif
 
			// #ifdef H5
			this.navHeight = 120 + 'rpx'; 
			// #endif
			
			// 运行在小程序
			//#ifdef MP-WEIXIN
			uni.getSystemInfo({
				success: data => {
					console.log(data,'data')
					let custom = uni.getMenuButtonBoundingClientRect();
					console.log(custom, 'custom');
					//导航栏高度 = 状态栏到胶囊的间距（胶囊距上距离-状态栏高度） * 2 + 胶囊高度 + 状态栏高度。
					// 100rpx='当前盒子的高度60rpx+距状态栏的高度40rpx'
					this.navHeight = custom.bottom + custom.top - data.statusBarHeight + 100 + 'rpx';
					console.log(this.navHeight, 'navHeight');
				}
			});
			//#endif
		},
		getCode() {
			//倒计时
			this.timer = setInterval(() => {
				if (this.countdownNum > 1) {
					this.countdownNum--;
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					});
				}
			}, 1000);
		},
		jump() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		}
	}
};
</script>
 
<style lang="scss" scoped>
* {
	margin: 0;
	padding: 0;
}
 
.app {
	width: 750rpx;
	height: 100vh;
	position: relative;
	.countdown {
		position: absolute;
		z-index: 99;
		top: 0;
		right: 40rpx;
		width: 120rpx;
		height: 60rpx;
		font-size: 26rpx;
		border-radius: 40rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: #fff;
		text-align: center;
		line-height: 60rpx;
	}
	image {
		width: 100%;
		height: 100%;
	}
}
</style>