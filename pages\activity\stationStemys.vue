<template>
	<view class="box flex flex-column">
		<view class="nav">
			<u-navbar :is-back="true" :autoBack="true" leftIconColor="#323232" bgColor="#FFFFFF">
				<!-- <view
					class="u-nav-slot"
					slot="left"
				>
					<u-icon
						name="arrow-left"
						size="22"
					></u-icon>
				</view> -->
				<view class="nav_txt" slot="center" @click="one">
					<span style="padding-right: 20rpx;font-size: 34rpx;font-weight: bold;">系统消息</span>
					<u-icon size="19" name="/static/clearicon.png"></u-icon>
				</view>
			</u-navbar>
		</view>
		<view style="margin-top: 180rpx;" v-if="list.length > 0">
			<view class="center flex flex-column flex-start" v-for="detail,index in list" :key="index"
				@click="only(detail.id,detail.type)">
				<span class="title">
					<span class="jiao" v-if="detail.type == 1">
						<u-badge :isDot="true" type="error"></u-badge>
					</span>
					{{detail.title}}
				</span>
				<span class="span" style="margin-top: 20rpx;">
					<!-- <u-parse :content="detail.desc" :selectable="true"></u-parse> -->
					<rich-text :nodes="detail.desc" selectable user-select></rich-text>
				</span>
				<span class="span" style="margin-top: 20rpx; color: #999999 ;">{{detail.createtime_text}}</span>
			</view>
		</view>
		<u-loadmore :status="loadStatus" nomoreText="已经到底啦~" v-if="list.length > 0" />
		<view class="flex flex-column flex-start align-items" v-if="list.length == 0" style="margin-top: 600rpx;">
			<image src="/static/message/message.png" mode="" style="width: 256rpx;height: 240rpx;">
			</image>
			<view>暂无消息~</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				limit: 10,
				list: [],
				loadStatus: 'loading',
				count: 0
			};
		},
		onShow() {
			this.resetLists()
			this.getDetail();
		},
		onLoad(option) {
			console.log(option)
			this.resetLists()
		},
		onReachBottom() {
			if (this.list.length < this.count) {
				this.page++;
				this.getDetail();
			}
		},
		methods: {
			// 单独已读
			only(id, type) {

				uni.$u.http.get('/api/school/message/detail', {
					params: {
						id: id,
					}
				}).then(res => {
					if (res.code == 1) {
						// uni.showToast({
						// 	title: '确认已读',
						// 	icon: 'none',
						// 	duration: 2000
						// });
						this.detail(id);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});


			},
			// 消息详情
			detail(id) {
				uni.navigateTo({
					url: "/packageA/my/stationDetail?id=" + id
				})
			},
			getDetail() {
				uni.$u.http.get('/api/school/message/message_list', {
					params: {
						page: this.page,
						limit: this.limit,
						status: 'system,activity',
						type: "1,2",
					}
				}).then(res => {
					if (res.code == 1) {
						this.count = res.data.count;
						this.list = [...res.data.list, ...this.list]
						this.loadStatus = this.list.length >= res.data.count ? 'nomore' : 'loading';
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},

			// 一键已读
			one() {
				uni.$u.http.get('/api/school/message/batch', {
					params: {
						id: '*',
						status: 'system,activity',
						mini_type: '',
						event: ''
					}
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '全部已读',
							icon: 'none',
							duration: 2000
						});
						this.list = [];
						this.page = 1;
						this.getDetail();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 重置列表
			resetLists() {
				this.page = 1;
				this.list = [];
				this.loadStatus = 'loading'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: #F1F2F8;
		width: 750rpx;
		min-height: 100vh;
		margin: 0 auto;

		.nav_txt {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.u-nav-slot {
			width: 64rpx;
			height: 64rpx;
			border-radius: 32rpx 32rpx 32rpx 32rpx;
			border: 1rpx solid #EBEBEB;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.center {
		margin: 30rpx;
		width: 690rpx;
		// height: 278rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		position: relative;

		.span {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 26rpx;
			line-height: 36rpx;
			color: #202020;
		}

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #202020;
			display: flex;
			align-items: center;
			justify-content: start;
		}

		.jiao {
			margin-right: 20rpx;
		}
	}
</style>
<style>
	::v-deep .u-loadmore{
		display: block!important;
	}
</style>