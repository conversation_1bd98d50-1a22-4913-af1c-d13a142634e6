<template>
	<!-- <view>填写发票信息</view> -->
	<view class="container">
		<view class="invoiceInfo">
			<view class="invoiceList-item flex" v-if="num == 1">
				<view class="item-img">
					<!-- <image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;" src="../../static/index/about.png"
						mode=""></image> -->
					<image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;" :src="detail.images[0]" mode="">
					</image>
				</view>
				<view class="item-con">
					<view class="itenCon-actName" style="">{{detail.title}}</view>
					<view class="itenCon-actPrice" style="">￥ {{detailAny.payprice}}</view>
				</view>
			</view>
			<view v-else style="padding: 0 30rpx;">
				<view style="color: #3D3D3D;font-size: 26rpx;font-weight: 400;margin-top: 10rpx;">已选择{{num}}个订单 预计发票金额：￥
					{{totalPrice}}
				</view>
				<view style="color: #999999;font-size: 26rpx;font-weight: 400;margin-top: 10rpx;">预计金额仅作为参考，请以合并后的实际金额为准
				</view>
			</view>
		</view>
		<!-- 表单 -->
		<view style="height: auto;">
			<u-form :model="form" ref="uForm" label-width="auto" labelPostition="left">
				<view class="formBox">
					<u-form-item label="发票类型" prop="invoice_type">
						<view class="typeBOx" style="width: 219px;justify-content: flex-end;">
							<view class="normalInv" @click="handleHeadTp1()">
								<view class="typeImgs">
									<image v-if="form.invoice_type == 'ordinary'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">普通发票</view>
							</view>
							<view class="specialInv" @click="handleHeadTp2()">
								<view class="typeImgs">
									<image v-if="form.invoice_type == 'special'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">专用发票</view>
							</view>
						</view>

					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="发票内容" prop="">
						<view class="typeBOx" style="width: 296px;justify-content: space-between;">
							<view class="typeImgs" @click="explainShow = true">
								<image style="width: 38rpx;height: 38rpx;position: relative;top: 6rpx;"
									src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/conRecord.png" mode="">
								</image>
							</view>
							<view class="normalInv">
								<view class="typeImgs">
									<image style="width: 44rpx;height: 44rpx;" src="/static/fabu/check.png" mode="">
									</image>
								</view>
								<view class="typeText">商品明细</view>
							</view>
						</view>
						<!-- <image style="width: 38rpx;height: 38rpx;" src="/static/tips.png" mode=""></image> -->
						<!-- <u-input inputAlign="right" v-model="form.password"  placeholder="请输入密码" border="none"/> -->
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="抬头类型" prop="head_type">
						<view class="typeBOx" style="width: 219px;justify-content: flex-end;">
							<view class="normalInv" v-if="form.invoice_type == 'ordinary' || form.invoice_type == ''"
								@click="handlePerson()">
								<view class="typeImgs">
									<image v-if="form.head_type == 'personal'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">个人或事业单位</view>
							</view>
							<view class="specialInv" @click="handleCompany()">
								<view class="typeImgs">
									<image v-if="form.head_type == 'corporate'" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>
								</view>
								<view class="typeText">企业</view>
							</view>
						</view>

					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="预设抬头" prop="">
						<view class="form_right" @click="openHeadShow()">
							选择预设抬头类型
							<u-icon name="arrow-right"></u-icon>
						</view>


						<!-- name和range-key绑定的值都是抬头类型的字段-->
						<!-- <picker @change="clickUpTypeShow" name="invoice_header" :value="upTypelist" :range="upTypelist" range-key="invoice_header">
							<view class="form_right">
								选择预设抬头类型
								<u-icon name="arrow-right"></u-icon>
							</view>
						</picker> -->
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>

					<u-form-item label="抬头名称" prop="invoice_header">
						<u-input inputAlign="right" v-model="form.invoice_header" placeholder="请输入抬头名称" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate'"></view>
					
					<u-form-item label="单位税号" prop="tax_id" v-if="form.head_type == 'corporate'">
						<u-input inputAlign="right" v-model="form.tax_id" placeholder="请输入单位税号" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate'"></view>
					
					<u-form-item label="注册地址" prop="enterprise_address" v-if="form.head_type == 'corporate'">
						<u-input inputAlign="right" style="width:400rpx" v-model="form.enterprise_address"
							placeholder="必填" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate'"></view>
					
					<u-form-item label="注册电话" prop="enterprise_phone" v-if="form.head_type == 'corporate'">
						<u-input inputAlign="right" v-model="form.enterprise_phone" type="number" placeholder="必填"
							border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate'"></view>
					
					<u-form-item label="开户银行" prop="bank_deposit" v-if="form.head_type == 'corporate'">
						<u-input inputAlign="right" v-model="form.bank_deposit" placeholder="必填" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;" v-if="form.head_type == 'corporate'"></view>
					
					<u-form-item label="银行账号" prop="bank_number" v-if="form.head_type == 'corporate'">
						<u-input inputAlign="right" v-model="form.bank_number" type="number" placeholder="必填"
							border="none" />
					</u-form-item>
				</view>
				<!-- <view style="background-color: #f5f5f5;height: 40rpx;"></view> -->

				<view class="recipientInfo">
					<u-form-item label="手机号" prop="invoice_reservation_phone">
						<u-input inputAlign="right" v-model="form.invoice_reservation_phone" type="number"
							placeholder="请输入发票预留手机号" border="none" />
					</u-form-item>
					<view class="line-row" style="margin: 10rpx 0;"></view>
					
					<u-form-item label="邮箱地址" prop="invoice_reservation_email">
						<u-input inputAlign="right" v-model="form.invoice_reservation_email" placeholder="请输入发票预留邮箱"
							border="none" />
					</u-form-item>
				</view>


				<!--<u-form-item>
							<u-button type="primary" @click="submit">申请开票</u-button>
						</u-form-item>-->



			</u-form>

			<view style="width: 100%;height: 140rpx;background-color: #f5f5f5;"></view>
		</view>

		<!-- 提交按钮 -->
		<view class="footer-btn">
			<view class="invoiceBtn" @click.stop="submit()">
				<view>申请开票</view>
			</view>
		</view>



		<u-popup :show="headShow == true" :round="22" mode="bottom" @close="closeHeadShow" @open="openHeadShow"
			:custom-style="popupStyletk">
			<view class="popup_tkall">
				<view class="popup_tk">选择预设抬头</view>
				<scroll-view scroll-y="true" class="popup-content">
					<view class="manageList" v-for="(item,index) in headList" :key="index">
						<!-- 专用 -->
						<view class="manageList-item" v-if="item.head_type == 'corporate'" @click="selectHead(item,index)" style="height: 237rpx;">
							<view class="manage-type" v-if="item.head_type == 'corporate'">专用发票抬头</view>
							<view class="manage-type" v-else>个人发票抬头</view>
							<view class="manageLine"></view>
							<view class="manageCon">
								<view class="messContext">
									<view class="textFir">
										<view class="defaultBox" v-if="item.is_default == 1">默认</view>
										<view class="manageName">{{item.invoice_header}}</view>
									</view>
									<view class="textSec">{{item.tax_id}}</view>
								</view>
								<view class="manageEdit" @click.stop="editHead(item.id)">
									<image style="width: 50rpx;height: 50rpx;"
										src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/manageEdit.png"
										mode=""></image>
								</view>
							</view>
						</view>
						<!-- 个人 -->
						<view class="manageList-item" v-else style="height: 203rpx;" @click="selectHead(item,index)">
							<view class="manage-type">个人发票抬头</view>
							<view class="manageLine"></view>
							<view class="manageCon">
								<view class="messContext">
									<view class="textFir">
										<view class="defaultBox">
											默认</view>
										<view class="manageName">洛阳灵睿网络技术有限公司</view>
									</view>
								</view>
								<view class="manageEdit">
									<image style="width: 50rpx;height: 50rpx;" src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/manageEdit.png"
										mode=""></image>
								</view>
							</view>
						</view>
						
					</view>

					<view class="bottom_box flex justify-center align-items" v-if="headList.length == 0">
						<view style="text-align: center;">
							<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
							<view>暂无数据</view>
						</view>
					</view>

				</scroll-view>

				<view class="popup-footer1">
					<view class="headBtn" style="width: 90%;height: 90rpx;background-color: #323232;border-radius: 148rpx;color: #BBFC5B;font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						position: fixed;
						bottom: 66rpx;"
						@click="addHead()">
						<view @click="addHead()">添加抬头</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="explainShow" :round="22" mode="center" @close="explainShow = false" @open="explainShow = true"
			:custom-style="popupStyRe">
			<view class="popup_tkall" style="">
				<view class="popup_tk">发票内容说明</view>
				<view style="font-size: 28rpx;font-weight: 400;color: #3D3D3D;">
					<view>·发票内容将显示详细商品名称与价格信息</view>
					<view style="margin-top: 20rpx;">·部分商家可能开具发票内容为商品所属类别及价格信息，如有特殊需求，请向商家客服咨询。</view>
				</view>

				<view class="popup-footer"
					style="display: flex;justify-content: center;align-items: center;margin-top: 40rpx;">
					<view class="headBtn" style="width: 45%;height: 80rpx;background-color: #ffffff;border-radius: 148rpx;color: #999999;font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						border: 2rpx solid #999999" @click="explainShow = false">
						<view>取消</view>
					</view>
					<view class="headBtn" style="width: 45%;height: 80rpx;background-color: #323232;border-radius: 148rpx;color: #BBFC5B;font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						margin-left: 20rpx;" @click="explainShow = false">
						<view>我已知晓</view>
					</view>
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupStyletk: {
					width: '710rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '800rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				popupStyRe: {
					width: '540rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '384rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				style: {
					// 字符串的形式
					img: 'width: 100%'
				},
				upTypelist: [], //抬头类型列表
				id: 0,
				order_no: '',
				order_nos: '',
				num: '',
				form: {
					order_nos: '',
					head_type: '',
					invoice_type: '',
					invoice_header: '',
					tax_id: '',
					bank_deposit: '',
					bank_number: '',
					enterprise_address: '',
					enterprise_phone: '',
					invoice_reservation_phone: '',
					invoice_reservation_email: ''
				},

				detail: '', //第二个detail
				detailAny: '', //第一个detail
				qrimages: '',
				headShow: false, //预设抬头的遮罩
				page: 1,
				limit: 10,
				headList: [],
				selectHeadIndex: null,
				totalPrice: 0,
				explainShow: false, //发票内容显隐
				// keywords: '',
				id: null,
				order_no: null,
			}
		},
		onLoad(options) {
			this.headShow = false;
			console.log('options:', options.num, options.order_nos);
			this.num = options.num
			this.order_nos = options.order_nos
			if (this.num == 1) {
				this.id = options.id
				console.log('id1', this.id);
				this.getOrderInfo();
			} else {
				this.totalPrice = options.totalPrice
				console.log('totalPrice:', this.totalPrice);
			}
			console.log('order_nos:', this.order_nos, 'totalPrice:', this.totalPrice);
		},
		onShow() {
			console.log('onshow', this.headShow);
			// this.headShow = false;
			this.getHeadList();
		},
		onReady() {
			console.log('设置规则:', this.rules);
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			getOrderInfo() {
				console.log('id', this.id);
				uni.$u.http.get('/api/school.newactivity.order/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail.detail;

						// this.qrList = res.data.detail.ordercode;
						this.qrimages = res.data.detail.ordercode;
						this.detailAny = res.data.detail;
						// this.generateAllQRCodes()
						console.log('qrimages:', this.qrimages);
						console.log('detailAny:', this.detailAny);
						console.log('detail:', this.detail);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			handleHeadTp1() {
				this.form.invoice_type = 'ordinary'
				this.form.head_type = ''
				console.log('1-1', this.form.invoice_type);
			},
			handleHeadTp2() {
				// this.$set(this.form.invoice_type = 'special')
				this.form.invoice_type = 'special'
				this.form.head_type = 'corporate'
				console.log('2-2', this.form.invoice_type);
			},
			handlePerson() {
				console.log('1');
				this.form.head_type = 'personal'
			},
			handleCompany() {
				console.log('2');
				this.form.head_type = 'corporate'
			},
			openHeadShow() {
				this.headShow = true;
				this.getHeadList();
			},
			closeHeadShow() {
				this.headShow = false;
			},
			getHeadList() {
				uni.$u.http.get('/api/school.header/header_list', {
					params: {
						// keywords: this.keywords,
						page: this.page,
						limit: this.limit,
					}
				}).then(res => {
					if (res.code == 1) {
						console.log('res:', res);
						this.headList = res.data.list
						console.log('headlist', this.headList[2]);
						// this.headShow = true;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			selectHead(item, index) {
				console.log('index:', index, 'item:', item.head_type);
				this.form.head_type = item.head_type
				console.log('this.form.head_type', this.form.head_type);
				// if(this.form.head_type == 'ordinary'){
				// 	console.log('edfsd');
				// 	this.form.tax_id = item.tax_id
				// 	this.form.bank_deposit = item.bank_deposit
				// 	this.form.bank_number = item.bank_number
				// 	this.form.enterprise_address = item.enterprise_address
				// 	this.form.enterprise_phone = item.enterprise_phone
				// }
				this.selectHeadIndex = index
				this.form.invoice_type = item.invoice_type
				this.form.invoice_header = item.invoice_header
				this.form.invoice_reservation_phone = item.invoice_reservation_phone
				this.form.invoice_reservation_email = item.invoice_reservation_email
				this.form.tax_id = item.tax_id
				this.form.bank_deposit = item.bank_deposit
				this.form.bank_number = item.bank_number
				this.form.enterprise_address = item.enterprise_address
				this.form.enterprise_phone = item.enterprise_phone

				console.log('5555', this.form);
				this.closeHeadShow();
			},
			addHead() {
				uni.navigateTo({
					url: '/packageB/invoice/addHead'
				})
			},
			editHead(id) {
				this.HeadId = id
				console.log('HeadId', this.HeadId);
				uni.navigateTo({
					url: '/packageB/invoice/addHead?id=' + this.HeadId
				})
			},
			submit() {
				console.log('sub1');
				if (this.form.invoice_reservation_phone == '') {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				if (this.form.invoice_reservation_email == '') {
					uni.showToast({
						title: '请输入邮箱地址',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				if (this.form.invoice_header == '') {
					uni.showToast({
						title: '请输入抬头名称',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				if (this.form.head_type == 'corporate') {
					console.log('判断');
					if (this.form.tax_id == '') {
						uni.showToast({
							title: '请输入单位税号',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					if (this.form.enterprise_address == '') {
						uni.showToast({
							title: '请输入企业注册地址',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					if (this.form.enterprise_phone == '') {
						uni.showToast({
							title: '请输入企业注册电话',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					if (this.form.bank_deposit == '') {
						uni.showToast({
							title: '请输入企业开户银行',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					if (this.form.bank_number == '') {
						uni.showToast({
							title: '请输入银行账号',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					
					//正则判断银行卡不低于13位
					const regBank = /^[1-9]\d{12,}$/;
					if(!regBank.test(this.form.bank_number)){
						uni.showToast({
							title: '请输入正确的银行卡号',
							icon: "none",
							duration: 1500,
						});
						return;
					}
				}
				
				if (!/^1[3-9]\d{9}$/.test(this.form.invoice_reservation_phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				      // return '';
				console.log('submit:', this.order_nos);
				// 如果存在意外引号，可以清理：
				const cleanOrderNos = this.order_nos.replace(/^"+|"+$/g, '');
				uni.$u.http.post('/api/school.header/apply', {
					// order_nos: this.order_nos,
					order_nos: cleanOrderNos,
					head_type: this.form.head_type,
					invoice_type: this.form.invoice_type,
					invoice_header: this.form.invoice_header,
					tax_id: this.form.tax_id,
					bank_deposit: this.form.bank_deposit,
					bank_number: this.form.bank_number,
					enterprise_address: this.form.enterprise_address,
					enterprise_phone: this.form.enterprise_phone,
					invoice_reservation_phone: this.form.invoice_reservation_phone,
					invoice_reservation_email: this.form.invoice_reservation_email
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '发票申请提交成功',
							icon: 'success',
							duration: 2000
						})
						setTimeout(() => {
							uni.navigateBack()
							// this.getInfo();
							// uni.hideLoading();
						}, 2000)

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.custom-btn {
		color: #bbfc5b !important;
	}

	.form_right {
		display: flex;
		align-items: center;
		margin-left: auto;
		color: #848484;
		width: 300rpx;
		padding: 0rpx 0rpx 0rpx 20rpx;
		position: relative;
		left: 2rpx;
		font-size: 26rpx;
		font-weight: 400;
		text-align: right;
		justify-content: flex-end;
	}

	.typeBOx {
		display: flex;
		align-items: center;
		margin-left: auto;

		.normalInv {
			display: flex;
			align-items: center;
			justify-content: center;

			.typeImgs {
				width: 44rpx;
				height: 44rpx;
			}

			.typeText {
				font-size: 30rpx;
				font-weight: 400;
				color: #999999;
				margin-left: 20rpx;
			}

			.typeText2 {
				font-size: 30rpx;
				font-weight: 400;
				color: #323232;
				margin-left: 20rpx;
			}
		}

		.specialInv {
			margin-left: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.typeImgs {
				width: 44rpx;
				height: 44rpx;
			}

			.typeText {
				font-size: 30rpx;
				font-weight: 400;
				color: #999999;
				margin-left: 20rpx;
			}

			.typeText2 {
				font-size: 30rpx;
				font-weight: 400;
				color: #323232;
				margin-left: 20rpx;
			}
		}
	}

	.container {
		min-height: 90vh;
		padding: 30rpx 0;
		background-color: #f5f5f5;

		.invoiceInfo {
			.invoiceList-item {
				display: flex;
				justify-content: left;
				align-items: center;
				background-color: #ffffff;
				width: 100%;
				height: 220rpx;

				.item-img {
					width: 170rpx;
					height: 170rpx;
					margin-left: 40rpx;

				}

				.item-con {
					margin-left: 20rpx;
					width: 60%;
					height: 160rpx;
					position: relative;
					color: #323232;

					.itenCon-actName {
						position: absolute;
						top: 0;
						font-size: 28rpx;
						font-weight: 400;
					}

					.itenCon-actPrice {
						position: absolute;
						bottom: 0;
						font-size: 32rpx;
						font-weight: 900;
					}
				}


			}
		}

		.formBox {
			// width: 100%;
			margin-top: 30rpx;
			background-color: #ffffff;
			padding: 10rpx 30rpx;
			border-radius: 20rpx;

			.line-row {
				margin-top: 25rpx;
				width: 700rpx;
				height: 1rpx;
				background: #F0F0F0;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
		}

		.recipientInfo {
			margin-top: 30rpx;
			background-color: #ffffff;
			padding: 10rpx 30rpx;
			border-radius: 20rpx;
			
			.line-row {
				width: 700rpx;
				height: 1rpx;
				background: #F0F0F0;
				border-radius: 0rpx 0rpx 0rpx 0rpx;
			}
		}

		.footer-btn {
			width: 95%;
			padding: 20rpx 20rpx 60rpx;
			display: flex;
			position: fixed;
			bottom: 0;
			// left: 0;
			right: -2rpx;
			z-index: 99;
			background-color: #f5f5f5;
			color: #bbfc5b;

			.invoiceBtn {
				width: 90%;
				height: 90rpx;
				background-color: #323232;
				border-radius: 148rpx;
				color: #BBFC5B;
				font-size: 36rpx;
				font-weight: 400;
				line-height: 50rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				text-transform: none;
				font-style: normal;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				position: fixed;
				bottom: 66rpx;
				margin-left: 2%;
			}

		}


		.popup_tkall {
			background-color: #ffffff;

			.popup_tk {
				font-size: 32rpx;
				font-weight: 500;
				margin: 12rpx 0 24rpx 0;
				text-align: center;
				color: #3D3D3D;
			}

			.popup-content {
				height: auto;
				padding-bottom: 80rpx;
				// overflow-y: auto;

				.manageList {
					width: 100%;
					padding-top: 20rpx;
					display: grid;
					justify-content: center;
					
					.manageList-item {
						background-color: #f8f8f8;
						padding: 20rpx 0;
						width: 710rpx;
						// margin-left: 20rpx;
						// margin-bottom: 30rpx;
						margin: 0 auto;
						border-radius: 20rpx;
					
						.manage-type {
							width: 100%;
							height: 32rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 28rpx;
							color: #3D3D3D;
							line-height: 32rpx;
							// text-align: center;
							font-style: normal;
							text-transform: none;
							// margin: 20rpx;
							padding: 30rpx;
						}
					
						.manageLine {
							width: 100%;
							height: 1rpx;
							background: #EEEEEE;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
						}
					
						.manageCon {
							display: flex;
							justify-content: space-between;
							// justify-content: center;
							// margin: 20rpx;
							padding: 30rpx;
							align-items: center;
					
							.messContext {
								display: grid;
					
					
								.textFir {
									display: flex;
									justify-content: left;
									align-items: center;
					
					
									.defaultBox {
										width: 68rpx;
										height: 32rpx;
										background: #FFEEEE;
										border-radius: 6rpx;
										color: #EB1B1B;
										border: 2rpx solid #EB1B1B;
										text-align: center;
										font-size: 22rpx;
										font-weight: 500;
										margin-right: 10rpx;
										// padding: 10rpx;
									}
					
									.manageName {
										height: 32rpx;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 28rpx;
										color: #3D3D3D;
										line-height: 32rpx;
										font-style: normal;
										text-transform: none;
									}
								}
					
								.textSec {
									margin-top: 20rpx;
									height: 32rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #999999;
									line-height: 32rpx;
									font-style: normal;
									text-transform: none;
								}
							}
					
							.manageEdit {
								width: 50rpx;
								height: 50rpx;
							}
						}
					}
					

					.manageList-itemSpec {
						background-color: #f8f8f8;
						padding: 20rpx 0;
						width: 690rpx;
						margin-bottom: 20rpx;
						border-radius: 20rpx;
						height: 237rpx;
						// margin-left: 5%;
						// margin: 0 20rpx;

						.manage-type {
							width: 100%;
							height: 32rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 28rpx;
							color: #3D3D3D;
							line-height: 32rpx;
							// text-align: center;
							font-style: normal;
							text-transform: none;
							margin: 20rpx;
						}

						.manageLine {
							width: 100%;
							height: 1rpx;
							background: #EEEEEE;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
						}

						.manageCon {
							display: flex;
							justify-content: space-between;
							margin: 20rpx;
							align-items: center;

							.messContext {
								display: block;

								.textFir {
									display: flex;
									justify-content: left;
									align-items: center;

									.defaultBox {
										width: 68rpx;
										height: 32rpx;
										background: #FFEEEE;
										border-radius: 4rpx 4rpx 4rpx 4rpx;
										border: 1rpx solid #EB1B1B;
										text-align: center;
										font-size: 26rpx;
										font-weight: 500;
										margin-right: 10rpx;
									}

									.manageName {
										// margin-left: 10rpx;
										height: 32rpx;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 28rpx;
										color: #3D3D3D;
										line-height: 32rpx;
										font-style: normal;
										text-transform: none;
									}
								}

								.textSec {
									margin-top: 20rpx;
									height: 32rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #999999;
									line-height: 32rpx;
									font-style: normal;
									text-transform: none;
								}
							}

							.manageEdit {
								width: 50rpx;
								height: 50rpx;
							}
						}

					}

					.manageList-itemPer {
						background-color: #f8f8f8;
						padding: 20rpx 0;
						// width: 90%;
						width: 690rpx;
						margin-bottom: 20rpx;
						border-radius: 20rpx;
						// margin: 0 auto;
						// margin-left: 5%;
						height: 203rpx;

						.manage-type {
							width: 100%;
							height: 32rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							font-size: 28rpx;
							color: #3D3D3D;
							line-height: 32rpx;
							// text-align: center;
							font-style: normal;
							text-transform: none;
							margin: 20rpx;
						}

						.manageLine {
							width: 100%;
							height: 1rpx;
							background: #EEEEEE;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
						}

						.manageCon {
							display: flex;
							justify-content: space-between;
							margin: 20rpx;
							align-items: center;

							.messContext {
								display: block;

								.textFir {
									display: flex;
									justify-content: left;
									align-items: center;

									.defaultBox {
										width: 68rpx;
										height: 32rpx;
										background: #FFEEEE;
										border-radius: 4rpx 4rpx 4rpx 4rpx;
										border: 1rpx solid #EB1B1B;
										text-align: center;
										font-size: 26rpx;
										font-weight: 500;
									}

									.manageName {
										margin-left: 10rpx;
										height: 32rpx;
										font-family: PingFang SC, PingFang SC;
										font-weight: 400;
										font-size: 28rpx;
										color: #3D3D3D;
										line-height: 32rpx;
										font-style: normal;
										text-transform: none;
									}
								}

								.textSec {
									margin-top: 20rpx;
									height: 32rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #999999;
									line-height: 32rpx;
									font-style: normal;
									text-transform: none;
								}
							}

							.manageEdit {
								width: 50rpx;
								height: 50rpx;
							}
						}
					}

				}

				.bottom_box {
					display: grid;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 500rpx;
					position: relative;
					// top:20rpx;
					z-index: 10000;
				}

				.popup-footer {
					width: 100%;
					display: flex;
					justify-content: center;
					align-items: center;

					.headBtn {
						width: 90%;
						height: 90rpx;
						background-color: #323232;
						border-radius: 148rpx;
						color: #BBFC5B;
						font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						position: fixed;
						bottom: 66rpx;
						margin-left: 2%;
					}
				}



			}

			.popup-footer1 {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				.headBtn {
					width: 90%;
					height: 90rpx;
					background-color: #323232;
					border-radius: 148rpx;
					color: #BBFC5B;
					font-size: 36rpx;
					font-weight: 400;
					line-height: 50rpx;
					font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
					text-transform: none;
					font-style: normal;
					display: flex;
					justify-content: center;
					align-items: center;
					text-align: center;
					position: fixed;
					bottom: 66rpx;
					margin-left: 2%;
				}
			}


		}


	}
</style>