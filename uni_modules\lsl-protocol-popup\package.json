{"id": "lsl-protocol-popup", "displayName": "弹窗协议，隐私弹窗，隐私协议，用户协议，授权弹窗", "version": "1.0.12", "description": "支持自定义多种多个添加协议列表，支持自定义弹窗内容,弹出方式,触发条件,主题颜色，已添加2023年9月15日起，微信要求小程序开发者同步用户同意的规则后才能调用隐私接口", "keywords": ["lsl-protocol-popup", "弹窗协议", "隐私弹窗", "用户协议", "授权弹窗"], "repository": "", "engines": {}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "y", "联盟": "y"}}}}}