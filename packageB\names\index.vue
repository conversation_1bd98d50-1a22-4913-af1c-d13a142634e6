<template>
	<view class="page">
		<view class="nav">
		<u-navbar :titleStyle="{color:'#000000',fontSize:'34rpx',fontWeight:'bold'}" :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'#ffffff'" :title="title"
			></u-navbar>
	</view>
		<view style="padding:30rpx;margin-top: 180rpx;" v-if="istype">
			<view style="display: flex;align-items: center;">
				<view style="width: 100%;">
					<input placeholder-class="plasty" placeholder="输入您的昵称" class="input" v-model="form.nickname" />
				</view>
			</view>
		</view>
		<view style="padding:30rpx;margin-top: 180rpx;" v-else>
			<view style="display: flex;align-items: center;">
				<view style="width: 100%;">
					<input placeholder-class="plasty" placeholder="输入您的签名" class="input" v-model="form.bio" />
				</view>
			</view>
		</view>
		<view class="btn_1" @click="save()">保存信息</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			istype:false,
			title:"",
			form: {
				nickname: '',
				bio: ''
			},
		}
	},
	onLoad(options) {
		if (options.type == 'nc') {
			this.istype = true;
			this.title = '修改昵称'
			this.form.nickname = uni.getStorageSync('userInfo').nickname;
		}else{
			this.istype = false;
			this.title = '修改签名'
			this.form.bio = uni.getStorageSync('userInfo').bio;
		}
		
	},
	methods: {
		
		save() {
			if (this.istype) {
				if (this.form.nickname == '') {
					uni.showToast({
						title: '请输入昵称',
						icon: "none",
						duration: 1500,
					});
					return;
				}
			}else{
				if (this.form.bio == '') {
					uni.showToast({
						title: '请输入签名',
						icon: "none",
						duration: 1500,
					});
					return;
				}
			}
			
			
			let nickname = this.form.nickname;
			let bio = this.form.bio;
			uni.$u.http
				.post("/api/user/profile", {
					nickname: nickname,
					bio: bio,
					update_fields: ['nickname','bio']
				})
				.then((res) => {
					console.log(res);
					if (res.code == 1) {
						uni.showToast({
							title: '保存成功',
							icon: "none",
							duration: 1500,
							complete: function () {
								setTimeout(function () {
									uni.navigateBack(1);
								}, 1500);
							}
						});
						if (this.istype) {
							uni.setStorageSync('userInfo', {
								...uni.getStorageSync('userInfo'),
								nickname: nickname
							});
							uni.setStorageSync('niName', nickname);
						}else{
							uni.setStorageSync('userInfo', {
								...uni.getStorageSync('userInfo'),
								bio: bio
							});
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	font-family: PingFang SC Bold, PingFang SC Bold;
}

.input {
	border-radius: 18rpx 18rpx 18rpx 18rpx;
	border: 1rpx solid #707070;
	height: 90rpx;
	padding-left: 40rpx;
	color: #999999;
}

.plasty {
	font-weight: 300;
	font-size: 28rpx;
	color: #999999;
	
}

.btn_1 {
	width: 95%;
	height: 92rpx;
	background: #323232;
	border-radius: 198rpx 198rpx 198rpx 198rpx;
	font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
	font-weight: 400;
	font-size: 36rpx;
	color: #BBFC5B;
	line-height: 92rpx;
	text-align: center;
	margin: 0 auto;
	position: fixed;
	bottom: 5%;
	left: 0;
	right: 0;
}
</style>
