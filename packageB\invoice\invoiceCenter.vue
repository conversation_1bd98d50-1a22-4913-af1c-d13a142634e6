<template>
	<!-- <view>发票中心</view> -->
	<view class="box flex">
		<view class="con-center flex flex-column justify-center align-items">
			<view class="tabs align-items">
				<span class="s-header flex flex-column" v-for="(item, index) in headers" :key="index"
					:class="{ selected: headerSelected(item.index) }" @click="selectheader(item.index)">
					{{ item.text }}
					<span class="lines" :class="{ selected: headerSelected(item.index) }"></span>
				</span>
			</view>

			<view class="record-tabs" v-if="selected == 1">
				<span class="s-header flex flex-column" v-for="(item, index) in recordTypeList" :key="index"
					:class="{ selected: recordIndexSelected(item.status) }" @click="selecRecordIndex(item.status)">
					{{ item.text }}
					<!-- <span class="lines" ></span> -->
				</span>
			</view>

			<!-- 信息提醒 -->
			<view class="manageRecord" v-if="selected == 2">
				<image style="width: 40rpx;height: 36rpx;margin-right: 10rpx;"
					src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/manageRecord.png" mode="">
				</image>
				<view>抬头信息仅用于开具发票，请勿用于转账等其他用途谨防受骗</view>
			</view>

			<view style="min-height: 100vh;margin-top: 80rpx;width: 100%;">
				<!-- 合开发票 -->
				<view v-if="selected == 0">
					<view class="invoiceList" v-for="(item,index) in unInvoiceList" :key="index">
						<view class="invoiceList-item flex" @click="checkInvoice(item.invoiceCheck,index)">
							<view class="changedBox">
								<view class="changedBox-Img">
									<image v-if="item.invoiceCheck === 1" style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/check.png" mode=""></image>
									<image v-else style="width: 44rpx;height: 44rpx;"
										src="../../static/fabu/nocheck.png" mode=""></image>

								</view>
							</view>
							<view class="item-img">
								<image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;"
									:src="item.detail.images[0]" mode=""></image>
							</view>
							<view class="item-con">
								<view class="itenCon-actName" style="">{{item.detail.title}}</view>
								<view class="itenCon-actPrice" style="">￥ {{item.payprice}}</view>
							</view>
						</view>
					</view>
					<!-- <view v-if="unInvoiceList.length == 0" class="invoiceBtn" @click="handleIssueInvoice()">
						<view>申请开票</view>
					</view> -->
					<view class="invoiceBtn" @click="handleIssueInvoice()">
						<view>申请开票</view>
					</view>
					<view class="bottom_box flex justify-center align-items" v-if="unInvoiceList.length == 0">
						<view style="text-align: center;">
							<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
							<view>暂无数据</view>
						</view>
					</view>
				</view>

				<!-- 提交记录 -->
				<view class="recordList" v-else-if="selected == 1">

					<!-- v-if="item[index].length == 1" -->
					<view v-for="(item,index) in recordList" :key="index">
						<!-- 单开 -->
						<view class="recordList-item flex" v-if="item.apply_type == '1'">
							<view class="item-img2">
								<image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;"
									src="../../static/index/about.png" mode=""></image>
							</view>
							<view class="item-con2">
								<view class="itenCon2-actName">{{item.activityorders[0].detail.title}}</view>
								<view class="itenCon2-actPrice">
									<view>￥ {{item.activityorders[0].detail.price}}</view>
									<view v-if="item.status == 2" class="lookInvoice" @click="toPageInvoiceInfo(item)">
										<view>查看发票</view>
										<u-icon name="arrow-right" @click="aaa"></u-icon>
									</view>
									<view class="lookInvoice2" v-else-if="item.status == 1">
										<text>开票中</text>
									</view>
									<view class="lookInvoice3" v-else>
										<text>已作废</text>
									</view>
									<!-- <view style="position: fixed;right: 5%;">
										<view v-if="item.status == 2" class="lookInvoice" @click="toPageInvoiceInfo(item)">
											<view>查看发票</view>
											<u-icon name="arrow-right" @click="aaa"></u-icon>
										</view>
										<view class="lookInvoice2" v-else-if="item.status == 1">
											<text>开票中</text>
										</view>
										<view class="lookInvoices" v-else>
											<text>已作废</text>
										</view>
									</view> -->

								</view>
							</view>
						</view>
						<!-- 合开-->
						<view class="recordList-items flex" v-else>
							<!-- <view></view> -->
							<view class="reListIts-item flex" v-for="(e,i) in item.activityorders" :key="i">
								<view class="itsItem-img2">
									<image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;"
										:src="e.detail.images[0]" mode=""></image>
								</view>
								<view class="itsItem-con2">
									<view class="itsItemCon2-actName">{{e.detail.title}}</view>
									<view class="itsItemCon2-actPrice">
										<view>￥ {{e.detail.price}}</view>
									</view>
								</view>

							</view>
							<!-- <view class="reListIts-item flex">
								<view class="itsItem-img2">
									<image style="width: 170rpx;height: 170rpx;border-radius: 20rpx;"
										src="../../static/index/about.png" mode=""></image>
								</view>
								<view class="itsItem-con2">
									<view class="itsItemCon2-actName">名字</view>
									<view class="itsItemCon2-actPrice">
										<view>￥ 价格</view>
									</view>
								</view>


							</view> -->

							<view class="reLine"></view>
							<view class="reListIts-itemData">
								<view>{{item.activityorders.length}}个订单 发票金额：￥{{item.price}}</view>
								<view class="lookInvoices" v-if="item.status == 2" @click="toPageInvoiceInfo(item)">
									<text>查看发票</text>
									<u-icon name="arrow-right"></u-icon>
								</view>
								<view class="lookInvoices2" v-else-if="item.status == 1">
									<text>开票中</text>
								</view>
								<view class="lookInvoices" v-else>
									<text>已作废</text>
								</view>
							</view>
						</view>
					</view>

					<view class="bottom_box flex justify-center align-items" v-if="recordList.length == 0">
						<view style="text-align: center;">
							<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
							<view>暂无数据</view>
						</view>
					</view>

					<view style="width: 100%;height: 100rpx;"></view>

				</view>

				<!-- 抬头管理 -->
				<view class="manageList" v-else>
					<view v-for="(item,index) in headList" :key="index" style="margin: 0 auto;display: grid;">
						<!-- 专用 -->
						<view class="manageList-item" v-if="item.head_type == 'corporate'"
							style="margin-bottom: 20rpx;height: 237rpx;">
							<view class="manage-type" v-if="item.head_type == 'corporate'">专业发票抬头</view>
							<view class="manageLine"></view>
							<view class="manageCon">
								<view class="messContext" style="position: relative;
							top: 14rpx;">
									<view class="textFir">
										<view class="defaultBox" v-if="item.is_default == 1">默认</view>
										<view class="manageName">{{item.invoice_header}}</view>
									</view>
									<view class="textSec">{{item.tax_id}}</view>
								</view>
								<view class="manageEdit" @click="editHead(item.id)">
									<image style="width: 50rpx;height: 50rpx;"
										src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/manageEdit.png"
										mode="">
									</image>
								</view>
							</view>
						</view>
						<!-- 个人 -->
						<view class="manageList-item" v-else style="height: 203rpx;">
							<view class="manage-type">个人发票抬头</view>
							<view class="manageLine"></view>
							<view class="manageCon">
								<view class="messContext" style="position: relative;
							top: 20rpx;">
									<view class="textFir">
										<view class="defaultBox" v-if="item.is_default == 1">默认</view>
										<view class="manageName">{{item.invoice_header}}</view>
									</view>
									<view class="textSec">{{item.tax_id}}</view>
								</view>
								<view class="manageEdit" @click="editHead(item.id)">
									<image style="width: 50rpx;height: 50rpx;"
										src="https://naweigetetest2.hschool.com.cn/miniapp/invoice/manageEdit.png"
										mode="">
									</image>
								</view>
							</view>
						</view>

					</view>

					<!-- 按钮 -->
					<view class="footer-mangBtn">
						<view class="invoiceBtn" @click="addHead()">
							<view>添加发票抬头</view>
						</view>
					</view>


					<view class="bottom_box flex justify-center align-items" v-if="headList.length == 0">
						<view style="text-align: center;">
							<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
							<view>暂无数据</view>
						</view>
					</view>

				</view>

			</view>

		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupStyle: {
					width: '690rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					padding: '0',
					borderRadius: '20rpx'
				},
				size: 13,
				sortStyle: ['#ff557f', '#3f3f3f'],
				selected: '0',
				headers: [{
						index: '0',
						text: '合开发票'
					},
					{
						index: '1',
						text: '提交记录'
					},
					{
						index: '2',
						text: '抬头管理'
					}
				],
				invoiceCheck: '0', //和开发票选中状态：0未选中，1选中
				recordTypeList: [{
						status: '',
						text: '全部'
					},
					{
						status: '2',
						text: '已开票'
					},
					{
						status: '1',
						text: '未开票'
					}
				], //提交记录的tabs数据
				recordeStatus: '', //提交记录的tabs选中状态
				recordList: [], //提交记录
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				unInvoiceList: [], //可开发票列表
				invoicedList: [], //已开发票列表
				upList: [], //支票抬头列表
				count: 0,
				order_nos: '', //选中的多个订单号
				totalPrice: 0,
				num: 0,
				id: 0,
				headList: [], //抬头管理
				HeadId: null,
			};
		},
		onLoad(option) {
			this.getInvoiceList()
			this.getHeadList()
			this.getRecordList()
		},
		onShow() {
			this.getInvoiceList()
			this.getHeadList()
			this.getRecordList()
		},

		onPullDownRefresh() {

		},
		onReachBottom() {

		},

		methods: {
			//三个列表的切换
			selectheader(index) {
				console.log('111');
				const that = this;
				that.selected = index;
				console.log('selectheader：', that.selected)
				if (that.selected == 0) {
					console.log('合开');
					this.getInvoiceList()
				} else if (that.selected == 1) {
					console.log('记录', this.recordeStatus);

					this.getRecordList()
				} else {
					console.log('抬头');
					this.getHeadList();
				}
			},
			// 判断当前所选中列表
			headerSelected(index) {
				console.log('3333');
				// console.log('index',index);
				return this.selected === index;
			},

			//获取可开发票列表
			getInvoiceList() {
				uni.$u.http.get('/api/school.newactivity.order/order_list', {
					params: {
						page: this.page,
						limit: this.limit,
						// status: 9,
						// keywords: this.keywords,
						invoice_status: 0,
						have_invoice: 1,
					}
				}).then(res => {
					console.log('code', res.code);
					if (res.code === 1) {
						this.count = res.data.count
						this.unInvoiceList = [...res.data.list];
						console.log('unInvoiceList:', this.unInvoiceList);
						if (this.unInvoiceList.length >= res.data.count) {
							this.loadStatus = 'nomore';
						} else {
							this.loadStatus = 'loading';
						}
						this.unInvoiceList.forEach(item => {
							item.invoiceCheck = 0
						})
						// this.isInitialized  = false;  // 标记已初始化 
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.loadStatus = 'loading';
					}
				}).catch(error => {
					console.error('请求失败', error);
					this.loadStatus = 'loading';
				});
			},
			//选择要开发票的订单
			checkInvoice(e, index) {
				this.$set(this.unInvoiceList, index, {
					...this.unInvoiceList[index],
					invoiceCheck: e === 0 ? 1 : 0
				});
			},
			//跳转填写发票信息
			handleIssueInvoice() {
				//拿到选中的订单
				const list = this.unInvoiceList.filter(item => item.invoiceCheck == 1).map(item => item.order_no)
				const list1 = this.unInvoiceList.filter(item => item.invoiceCheck == 1)
				const idList = this.unInvoiceList.filter(item => item.invoiceCheck == 1).map(item => item.id)
				console.log('list:', list, idList);
				// this.order_nos = JSON.stringify(list)
				this.order_nos = JSON.stringify(list.join(','))
				this.num = list.length
				console.log('num:', this.num, list1);
				if (this.num > 1) {
					list1.forEach(e => {
						// if(e.payprice)
						this.totalPrice = this.totalPrice + Number(e.payprice)
					})
				} else if (this.num > 1) {
					this.totalPrice = list1[0].payprice
				} else {

				}

				console.log('num:', this.num, 'totalPrice:', this.totalPrice);
				console.log('unInvoiceList', this.unInvoiceList.length);
				if (this.unInvoiceList.length == 0) {
					uni.showToast({
						title: '暂无可开发票的订单，请先去参加活动下单',
						icon: 'none',
						duration: 2000
					})
					return
				}
				if (list.length < 1) {
					uni.showToast({
						title: '请选择需要开发票的订单',
						icon: 'none',
						duration: 2000
					})
				} else if (list.length === 1) {
					this.id = idList[0]
					console.log('id', this.id);
					uni.navigateTo({
						url: '/packageB/invoice/addInvoice?order_nos=' + this.order_nos + '&num=' + this.num +
							'&id=' + this.id
					})
				} else {
					uni.navigateTo({
						url: '/packageB/invoice/addInvoice?order_nos=' + this.order_nos + '&num=' + this.num +
							'&totalPrice=' + this.totalPrice
					})
				}
				// uni.navigateTo({
				// 	url: '/packageB/invoice/addInvoice?order_nos=' + this.order_nos + '&num=' + this.num 
				// })
			},


			//提交记录
			getRecordList() {
				console.log('提交记录', this.recordeStatus);
				uni.$u.http.get('/api/school.header/apply_list', {
					params: {
						// keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status: this.recordeStatus,
					}
				}).then(res => {
					if (res.code == 1) {
						console.log('res:', res);
						this.recordList = res.data.list
						console.log('recordList', this.recordList);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			//提交记录的列表状态切换
			selecRecordIndex(recordStatus) {
				console.log('selecRecordIndex', recordStatus)
				// const that = this;
				this.recordeStatus = recordStatus;
				console.log('recordeStatus', this.recordeStatus)
				this.getRecordList()
			},
			//判断当前的提交记录的列表选中状态
			recordIndexSelected(recordStatus) {
				console.log('recordIndexSelected', recordStatus);
				return this.recordeStatus === recordStatus;
			},
			//查看发票信息
			toPageInvoiceInfo(item) {
				console.log('toPageInvoiceInfo', item);
				uni.navigateTo({
					url: '/packageB/invoice/invoiceInfo?id=' + item.id
				})
			},

			//抬头管理列表
			getHeadList() {
				uni.$u.http.get('/api/school.header/header_list', {
					params: {
						// keywords: this.keywords,
						page: this.page,
						limit: this.limit,
					}
				}).then(res => {
					if (res.code == 1) {
						console.log('res:', res);
						this.headList = res.data.list
						console.log('headlist', this.headList[2]);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},
			addHead() {
				uni.navigateTo({
					url: '/packageB/invoice/addHead'
				})
			},
			editHead(id) {
				this.HeadId = id
				console.log('HeadId', this.HeadId);
				uni.navigateTo({
					url: '/packageB/invoice/addHead?id=' + this.HeadId
				})
			},

		}

	}
</script>

<style lang="scss" scoped>
	.box {

		background: #f5f5f5;

		.con-center {
			width: 100%;
			height: 100%;
			// margin-top: 25rpx;
			// overflow: hidden;

			.tabs {
				z-index: 1000;
				background: #ffffff;
				width: 100%;
				display: flex;
				overflow-x: auto;
				// margin-top: 10rpx;
				-webkit-overflow-scrolling: touch;
				font-family: PingFang SC, PingFang SC;
				position: fixed;
				top: 0;



				.s-header {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 33.3%;
					height: 80rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #9E9E9E;

					line-height: 26rpx;
					flex: 0 0 auto;
					position: relative;
				}

				.s-header.selected {
					width: 33.3%;
					height: 80rpx;
					background: #ffffff;
					font-weight: 800;
					font-size: 32rpx;
					color: #323232;
					line-height: 28rpx;
				}

				.lines {
					position: absolute;
					bottom: 0;
					width: 44rpx;
				}

				.lines.selected {
					border-bottom: #323232 solid 8rpx;
				}
			}

			.invoiceList {
				width: 100%;
				height: auto;

				.invoiceList-item {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #ffffff;
					width: 91%;
					margin-top: 20rpx;
					padding: 20rpx 30rpx;
					height: 220rpx;

					.changedBox {
						width: 44rpx;
						height: 44rpx;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;

						.changedBox-Img {
							width: 44rpx;
							height: 44rpx;
						}
					}

					.item-img {
						width: 170rpx;
						height: 170rpx;
						margin-left: 40rpx;

					}

					.item-con {
						margin-left: 20rpx;
						width: 60%;
						height: 160rpx;
						position: relative;
						color: #323232;

						.itenCon-actName {
							position: absolute;
							top: 0;
							font-size: 28rpx;
							font-weight: bold;
						}

						.itenCon-actPrice {
							position: absolute;
							bottom: 0;
							font-size: 32rpx;
							font-weight: 900;
						}
					}


				}
				
				
				
			}
			
			.bottom_box {
				display: grid;
				justify-content: center;
				align-items: center;
				width: 100%;
				// height: 900rpx;
				position: absolute;
				top: 40%;
			}

			.invoiceBtn {
				width: 90%;
				height: 90rpx;
				background-color: #323232;
				border-radius: 148rpx;
				color: #BBFC5B;
				font-size: 36rpx;
				font-weight: 400;
				line-height: 50rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				text-transform: none;
				font-style: normal;
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				position: fixed;
				bottom: 66rpx;
				margin-left: 5%;
			}


			.record-tabs {
				background: #f5f5f5;
				width: 100%;
				z-index: 1000;
				background-color: #f5f5f5;
				display: flex;
				overflow-x: auto;
				position: fixed;
				top: 70rpx;
				margin: 10rpx 0;
				padding: 20rpx 0;
				// border-radius: 30rpx;
				-webkit-overflow-scrolling: touch;
				font-family: PingFang SC, PingFang SC;


				.s-header {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 150rpx;
					height: 60rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #9E9E9E;
					border-radius: 40rpx;
					background-color: #ffffff;
					margin-right: 20rpx;
					line-height: 26rpx;
					flex: 0 0 auto;
					position: relative;
					z-index: 1000;
				}

				.s-header.selected {
					width: 150rpx;
					height: 60rpx;
					background-color: #ffffff;
					// font-weight: 800;
					// font-size: 32rpx;
					color: #323232;
					line-height: 28rpx;
					z-index: 1000;
				}

			}


			.recordList {
				width: 100%;
				height: auto;
				display: block;
				justify-content: center;
				margin-top: 100rpx;


				.recordList-item {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #ffffff;
					width: 91%;
					// margin-top: 20rpx;
					margin-bottom: 20rpx;
					padding: 20rpx 30rpx;
					height: 220rpx;
					// margin: 0 auto;

					.item-img2 {
						width: 170rpx;
						height: 170rpx;
						// margin-left: 40rpx;

					}

					.item-con2 {
						margin-left: 30rpx;
						width: 75%;
						height: 160rpx;
						position: relative;
						color: #323232;

						.itenCon2-actName {
							position: absolute;
							top: 0;
							font-size: 28rpx;
							font-weight: bold;
						}

						.itenCon2-actPrice {
							width: 100%;
							position: absolute;
							bottom: 0;
							font-size: 32rpx;
							font-weight: 900;
							display: flex;
							justify-content: space-between;

							.lookInvoice {
								width: 200rpx;
								display: flex;
								position: absolute;
								right: -15%;
								margin-top: 6rpx;
								font-weight: 400;
								font-size: 28rpx;
							}

							.lookInvoice3 {
								width: 200rpx;
								display: flex;
								position: absolute;
								right: -22%;
								margin-top: 6rpx;
								font-weight: 400;
								font-size: 28rpx;
							}

							.lookInvoice2 {
								width: 200rpx;
								color: #ff4810;
								display: flex;
								position: absolute;
								right: -22%;
								margin-top: 6rpx;
								font-weight: 400;
								font-size: 28rpx;
							}
						}
					}
				}

				.recordList-items {
					display: grid;
					background-color: #ffffff;
					width: 91%;
					margin-top: 20rpx;
					padding: 20rpx 30rpx;
					height: auto;
					// margin: 0 auto;

					.reListIts-item {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						margin-bottom: 20rpx;
						// margin: 0 auto;
						// background-color: #9E9E9E;

						.itsItem-img2 {
							width: 170rpx;
							height: 170rpx;
							// margin-left: 40rpx;

						}

						.itsItem-con2 {
							margin-left: 30rpx;
							width: 78%;
							height: 160rpx;
							position: relative;
							color: #323232;

							.itsItemCon2-actName {
								position: absolute;
								top: 0;
								font-size: 28rpx;
								font-weight: bold;
							}

							.itsItemCon2-actPrice {
								width: 100%;
								position: absolute;
								bottom: 0;
								font-size: 32rpx;
								font-weight: 900;
								display: flex;
								justify-content: space-between;

								.lookInvoice {
									width: 200rpx;
									display: flex;
									position: absolute;
									right: -15%;
									font-weight: 400;
									font-size: 28rpx;
								}

								.lookInvoice2 {
									width: 200rpx;
									color: #ff4810;
									display: flex;
									position: absolute;
									right: -15%;
									font-weight: 400;
									font-size: 28rpx;
								}
							}
						}
					}

					.reLine {
						width: 100%;
						height: 1rpx;
						background-color: #D8D8D8;
						margin: 10rpx 0;

					}

					.reListIts-itemData {
						width: 100%;
						margin-top: 20rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.lookInvoices {
							width: 200rpx;
							display: flex;
							position: relative;
							right: -11%;
							font-weight: 400;
							font-size: 28rpx;
						}

						.lookInvoices2 {
							width: 200rpx;
							color: #ff4810;
							display: flex;
							position: relative;
							right: -11%;
							font-weight: 400;
							font-size: 28rpx;
						}
					}
				}
				
				.bottom_box {
					display: grid;
					justify-content: center;
					align-items: center;
					width: 100%;
					// height: 1000rpx;
					// position: absolute;
					// top: 50%;
				}
				
			}

			.manageRecord {
				z-index: 100;
				position: fixed;
				top: 78rpx;
				// width: 100%;
				// margin: 0rpx 20rpx 20rpx 20rpx;
				padding: 30rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 28rpx;
				color: #EB1B1B;
				line-height: 32rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				align-items: center;
				display: flex;
				justify-content: center;
				background-color: #f5f5f5;
				// margin: 0 auto;
			}

			.manageList {
				width: 100%;
				display: grid;
				justify-content: center;
				position: relative;
				top: 120rpx;
				margin: 0 auto;
				row-gap: 20rpx;
				// padding-top: 20rpx;

				.manageList-item {
					background-color: #ffffff;
					padding: 20rpx 0;
					width: 690rpx;
					// margin-left: 20rpx;
					// margin-bottom: 30rpx;
					margin: 0 auto;
					border-radius: 20rpx;

					.manage-type {
						width: 92%;
						height: 32rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 28rpx;
						color: #3D3D3D;
						line-height: 32rpx;
						// text-align: center;
						font-style: normal;
						text-transform: none;
						// margin: 20rpx;
						padding: 30rpx;
					}

					.manageLine {
						width: 100%;
						height: 1rpx;
						background: #EEEEEE;
						border-radius: 0rpx 0rpx 0rpx 0rpx;
					}

					.manageCon {
						display: flex;
						justify-content: space-between;
						// justify-content: center;
						// margin: 20rpx;
						padding: 30rpx;
						align-items: center;

						.messContext {
							display: grid;


							.textFir {
								display: flex;
								justify-content: left;
								// align-items: center;


								.defaultBox {
									width: 68rpx;
									height: 32rpx;
									background: #FFEEEE;
									border-radius: 6rpx;
									color: #EB1B1B;
									border: 2rpx solid #EB1B1B;
									text-align: center;
									font-size: 22rpx;
									font-weight: 500;
									margin-right: 10rpx;
									// padding: 10rpx;
								}

								.manageName {
									height: 32rpx;
									font-family: PingFang SC, PingFang SC;
									font-weight: 400;
									font-size: 28rpx;
									color: #3D3D3D;
									line-height: 32rpx;
									font-style: normal;
									text-transform: none;
								}
							}

							.textSec {
								margin-top: 20rpx;
								height: 32rpx;
								font-family: PingFang SC, PingFang SC;
								font-weight: 400;
								font-size: 28rpx;
								color: #999999;
								line-height: 32rpx;
								font-style: normal;
								text-transform: none;
							}
						}

						.manageEdit {
							width: 50rpx;
							height: 50rpx;
						}
					}
				}

				.footer-mangBtn {
					width: 100%;
					height: 160rpx;
					background-color: #f5f5f5;
					position: fixed;
					bottom: 0;
					left: 0;

					.invoiceBtn {
						width: 90%;
						height: 90rpx;
						background-color: #323232;
						border-radius: 148rpx;
						color: #BBFC5B;
						font-size: 36rpx;
						font-weight: 400;
						line-height: 50rpx;
						font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
						text-transform: none;
						font-style: normal;
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						position: fixed;
						bottom: 66rpx;
						margin-left: 5%;
					}
				}
				
				.bottom_box {
					display: grid;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 1000rpx;
					// position: absolute;
					// top: 50%;
				}
				
			}
			
			
			
		}


		

	}


	.flex {
		display: flex;
	}



	.justify-center {
		justify-content: center;
	}

	.flex-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		width: 630rpx;
		height: 1rpx;
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		margin: 0 30rpx;
	}
</style>