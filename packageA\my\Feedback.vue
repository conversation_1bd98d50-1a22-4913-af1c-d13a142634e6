<template>
	<view class="box flex justify-center align-items flex-column">
		<view class="con-center flex flex-column justify-center align-items">
			<view class="search flex  align-items" style="width: 99%;">
				<span class="dashed flex align-items justify-center">
					<image src="../../static/center/search.png" mode=""></image>
				</span>
				<span class="line-search"></span>
				<input type="text" placeholder="搜索您的评价课程" v-model="keywords"  class="input" placeholder-class="plasty" />
				<span class="searchBtn" @click.stop="search()">搜索</span>
			</view>
			<view class="header flex align-items w-100" style="justify-content:space-around;">
				<span class="s-header flex justify-center align-items" v-for="(item,index) in headers" :key="index"
					:class="{ selected: headerSelected(item.status) }"
					@click="selectheader(item.status)">{{item.text}}</span>
			</view>
			<span class="line"></span>
		
			<view class="centerBox flex justify-start align-items flex-column">
				<view class="flex w-100 center flex-column" style="align-items: center;" v-for="(item,index) in list"
					:key="index">
					<view class="flex" style="width: 690rpx;">
						<image :src="item.detail.headimage" mode="" style="width: 280rpx; height: 200rpx;border-radius: 12rpx;"></image>
						<view class="flex flex-column rightBox">
							<span class="name">{{item.detail.title}}</span>
							<!-- <span class="line-row"></span> -->
							<span class="minge">{{'购买时间 ' + item.paytime_text }}</span>
							<span class="minge">{{'开始时间 ' }}{{item.detail.start_time | formatDateTime}}</span>
							<span class="minge">{{'结束时间 ' }}{{item.detail.end_time | formatDateTime }}</span>
							<span class="Cancel" @click.stop="Cancel(item.id,2)" v-if="item.classes_evaluate_id != 0 && item.evaluate.update_number == 0">修改评价</span>
							<span class="Cancel" @click.stop="Cancel(item.id,1)" v-if="item.classes_evaluate_id == 0">去评价</span>
							<span class="Cancel" @click.stop="Cancel(item.id,3)" v-if="item.classes_evaluate_id != 0 && item.evaluate.update_number == 1">查看评价</span>
						</view>
					</view>
					<!-- <span class="error" v-if="item.auth_status == 2">审核失败:{{item.reason}}</span> -->
					<span class="box-line"></span>
				</view>
			</view>
		</view>
		
		<u-loadmore :status="loadStatus" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				popupStyle: {
					width: '690rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					padding: '0',
					borderRadius: '20rpx'
				},
				sortList: [{
					text: '按照价格排序',
					value: ['acs', 'desc'],
				}, {
					text: '按照编号排序',
					value: ['acs', 'desc'],
				}],
				size: 13,
				sortStyle: ['#ff557f', '#3f3f3f'],
				headers: [{
						status: '2',
						text: '未评价'
					},
					{
						status: '1',
						text: '已评价'
					},
				],
				selected: '2',
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				keywords: '',
				list: [],
				show: false,
				qrcode: {},
				order_no: '',
				count:0
			};
		},
		filters: {
		    formatDateTime(timestamp) {
		     if (!timestamp) return '';
		           const date = new Date(timestamp * 1000);
		           const year = date.getFullYear();
		           const month = String(date.getMonth() + 1).padStart(2, '0');
		           const day = String(date.getDate()).padStart(2, '0');
		           const hours = String(date.getHours()).padStart(2, '0');
		           const minutes = String(date.getMinutes()).padStart(2, '0');
		           const seconds = String(date.getSeconds()).padStart(2, '0');
		           return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		         }
		  },
		
		onShow() {
			this.page = 1
			this.list = []
			this.getList(this.selected);
		},
		onReachBottom() {
			if (this.list.length < this.count) {
				this.page++;
				this.getList(this.selected);
			}
		},
		methods: {
			
			// 关闭弹窗
			close() {
				this.qrcode = {}
				this.show = false
			},
			open() {
				this.show = true
			},
			switchSort(index, value) {
				console.log(index, value);
			},
			
		
			//取消预约
			Cancel(id,status) {
				uni.navigateTo({
					url:"/packageA/my/Judge?id=" + id + '&status=' + status
				})
			},
			// 搜索
			search(){
				const that = this
				that.page = 1
				that.list = []
				this.getList(this.selected)
			},
			// 获取课程列表
			getList(status) {
				// if (this.loadStatus === 'nomore') return;
				uni.$u.http.get('/api/school/order/order_list', {
					params: {
						keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status:'3,9',
						has_evaluate: status,
					}
				}).then(res => {
					if (res.code == 1) {
						this.count = res.data.count
						this.list = [...this.list, ...res.data.list];
						if (this.list.length >= res.data.count) {
							this.loadStatus = 'nomore';
						} else {
							this.loadStatus = 'loading';
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.loadStatus = 'loading';
					}
				}).catch(error => {
					console.error('请求失败', error);
					this.loadStatus = 'loading';
				});
			},
			selectheader(status) {
				const that = this
				that.selected = status;
				console.log(status, '')
				that.page = 1
				that.list = []
				that.getList(status)
			},
			headerSelected(status) {
				return this.selected === status;
			},
			// 跳转详情
			// toDetail(id, orderId) {
			// 	uni.navigateTo({
			// 		url: "/packageA/center/detail?id=" + id + "&orderId=" + orderId
			// 	})
			// }
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: linear-gradient(to bottom, #F1F2F8 0%, #FFFFFF 5%, #FFFFFF 100%);

		.con-center {
			width: 690rpx;
			margin-top: 24rpx;

			.centerBack {
				position: fixed;
				width: 100%;
				height: 100%;
				top: 25rpx;
				left: 0;
				z-index: -1;
			}

			.header {
				height: 50rpx;
				margin-top: 37rpx;

				.s-header {
					width: 104rpx;
					height: 50rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #9E9E9E;
					line-height: 26rpx;
				}

				.s-header.selected {
					width: 104rpx;
					height: 50rpx;
					background: #008CFF;
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 28rpx;
					color: #FFFFFF;
					line-height: 26rpx;
				}
			}
		}

	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		margin-top: 12rpx;
		width: 690rpx;
		height: 1rpx;
		background: #008CFF;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.centerBox {
		width: 690rpx;

		.error {
			width: 100%;
			word-break: break-all;
		}

		.box-line {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			margin-top: 24rpx;
		}

		.center {
			margin: 32rpx 0 32rpx 0;
		}

		.rightBox {
			margin-left: 24rpx;
			width: 378rpx;

			.line-row {
				width: 382rpx;
				height: 1rpx;
				background: #D9D9D9;
				box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
				margin: 14rpx 0 6rpx 0;
			}

			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 36rpx;
				color: #343434;
				margin-bottom: 24rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				/* 设置行数 */
				overflow: hidden;
				text-overflow: ellipsis;
				word-break: break-all;
				/* 防止单词被截断 */
			}

			.minge {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #7A7A7A;
				margin-bottom: 14rpx;
			}

			.pass {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #34DC12;
			}

			.full {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #FF2323;
			}


			.money {
				font-weight: 800;
				font-size: 24rpx;
				color: #FF2323;

				span {
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
				}
			}

			.Cancel {
				width: 138rpx;
				height: 48rpx;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				background: #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
			}

			.make {
				width: 138rpx;
				height: 48rpx;
				background: #008CFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-left: 16rpx;
			}

			.QR {
				width: 138rpx;
				height: 48rpx;
				background: #4974FF;
				box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #EAEAEA;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-top: 50rpx;
			}

		}
	}

	.charge {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 28rpx;
		color: #FF2323;
		line-height: 32rpx;
	}

	.search {
		width: 690rpx;
		height: 64rpx;
		background: #FFFFFF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008CFF;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008CFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

		}
		.searchBtn{
			width: 128rpx;
			height: 64rpx;
			background: #008CFF;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	.popup {
		.header {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 42rpx;
			color: #008CFF;
			margin-top: 34rpx;
			width: 690rpx;
		}

		.line-row {
			width: 690rpx;
			height: 1rpx;
			background: #D9D9D9;
			margin: 11rpx 0 31rpx 0;
		}

		.pop-center {
			.left {}

			.right {
				margin-left: 30rpx;

				.title {
					width: 340rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
				}


			}
		}

		.line-short {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
		}

		.popList {
			justify-content: space-between;
			width: 600rpx;
			margin-top: 32rpx;

			.hei {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #343434;
			}
		}

		.pop-btn {
			width: 690rpx;
			margin-top: 62rpx;
			justify-content: space-around;

			.Cancel {
				width: 306rpx;
				height: 80rpx;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				border: 2rpx solid #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #008CFF;
			}

			.Confirm {
				width: 306rpx;
				height: 80rpx;
				background: #008CFF;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #FFFFFF;
			}
		}
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}
</style>