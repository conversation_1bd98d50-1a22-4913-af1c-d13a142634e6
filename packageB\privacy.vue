<template>
	<view style="padding: 30rpx;">
		<view v-if="type=='privacy'" v-html="info.privacy"></view>
		<view v-if="type=='user_protocol'" v-html="info.user_protocol"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'',
				info:''
			}
		},
		onLoad(options) {
			this.type=options.type;
			this.getInfo();
			//uniapp 动态修改标题
			if(this.type=='privacy'){
				uni.setNavigationBarTitle({
					title: '隐私政策'
				})
			}else{
				uni.setNavigationBarTitle({
					title: '用户协议'
				})
			}
		},
		methods: {
			getInfo(){
				uni.$u.http.get('/api/index/agreement').then(res=>{
					console.log(res)
					if(res.code==1){
						this.info=res.data;
					}
				})
			}
		}
	}
</script>

<style>

</style>
