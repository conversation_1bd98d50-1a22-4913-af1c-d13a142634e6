<template>
	<view class="page">
		<view style="background-color: #FFFFFF;border-radius: 0rpx 0rpx 44rpx 44rpx;padding: 30rpx 30rpx 40rpx 30rpx;">
			<view class="section_1">
				<view style="color: #FFFFFF;font-size: 26rpx;padding: 30rpx 30rpx 0rpx 30rpx;">
				<text style="vertical-align: middle;">当前余额</text>
				<image @click="tipsShowOne = true" src="/static/tips.png" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;vertical-align: middle;"></image>
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 30rpx;">
					<view style="color: #FFFFFF;font-size: 60rpx;font-weight: 900;">
						{{settleInfo.settled_amount?settleInfo.settled_amount:0}}
					</view>
					<view @click="openUrl('/packageB/wallet/bank')">
						<view
							style="text-align: center;width: 169rpx;height: 70rpx;background: #BBFC5B;line-height: 70rpx;color: #3D3D3D;font-size: 32rpx;border-radius: 70rpx;">
							提现</view>
					</view>
				</view>
				<view style="display: flex;align-items: center;color: #FFFFFF;font-weight: 400;font-size: 26rpx;padding-left: 30rpx;">
					<text>预计待入账</text>
					<text
						style="padding-left: 20rpx;">￥{{settleInfo.expected_incoming_amount?settleInfo.expected_incoming_amount:0}}</text>
					<image @click="tipsShow = true" src="/static/tips.png" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
					<text style="padding: 0rpx 20rpx;">|</text>
					<text>累计收益</text>
					<text
						style="padding-left: 20rpx;">￥{{settleInfo.accumulated_incoming_amount?settleInfo.accumulated_incoming_amount:0}}</text>
						<image @click="tipsShowTwo = true" src="/static/tips.png" style="width: 25rpx;height: 25rpx;margin-left: 10rpx;"></image>
				</view>
			</view>
		</view>
		<view style="background-color: #FFFFFF;border-radius:44rpx;padding:45rpx 30rpx;margin-top: 30rpx;min-height: 70vh;">
			<view style="display: flex;gap: 50rpx;padding-bottom: 40rpx;align-items: baseline;">
				<view @click="openTab(0)">
					<view :class="tabIndex==0?'act':'no_act'">收益明细</view>
					<view v-if="tabIndex==0"
						style="width: 60rpx;height: 10rpx;background-color: #BBFC5B;margin: 0 auto;margin-top: 20rpx;">
					</view>
				</view>
				<view @click="openTab(1)">
					<view :class="tabIndex==1?'act':'no_act'">提现明细</view>
					<view v-if="tabIndex==1"
						style="width: 60rpx;height: 10rpx;background-color: #BBFC5B;margin: 0 auto;margin-top: 20rpx;">
					</view>
				</view>
			</view>
			<view v-if="tabIndex==0" v-for="item in list" @click="qbIn(item)">
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view>
						<view style="font-size: 30rpx;color: #3D3D3D;font-weight: 600;">{{item.detail.title}}</view>
						<view style="font-size: 26rpx;color: #9C9C9C;font-weight: 400;margin: 30rpx 0rpx;">
							<text v-if="item.status==3">{{formatTimestamp(item.settletime)}}</text>
							<text v-if="item.status==2">{{formatTimestamp(item.createtime)}}</text>
						</view>
						<view style="font-size: 25rpx;color: #FF4810;font-weight: 400;">[
							活动订单总金额x{{item.fee_scale*100}}%（平台费率）=待入账金额 ]
						</view>
					</view>
					<view style="text-align: center;">
						<view>+{{item.settle_price}}</view>
						<view v-if="item.status==2"
							style="background: #E6EEFF;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;margin-top: 20rpx;">
							<view style="color: #2F78CD;font-size: 22rpx;line-height: 32rpx;">待入账</view>
						</view>
						<view v-if="item.status==3" 
							style="background: #EAF8EB;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;margin-top: 20rpx;">
							<view style="color: #2BA635;font-size: 22rpx;line-height: 32rpx;">已入账</view>
						</view>
					</view>
				</view>
				<view style="background: #F0F0F0;width: 100%;height: 1px;margin: 40rpx 0rpx;"></view>
			</view>
			<view v-if="tabIndex==1" v-for="item in list" @click="inTx(item)">
				<view style="display: flex;justify-content: space-between;">
					<view>
						<view style="font-size: 30rpx;color: #3D3D3D;font-weight: 600;">
							{{item.bank_name}}（{{cardSlice(item.bank_user_name)}}）</view>
						<view style="font-size: 26rpx;color: #9C9C9C;font-weight: 400;margin: 30rpx 0rpx;">
							<text v-if="item.status==1">{{formatTimestamp(item.createtime)}}</text>
							<text
								v-if="item.status==3 || item.withdrawal_status==2">{{formatTimestamp(item.examinetime)}}</text>
							<text v-if="item.withdrawal_status==3">{{formatTimestamp(item.paytime)}}</text>
						</view>
					</view>
					<view style="text-align: center;">
						<view style="margin-bottom: 20rpx;" v-if="item.withdrawal_status!=3">+{{item.real_price}}</view>
						<view style="margin-bottom: 20rpx;" v-if="item.withdrawal_status==3">+{{item.real_have_price}}</view>
						
						<view v-if="item.status==1 || item.withdrawal_status==2"
							style="background: #E6EEFF;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;">
							<view style="color: #2F78CD;font-size: 22rpx;line-height: 32rpx;">审核中</view>
						</view>
						<view v-if="item.status==3"
							style="background: #F8EAEA;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;">
							<view style="color: #A62B2B;font-size: 22rpx;line-height: 32rpx;">失败</view>
						</view>
						<view v-if="item.withdrawal_status==3" 
							style="background: #EAF8EB;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;">
							<view style="color: #2BA635;font-size: 22rpx;line-height: 32rpx;">已打款</view>
						</view>
						<!-- <view v-if="item.withdrawal_status==2"
							style="background: #FFF3ED;width: 90rpx;height: 32rpx;border-radius: 4rpx 4rpx 4rpx 4rpx;text-align: center;margin: 0 auto;">
							<view style="color: #FF783A;font-size: 22rpx;line-height: 32rpx;">打款中</view>
						</view> -->
					</view>
				</view>
				<view v-if="item.status==3"
					style="font-weight: 400;color: #888888;font-size: 24rpx;background: #f8f8f8;border-radius: 6rpx;padding: 20rpx 20rpx;">
					{{ item.reason }}
				</view>
				<view v-if="item.withdrawal_status==3 "
					style="font-weight: 400;color: #888888;font-size: 24rpx;background: #f8f8f8;border-radius: 6rpx;padding: 20rpx 20rpx;">
					{{ item.remark }}
				</view>
				<view style="background: #eeeeee;width: 100%;height: 1px;margin: 40rpx 0rpx;"></view>
			</view>
			<view v-if="list.length==0" style="position: absolute;text-align: center;margin: 0 auto;left: 0;right: 0;top: 50%;">
				<view>
					<image src="/static/detail/no_info.png" style="width: 180rpx;height: 180rpx"></image>
					<view style="font-size: 28rpx;">没有更多了</view>
				</view>
			</view>
			<u-loadmore v-if="list.length!=0" style="margin-bottom: 60rpx;" :status="loadStatus" />
		</view>
		<u-popup @touchmove.native.stop.prevent :custom-style="popupStyle" :closeable="false" :show="tipsShow" :round="10" mode="center"
			  :closeOnClickOverlay="false" :safeAreaInsetBottom="false">
			<view style="font-size: 40rpx;font-weight: 600;">预计待入账金额说明</view>
			<view style="font-size: 26rpx;color: #3D3D3D;padding: 40rpx 0rpx;line-height: 20px;">
				<view>①这是您已结束活动产生的、正在结算中的款项金额。</view>
				<view style="margin-top: 20rpx;">②为处理潜在的用户退款，所有款项会经过一个<text style="font-weight: 600;">为期7天</text>的“结算与售后处理期”。在此期间发生的退款，将直接从这笔金额中扣除。</view>
				<view style="margin-top: 20rpx;">③7天处理期结束后，剩余金额将自动转入您的“当前余额”，变为可提现资金。</view>
				<view style="margin-top: 20rpx;">④您可在【我发布的-已售后】查询退款详情。</view>
				<view style="margin-top: 20rpx;text-decoration: underline;">⑤最终转入“当前余额”的金额=活动应结算总额−7日内发生的退款总额。</view>
			</view>
			<view class="popup-footer">
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="tipsShow = false">取消</view>
					<view class="btn_2" @click="tipsShow = false">我同意</view>
				</view>
			</view>
		</u-popup>
		<u-popup @touchmove.native.stop.prevent :custom-style="{
					width: '600rpx',
					padding: '50rpx 40rpx 42rpx 40rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeable="false" :show="tipsShowOne" :round="10" mode="center"
			  :closeOnClickOverlay="false" :safeAreaInsetBottom="false">
			<view style="font-size: 40rpx;font-weight: 600;">当前余额</view>
			<view style="font-size: 26rpx;color: #3D3D3D;padding-top: 40rpx;line-height: 40rpx;text-align: center;">
				<text>前余额指已经完成所有结算流程、您可以随时发起提现的金额。</text>
			</view>
			<view class="popup-footer">
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="tipsShowOne = false">取消</view>
					<view class="btn_2" @click="tipsShowOne = false">我同意</view>
				</view>
			</view>
		</u-popup>
		<u-popup @touchmove.native.stop.prevent :custom-style="{
					width: '600rpx',
					padding: '50rpx 40rpx 42rpx 40rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeable="false" :show="tipsShowTwo" :round="10" mode="center"
			  :closeOnClickOverlay="false" :safeAreaInsetBottom="false">
			<view style="font-size: 40rpx;font-weight: 600;">累计收益</view>
			<view style="font-size: 26rpx;color: #3D3D3D;padding-top: 40rpx;line-height: 40rpx;text-align: center;">
				<text>所有已完成结算的活动为您带来的结算款项总和。累计收益=提现金额总和+当前余额。</text>
			</view>
			<view class="popup-footer">
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="tipsShowTwo = false">取消</view>
					<view class="btn_2" @click="tipsShowTwo = false">我同意</view>
				</view>
			</view>
		</u-popup>
		<u-popup @touchmove.native.stop.prevent :custom-style="{
					width: '620rpx',
					padding: '50rpx 40rpx 42rpx 40rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeable="false" :show="txShow" :round="10" mode="center"
			  :closeOnClickOverlay="false" :safeAreaInsetBottom="false">
			<view style="font-size: 40rpx;font-weight: 600;">提现到账说明</view>
			<view style="font-size: 26rpx;color: #3D3D3D;padding: 40rpx 20rpx;width: 100%;color: #3D3D3D;">
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view style="font-size: 26rpx;">提现金额</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ txInfo.real_price }}</view>
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;margin-top: 30rpx;">
					<view style="font-size: 26rpx;">跨行/跨银行手续费</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ txInfo.bank_fee}}</view>
				</view>
				<view style="font-size: 26rpx;margin-top: 30rpx;">
					备注：
				</view>
				<view 
					style="line-height: 40rpx;margin-top: 30rpx;font-weight: 400;color: #323232;font-size: 26rpx;background: #f8f8f8;border-radius: 6rpx;padding: 30rpx 20rpx;">
					{{txInfo.remark}}
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;margin-top: 30rpx;">
					<view style="font-size: 26rpx;">实际到账金额</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ txInfo.real_have_price}}</view>
				</view>
			</view>
			<view class="popup-footer">
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="txShow = false" style="margin-top: 0px;">关闭</view>
					<view class="btn_2" @click="txShow = false" style="margin-top: 0px;">我已知晓</view>
				</view>
			</view>
		</u-popup>
		<u-popup @touchmove.native.stop.prevent :custom-style="{
					width: '620rpx',
					padding: '40rpx 50rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeable="false" :show="qbShow" :round="10" mode="center"
			  :closeOnClickOverlay="false" :safeAreaInsetBottom="false">
			<view style="font-size: 40rpx;font-weight: 600;">提现到账说明</view>
			<view style="font-size: 26rpx;color: #3D3D3D;padding: 40rpx 20rpx;width: 100%;color: #3D3D3D;">
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view style="font-size: 26rpx;">活动收入金额</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ qbInfo.order_price }}</view>
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;margin-top: 30rpx;">
					<view style="font-size: 26rpx;">平台手续费（{{ qbInfo.fee_scale*100 }}%)</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ qbInfo.fee_price}}</view>
				</view>
				<view style="font-size: 26rpx;margin-top: 30rpx;">
					计算方式：
				</view>
				<view 
					style="line-height: 40rpx;margin-top: 30rpx;font-weight: 400;color: #FF4810;font-size: 26rpx;background: #f8f8f8;border-radius: 6rpx;padding: 30rpx">
					[ 活动订单总金额x{{ qbInfo.fee_scale*100 }}%（平台费率）=待入账金额 ]
				</view>
				<view style="display: flex;justify-content: space-between;align-items: center;margin-top: 30rpx;">
					<view style="font-size: 26rpx;">实际入账金额</view>
					<view style="font-size: 26rpx;font-weight: 600;">￥{{ qbInfo.settle_price}}</view>
				</view>
			</view>
			<view class="popup-footer">
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="qbShow = false" style="margin-top: 0px;">关闭</view>
					<view class="btn_2" @click="qbShow = false" style="margin-top: 0px;">我已知晓</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				txInfo:{},
				txShow:false,

				qbShow:false,
				qbInfo:{},

				tipsShowTwo:false,
				tipsShowOne:false,
				tabIndex: 0,
				page: 1,
				list: [],
				settleInfo: {},
				loadStatus: 'loading',
				tipsShow:false,
				popupStyle: {
					width: '620rpx',
					padding: '50rpx 40rpx 42rpx 40rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
			}
		},
		onShow() {
			this.token = uni.getStorageSync('token');
			if (this.token) {
				this.getUserInfo();
			}
			this.openTab(0);
		},
		onReachBottom() {
			this.page += 1;
			if (this.tabIndex == 0) {
				this.getSyList();
			} else {
				this.getTxList();
			}
		},
		methods: {
			inTx(item){
				if(item.withdrawal_status==3){
					this.txShow = true;
					this.txInfo = item;
				}
			},
			qbIn(item){
				if(item.status==3){
					this.qbShow = true;
					this.qbInfo = item;
				}
			},
			openTab(index) {
				this.loadStatus = 'loading';
				this.page = 1;
				this.tabIndex = index;
				this.list = [];
				if (index == 0) {
					this.getSyList();
				} else {
					this.getTxList();
				}
			},
			getSyList() {
				uni.$u.http
					.get("/api/school.newactivity.settle_log/settle_log", {
						params: {
							page: this.page,
							limit: 10,
							status: '2,3'
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.list.push(...res.data.list);
							if (res.data.list.length < 10) {
								this.loadStatus = 'nomore';
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
			},
			getTxList() {
				uni.$u.http
					.get("/api/school.newactivity.settle_log/withdrawal_log", {
						params: {
							page: this.page,
							limit: 10,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.list.push(...res.data.list);
							if (res.data.list.length < 10) {
								this.loadStatus = 'nomore';
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
			},
			// 获取个人信息
			getUserInfo() {
				uni.$u.http.get('/api/user/index', {}).then(res => {
					if (res.code == 1) {
						uni.setStorageSync('userInfo', res.data.user_info)
						uni.setStorageSync('settleInfo', res.data.settle_info)
						uni.setStorageSync('activityInfo', res.data.activity_info)
						this.settleInfo = res.data.settle_info;
					} else {
						uni.showToast({
							title: '登陆失败',
							icon: 'error',
							duration: 2000
						})
					}

				}).catch(error => {
					console.log('error', error);
					this.showPopup = false
					uni.showToast({
						title: '登陆失败',
						icon: 'error',
						duration: 2000
					})
				})
			},
			formatTimestamp(timestamp) {
				const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, "0");
				const day = String(date.getDate()).padStart(2, "0");
				const hours = String(date.getHours()).padStart(2, "0");
				const minutes = String(date.getMinutes()).padStart(2, "0");
				const seconds = String(date.getSeconds()).padStart(2, "0");
				return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
			},
			cardSlice(str) {
				return str.slice(-4); // 从倒数第4位开始截取到末尾 
			},
			openUrl(url) {
				uni.navigateTo({
					url: url
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		font-family: PingFang SC, PingFang SC;
		background: #f7f7f7;
		min-height: 100vh;
	}

	.section_1 {
		width: 100%;
		height: 264rpx;
		background: url(@/static/wallet.png);
		background-size: 100% 100%;
	}

	.no_act {
		font-size: 30rpx;
		font-weight: 600;
		color: #9C9C9C;
	}

	.act {
		font-size: 34rpx;
		color: #323232;
		font-weight: 600;
	}
	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		// margin: 30rpx 0;
		width: 100%;
	
		.zhixiao {
			height: 100rpx;
			background: #E8E8E8;
			//border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #9C9C9C;
			line-height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 100%;
			bottom: 0;
			border-radius: 0rpx 0rpx 20rpx 20rpx;
		}
	
		.shows_zhidao {
			background-color: #323232;
			color: #BBFC5B;
			font-weight: 400;
			font-size: 36rpx;
		}
	}
	
	.btn_2 {
		width: 50%;
		height: 80rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}
	
	.btn_4 {
		width: 50%;
		height: 80rpx;
		background: #ffffff;
		border: 1px solid #999999;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
		line-height: 80rpx;
		text-align: center;
		margin-top: 40rpx;
		z-index: 100;
	}
</style>