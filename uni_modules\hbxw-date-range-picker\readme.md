# hbxw-date-range-picker组件

## 介绍
一个可以自由定义选择级别的日期时间段选择器插件如: 2024-07-11 15:00:00 - 2030-07-12 16:18:56/2024-07-11 - 2030-07-12/2024-07-11 15:00 - 2030-07-12 16:18等

## 使用示例

使用前要以新建一个页面拷贝下面代码做一下测试，熟悉后再使用到项目中
```html

<template>
	<view class="content">
		<button @click="selctDateRange">选择日期时间段</button>
		<button @click="selctDateRange0">选择范围:年月日</button>
		<button @click="selctDateRange1">选择范围:年月日时分</button>
		<view class="text-area" v-if="date">
			<text class="date">values: {{JSON.stringify(date.values)}}</text>
			<text class="date">start: {{JSON.stringify(date.start)}}</text>
			<text class="date">end: {{JSON.stringify(date.end)}}</text>
			<text class="date">startStr: {{date.startStr}}</text>
			<text class="date">endStr: {{date.endStr}}</text>
			<text class="date">from: {{date.from}}</text>
		</view>
		<hbxw-date-range-picker
			title="选择时间"
			:visible="dateReangeVisible"
			@cancel="cancel"
			@sure="sure"
			@change="change"
		 />
		<hbxw-date-range-picker
			title="选择时间"
			:visible="dateReangeVisible0"
      level="date"
			@cancel="cancel0"
			@sure="sure0"
			@change="change0"
		 />
     <hbxw-date-range-picker
     	title="选择时间"
     	:visible="dateReangeVisible1"
      level="minute"
     	@cancel="cancel1"
     	@sure="sure1"
     	@change="change1"
      />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dateReangeVisible: false,
				dateReangeVisible0: false,
				dateReangeVisible1: false,
				date: null
			}
		},
		onLoad() {

		},
		methods: {
			selctDateRange() {
				this.dateReangeVisible = true;
			},
			cancel() {
				console.log('---- cancel ----');
        this.dateReangeVisible = false;
			},
			sure(res) {
				console.log('---- sure ----:', res);
				this.date = res;
			},
			change(res) {
				console.log('---- change ----:', res);
				this.date = res;
			},
      
      selctDateRange0() {
      	this.dateReangeVisible0 = true;
      },
      cancel0() {
      	console.log('---- cancel ----');
        this.dateReangeVisible0 = false;
      },
      sure0(res) {
      	console.log('---- sure ----:', res);
      	this.date = res;
      },
      change0(res) {
      	console.log('---- change ----:', res);
      	this.date = res;
      },
      
      selctDateRange1() {
      	this.dateReangeVisible1 = true;
      },
      cancel1() {
      	console.log('---- cancel ----');
        this.dateReangeVisible1 = false;
      },
      sure1(res) {
      	console.log('---- sure ----:', res);
      	this.date = res;
      },
      change1(res) {
      	console.log('---- change ----:', res);
      	this.date = res;
      },
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}


	.text-area {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	.date{
		padding:20rpx 0;
		border: 1px solid rgba(0,0,0,.2);
		font-size: 28rpx;
	}
</style>



```

## API

### Props

| 属性名       | 类型          | 默认值    | 必填 | 说明                               |
| ------------ | -------------| -------- | ---- | -----------------------------------|
| visible      | Boolean      | false    | 是   | 时间段选择器的显示与隐藏              |
| title        | String       | 选择时间  | 否   | 时间段选择器标题，如果不需要传空即可   |
| zIndex       | Number       | 9999     | 否   | 时间选择器的层级                     |
| yearSpan     | Number       | 10       | 否   | 时间选择器年份可以选到前后多少年前        |
| level        | String       | time     | 否   | 时间选择器选择范围定义,date(年月日)/time(年月日时分秒)/year(选择年份)/month(年月)/day(年月日同date)/hour(年月日时)/minute(年月日时分)/second(年月日时分秒同time)        |

### Events

| 事件名       | 说明   | 返回值                                                                                                                                                               |
| ------------ | ------------  |  ------------ 
| cancel       | 点击取消按钮的触发事件 |  undefined                                                                                                                                                |
| sure         | 点击确认按钮的触发事件 |    举列：{values: [[10,6,11,9,52,19],[0,0,1,1,0,0]],start: ["2024","07","12","09","52","19"],end: ["2024","07","13","01","00","00"],startStr: 2024年07月12日 09:52:19,endStr: 2024年07月13日 01:00:00,from: sure}  |
| change       | picker状态发生变化触发事件 |  举列：{values: [[10,6,11,9,52,19],[0,0,1,1,0,0]],start: ["2024","07","12","09","52","19"],end: ["2024","07","13","01","00","00"],startStr: 2024年07月12日 09:52:19,endStr: 2024年07月13日 01:00:00,from: start/end} |

### 注意

显示的插件包体积有768kb,是包括了演示图片在里面的，真正使用的时候包体积是23KB左右