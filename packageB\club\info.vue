<template>
	<view class="box flex flex-column align-items">
		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'"
				:titleStyle="{ color: '#000000', fontSize: '34rpx', fontWeight: 'bold' }">
				<view slot="left">
					<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
				</view>
			</u-navbar>
		</view>
		<view>
			<!--轮播-->
			<view class="swiper-container">
				<swiper
					class="swiper"
					:circular="true"
					:autoplay="true"
					:interval="3000"
					:duration="500"
					:indicator-dots="false">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<view class="swiper-item">
							<image
								:src="item.image"
								class="swiper-image"
								mode="aspectFill" />
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 轮播图数据
			swiperList: [
				{
					id: 1,
					image: 'https://dummyimage.com/700x400/000/fff',
					title: '轮播图1'
				},
				{
					id: 2,
					image: 'https://dummyimage.com/700x400/000/fff',
					title: '轮播图2'
				},
				{
					id: 3,
					image: 'https://dummyimage.com/700x400/000/fff',
					title: '轮播图3'
				}
			]
		}
	},
	methods: {

	}
}
</script>

<style lang="scss" scoped>
.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}

.swiper {
	width: 100%;
	height: 400rpx;
}

.swiper-item {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.swiper-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}
</style>
