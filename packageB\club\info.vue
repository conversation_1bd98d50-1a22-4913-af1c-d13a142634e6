<template>
	<view class="box">
		<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" :bgColor="'transparent'">
			<view slot="left">
				<img src="/static/wback.png" style="width: 50rpx;height: 50rpx;"></img>
			</view>
		</u-navbar>
		<view style="position: relative;">
			<!--轮播-->
			<view class="swiper-container">
				<swiper :circular="true" :autoplay="true" :interval="3000" :duration="500" :indicator-dots="false"
					style="width: 100%; height: 450rpx;">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<image :src="item.image" style="width: 100%; height: 100%; border-radius: 12rpx;"
							mode="scaleToFill"></image>
					</swiper-item>
				</swiper>
			</view>
			<view
				style="position: absolute;bottom: 140rpx;width: 100%;height: 100px;background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 80%);">
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 轮播图数据
			swiperList: [
				{
					id: 1,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图1'
				},
				{
					id: 2,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图2'
				},
				{
					id: 3,
					image: 'https://dummyimage.com/800x500/000/fff',
					title: '轮播图3'
				}
			]
		}
	},
	methods: {

	}
}
</script>

<style scoped>
page {}

.box {
	background-color: #10083A;
	height: 100vh;
	background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/club_bg.png");
	background-size: 100%;
	background-repeat: no-repeat;
}

.swiper-container {
	width: 100%;
	margin-bottom: 20rpx;
}
</style>
