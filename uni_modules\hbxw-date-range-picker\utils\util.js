// 创建年份数据
export const createYearList = (baseYear, span, startYear) => {
  const years = [];
  let nowYearIndex = -1;
  // 生成年份选项数据，往前span年
  for (let year = startYear ? startYear : baseYear - span; year <= baseYear + span; year++) {
  	if (baseYear >= year) {
  		nowYearIndex += 1;
  	}
  	years.push(year);
  }
  return {
    list: years,
    index: nowYearIndex
  }
}

// 创建月份数据
export const createMonthList = (nowMonth, baseMonth = 1) => {
  const months = [];
  let nowMonthIndex = -1;
  for (let month = baseMonth; month <= 12; month ++) {
  	if (nowMonth >= month) {
  		nowMonthIndex += 1;
  	}
  	months.push(String(month).padStart(2, '0'));
  }
  return {
    list: months,
    index: nowMonthIndex
  }
}

// 创建天数数据
export const createDayList = (year, month, nowDay, baseDay = 1) => {
  const days = [];
  let nowDayIndex = -1;
  // 可以选择的天，会根据月份有变化
  const maxDay = new Date(+year, +month, 0).getDate();
  for (let day = +baseDay; day <= maxDay; day ++) {
  	if (nowDay >= day) {
  		nowDayIndex += 1;
  	}
  	days.push(String(day).padStart(2, '0'));
  }
  return {
    list: days,
    index: nowDayIndex
  }
}

// 创建小时数数据
export const createHourList = (nowHour, baseHour = 0) => {
  const hours = [];
  let nowHourIndex = -1;
  for (let hour = +baseHour; hour <= 23; hour ++) {
    if (+nowHour >= hour) {
    	nowHourIndex += 1;
    }
  	hours.push(String(hour).padStart(2, '0'));
  }
  return {
    list: hours,
    index: nowHourIndex
  }
}

// 创建分钟数数据
export const createMinuteList = (nowMinute, baseMinute = 0) => {
  const minutes = [];
  let nowMinuteIndex = -1;
  for (let minute = +baseMinute; minute <= 59; minute ++) {
    if (+nowMinute >= minute) {
    	nowMinuteIndex += 1;
    }
  	minutes.push(String(minute).padStart(2, '0'));
  }
  return {
    list: minutes,
    index: nowMinuteIndex
  }
}

// 创建秒数数据
export const createSecondList = (nowSecond, baseMinute = 0) => {
  const seconds = [];
  let nowSecondIndex = -1;
  for (let second = +baseMinute; second <= 59; second ++) {
    if (+nowSecond >= second) {
    	nowSecondIndex += 1;
    }
  	seconds.push(String(second).padStart(2, '0'));
  }
  return {
    list: seconds,
    index: nowSecondIndex
  }
}

// 生成日期字符串
export const createDateStr = (year, month, day, hour, minute, second) => {
  let str = '';
  if (year) {
    str += `${year}年`
  }
  if (month) {
    str += `${month}月`
  }
  if (day) {
    str += `${day}日`
  }
  if (hour) {
    str += minute ? ` ${hour}:` : `${hour}`
  }
  if (minute) {
    str += second ? `${minute}:` : `${minute}`
  }
  if (second) {
    str += `${second}`
  }
  return str;
}