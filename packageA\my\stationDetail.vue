<template>
	<view class="box">
		<view style="height: 1px;background-color: #eeeeee;width: 100%;"></view>
		<view class=" flex">
			<view class="center">
				<span class="title">{{detail.title}}</span>
				<view style="margin-top: 15rpx;">
				 	<rich-text :nodes="detail.desc" selectable user-select style="font-weight: 400;"></rich-text>
					<view style="margin:16rpx 0;color: #9c9c9c;font-size: 26rpx;font-weight: 400;">{{detail.createtime_text}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: '',
				detail:{}
			};
		},
		onLoad(option) {
			this.id = option.id
			this.getDetail(option.id)
		},
		methods: {
			getDetail(id) {
				uni.$u.http.get('/api/school/message/detail', {
					params: {
						id: id
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: #ffffff;
		width: 750rpx;
		height: 100vh;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.center {
		    border-radius: 20rpx 20rpx 20rpx 20rpx;
			padding:50rpx 30rpx 30rpx 30rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #323232;
			

		.title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 36rpx;
			color: #343434;
		}
	}
</style>