module.exports = (vm) => {
    uni.$u.http.setConfig((config) => {
        // config.baseURL = 'https://2test.hschool.com.cn/'; //测试
		// config.baseURL = 'https://testy.hschool.com.cn/';
		config.baseURL = 'https://naweigetetest2.hschool.com.cn/'; //测试
        return config;
    });

    uni.$u.http.interceptors.request.use(
       (config) => {
            const token = uni.getStorageSync('token');
            if (token) {
                config.header.token = token;
            }
            config.data = config.data || {};
            return config;
        },
        (error) => {
            return Promise.reject(error);
        }
    );

    uni.$u.http.interceptors.response.use(
        async (response) => {
            const data = response.data;
            if (data && data?.errcode === 30002) {
                console.log('Errcode 30002 detected, switching tab...');
                try {
                    await uni.switchTab({
                        url: '/pages/my/index',
                        success: function() {
                            console.log('Switch tab success');
                        },
                        fail: function(err) {
                            console.error('Switch tab failed', err);
                        }
                    });
                } catch (err) {
                    console.error('Error during switchTab:', err);
                }
                return Promise.reject(data);
            }
            return data === undefined ? {} : data;
        },
        (error) => {
			if (error.data.code == 401) {
				const data = error.data;
				if (data && data?.data?.errcode == 30002) {
				    console.log('Errcode 30002 detected, switching tab...');
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function () {
						    setTimeout(function () {
						        uni.switchTab({
						            url: '/pages/my/index',
						        });
						    }, 2000);
						}
					});
				    return Promise.reject(data);
				}
			}
            return Promise.reject(error);
        }
    );
};