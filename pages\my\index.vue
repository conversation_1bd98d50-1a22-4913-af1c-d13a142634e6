<template>
	<view class="myBox">
		<view class="box flex justify-start align-items flex-column">
			<scroll-view @touchmove.prevent scroll-y="true" :show-scrollbar="true"
				class="con-center flex flex-start flex-column">
				<view class="header flex align-items">
					<view class="hea-left flex align-items">
						<image :src="avatar" mode="" class="avatar" @click="clickTop"></image>
						<view class="first-header flex justify-center align-items">
							<view class="flex flex-column" @click="clickTop">
								<view
									style="display: flex;align-items: center;;margin-bottom: 20rpx;font-weight: 800;font-size: 34rpx;color: #181818;">
									<view>{{ userName }}</view>
									<view style="padding-left: 20rpx; height: 35rpx;">
										<image v-if="shop_auth_info.auth_status == 1" src="/static/my/owenr.png" mode=""
											style="width: 35rpx; height: 35rpx;">
										</image>
									</view>
								</view>
								<view style="color: #909090; font-size: 24rpx;">{{ bio || '带着热爱，在搭子圈里撒野~'}}</view>
							</view>
							<image src="../../static/shezhi.png" mode=""
								style="width: 50rpx; height: 50rpx;position: absolute;right: 0;" @click="clickTop">
							</image>
						</view>
					</view>
					<!-- <view class="hea-right flex flex-column align-items" @click="out()" v-if="token">
						<image src="../../static/my/Leave.png" mode="" style="width: 64rpx; height: 64rpx;"></image>
						<span>退出登录</span>
					</view> -->
				</view>

				<view class="top_box flex">
					<view class="flex align-items justify-start" @click="toMycy">
						<image src="/static/my/wcyd.png" mode=""></image>
						<span class="flex flex-column align-items">
							<span class="p1">我参与的</span>
							<span class="p2">{{ actives }}个活动</span>
						</span>
					</view>
					<view class="top_box_right flex align-items justify-start" @click="toMyfb">
						<image src="/static/my/wdfb.png" mode=""></image>
						<span class="flex flex-column align-items justify-start">
							<span class="p1">我发布的</span>
							<span class="p2">{{ pushActives }}个活动</span>
						</span>
					</view>
				</view>





				<!-- 提现 -->
				<view class="first mymoney flex flex-column" @click="to('/packageB/wallet/index')">
					<view class="flex">
						<view class="flex flex-column myyue">
							<span style="font-size: 26rpx;">当前余额</span>
							<view class="flex align-items" style="margin-top: 20rpx;">
								<span
									style="font-size: 50rpx;margin-right: 20rpx;font-weight: 600;">{{settleInfo.settled_amount?settleInfo.settled_amount:0}}</span>
								<image src="/static/my/qjt.png" mode="" style="width: 28rpx; height: 28rpx;"></image>
							</view>
						</view>
						<view>
							<image class="tixian" src="/static/my/tixian.png" mode=""></image>
							<span class="tixianwz" style="font-weight: 600;">提现</span>
						</view>
					</view>
					<view class="flex align-items botmoney">
						<span>待入账 ￥{{settleInfo.expected_incoming_amount?settleInfo.expected_incoming_amount:0}}</span>
						<span class="shu"></span>
						<span>累计收益
							￥{{settleInfo.accumulated_incoming_amount?settleInfo.accumulated_incoming_amount:0}}</span>
					</view>
				</view>

				<!-- 成为主理人 -->
				<view class="first service flex justify-center align-items" @click="iszhuliren()">
				</view>



				<view class="flex align-items justify-center w-100" style="margin-top: 30rpx;margin-bottom: 6rpx;">
					<image src="/static/homehot.png" mode="" style="width: 469rpx;height: 54rpx;"></image>
				</view>

				<!-- 热门活动 -->
				<view class="hot flex flex-column">
					<view class="content flex align-items flex-column">
						<view class="flex flex-column w-100 bbb" v-for="(item, index) in hotList" :key="index"
							@click="detail(item.id)">
							<view class="flex " style="margin-top: 30rpx">
								<view class="right flex flex-column" style="align-items: self-start">
									<span class="title white-space">{{ item.title }}</span>
									<span class="first-image flex align-items" style="margin-top: 20rpx;width: 100%;">
										<image :src="item.user.avatar"
											style="width: 36rpx;height: 36rpx;border-radius: 80rpx"></image>
										<span class="white-space" style="color: #9C9C9C;margin-left: 10rpx;width: 210rpx;">{{ item.user.nickname }}</span>
										<view class="white-space" style="width: 56%;">
											<span style="color:#0CA013;margin-left: 10rpx;"
												v-for="items_t in item.classes_cate">#{{ items_t }}</span>
										</view>

									</span>

									<view class="flex flex-column" style="margin-top: 20rpx;">
										<view class="time flex align-items white-space">
											<span class="time_tex" v-if="item.status == 2">{{ '报名中' }}</span>
											<span style="margin:0 20rpx;">{{ dateWeeks(item.start_time) }}</span>
											<span style="width: 1rpx;height: 22rpx;background: #323232;"></span>
											<span class="white-space" style="margin-left: 20rpx;width: 260rpx;">{{ item.address_detail
												}}</span>
										</view>

									</view>

									<view class="flex align-items" style="margin-top: 20rpx;">
										<image class="imgs_con" v-for="(items_img, index) in item.images.slice(0, 3)"
											:key="index" :src="items_img" mode="aspectFill"></image>
									</view>
								</view>
							</view>
							<view class="bottom flex align-items">
								<view class="flex align-items toptext">
									<span class="flex align-items">
										<u-avatar-group :urls="item.join_info.users" keyName="avatar" size="30"
											maxCount="4" gap="0.4"></u-avatar-group>
										<image :class="item.join_info.users.length > 0 ? '' : 'smalld'"
											src="/static/index/dian.png"
											style="width: 60rpx;height: 60rpx;margin-left:-20rpx;z-index: 1;"></image>
									</span>
									<view class="number flex align-items">
										{{ Number(item.join_info.people_number) + "人已上车" }}
									</view>
								</view>
								<span v-if="item.status == 5" class="part1 flex justify-center align-items">
									已结束
								</span>
								<span v-else class="part flex justify-center align-items">
									上车
								</span>
							</view>
						</view>
					</view>
				</view>
				<view style="height: 80rpx;width: 100%;"></view>
			</scroll-view>



			<!-- 授权登录 -->
			<u-popup :show="showPopup" mode="center" round="20"
				:customStyle="{ 'width': '644rpx', 'height': '426rpx','position': 'relative'}" :closeable="false"
				@close="close">
				<!-- 隐私协议 -->
				<lsl-protocol-popup title="用户协议和隐私政策提示" predesc="感谢您使用洛阳多样青春搭+。为保护您的个人信息安全，在您洛阳多样青春搭+的服务前，请务必仔细阅读"
					subdesc='以了解详细内容。如您同意，请点击“同意并继续”并开始使用我们的服务。'
					top_img='https://naweigetetest2.hschool.com.cn/dyqc/dyqclogo.png' color="#C9935C" hideTabBar="true"
					:onNeed='false' :other="other" :title_style="'padding-top:60rpx;'"
					open_type='agreePrivacyAuthorization' @agree_call="miniLogin()">
				</lsl-protocol-popup>

				<view class="popup">
					<img class="img_shouquan" src="https://naweigetetest2.hschool.com.cn/dyqc/shou1.png" alt="" />
					<text id="txt2">授权成功后即可参加多种户外活动</text>
					<button class="texbut" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
						授权登录
						<!-- <u-icon label="" labelColor="#fff" size="28" name="/static/wechat1.png"></u-icon> -->
					</button>
				</view>
			</u-popup>

			<u-toast ref="uToast"></u-toast>
		</view>

		<tab-bar :tabBarShow="tabBarShow"></tab-bar>
	</view>

</template>

<script>
	import {
		mapActions
	} from 'vuex';
	import {
		dateWeek
	} from '../../utils/dateFormat'
	export default {
		...mapActions(['number']),
		data() {
			return {
				shop_auth_info: {},
				settleInfo: {},
				tabBarShow: null,
				topHeight: '',
				hotList: [],
				showPopup: false,
				userName: '请登录',
				bio: '',
				actives: 0,
				pushActives: 0,
				show: false,
				avatar: '',
				agree: false,
				other: [{
						tit: '《用户协议》',
						type: 'page', // doc自行下载打开文档 page跳转页面
						content: '/packageA/Negotiate/agreement', // 文档地址/页面跳转地址
					},
					{
						tit: '《隐私政策》',
						type: 'page', // doc自行下载打开文档 page跳转页面
						content: '/packageA/Negotiate/privacy', // 文档地址/页面跳转地址
					},
				],
				protocolArr: ["<隐私协议>"],
				userinfo: {},
				shop_id: '',
				openidInfo: null,
				token: '',
				src: '',
				openid_code: '',
				code: '',
				unread_number: '',
				init: {},
				timer: null, // 定时器
				list: [{
						image: '../../static/my/class.png',
						text: '我的课程',
						url: '/packageA/my/orderList'
					},
					{
						image: '../../static/my/make.png',
						text: '预约课程',
						url: '/packageA/my/makeList'
					},
					{
						image: '../../static/my/start.png',
						text: '收藏课程',
						url: '/packageA/my/Collect'
					},
					{
						image: '../../static/my/afterSales.png',
						text: '售后订单',
						url: '/pages/afterSales/list'
					},
					{
						image: '../../static/my/Course.png',
						text: '课程表',
						url: '/packageA/my/timetable',
					},
					{
						image: '../../static/my/Feedback.png',
						text: '课程反馈',
						url: '/packageA/my/Feedback',
					},
					{
						image: '../../static/my/station.png',
						text: '消息通知',
						url: '/packageA/my/station'
					},
					// {
					// 	image: '../../static/my/orderList.png',
					// 	text: '订单管理',
					// 	url: '/packageA/my/orderList'
					// },
					{
						image: '../../static/my/Personal.png',
						text: '修改信息',
						url: "/packageA/my/person-detail"

					},
					{
						image: '../../static/my/exercise.png',
						text: '我的活动',
						url: "/packageA/activity/exercise"
					},
					{
						image: '../../static/my/Help.png',
						text: '帮助',
						url: "/packageA/my/help"
					},

				],

			};
		},
		// watch: {
		// 	showPopup: {
		// 		handler(newVal, oldVal) {
		// 			if (newVal == true) {
		// 				uni.hideTabBar()
		// 			} else {
		// 				uni.showTabBar()
		// 			}
		// 		},
		// 		immediate: true,
		// 		deep: true
		// 	},
		// },
		// onLoad() {
		// 	this.token = uni.getStorageSync('token')
		// 	const userinfo = uni.getStorageSync('userInfo')
		// 	const niName = uni.getStorageSync('niName')
		// 	if (niName) {
		// 		this.userName = niName
		// 	} else {
		// 		this.userName = userinfo.name
		// 	}
		// 	this.avatar = uni.getStorageSync('avatar');
		// 	let openidInfo = uni.getStorageSync('openidInfo')
		// 	let _this = this;
		// 	if (!openidInfo) {
		// 		_this.miniLogin();
		// 	}
		// },
		onLoad() {
			uni.hideTabBar()
		},

		onShow() {
			this.token = uni.getStorageSync('token');
			if (this.token) {
				this.getUserInfo();
			}
			this.unread_number = this.number
			const userinfo = uni.getStorageSync('userInfo')
			const activityInfo = uni.getStorageSync('activityInfo')
			const niName = uni.getStorageSync('niName')
			if (niName) {
				console.log('1-',1, 'niName', niName)
				this.userName = niName
			} else {
				console.log(2)
				this.userName = userinfo.name
			}
			this.actives = activityInfo.join_num;
			this.pushActives = activityInfo.release_num;

			this.avatar = uni.getStorageSync('userInfo').avatar || '../../static/moren.png';
			let openidInfo = uni.getStorageSync('openidInfo');

			let _this = this;
			// if (!openidInfo) {
			// 	_this.miniLogin();
			// }
			this.number();
			this.getinit();
			this.getHotList();

		},
		created() {
			const systemInfo = uni.getSystemInfoSync();
			const statusBarHeightPx = systemInfo.statusBarHeight;
			const windowWidthPx = systemInfo.windowWidth;
			// 将 statusBarHeight 从 px 转换为 rpx
			const statusBarHeightRpx = statusBarHeightPx * (750 / windowWidthPx);

			this.topHeight = statusBarHeightRpx;


		},
		beforeDestroy() {
			this.stopTimer();
		},
		mounted() {
			this.startTimer();
		},
		methods: {
			dateWeeks(e) {
				return dateWeek(e);
			},
			// 获取热门课程
			getHotList() {
				uni.$u.http
					.get("/api/school.new_activity/activity_list", {
						params: {
							page: 1,
							limit: 1,
							order: "hot",
							status: "2", //5已结束
							auth_status: 1,
						},
					})
					.then((res) => {
						if (res.code == 1) {
							const list = res.data.list.data || [];
							this.hotList = list;
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},
			// 跳转详情
			detail(id) {
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id,
				});
			},
			iszhuliren() {
				if (this.shop_auth_info.auth_status == 1) {
					uni.$u.toast('您已是主理人！');
					return;
				}
				uni.navigateTo({
					url: "/pages/my/applyOwner"
				})
			},
			callPhone(phone) {
				console.log('phone',phone)
				uni.showToast({
					title: '开发中，敬请期待～',
					icon: 'none'
				});
				// uni.makePhoneCall({
				// 	phoneNumber: phone
				// })
			},
			getinit() {
				uni.$u.http.get('/api/index/init', {}).then(res => {
					if (res.code == 1) {
						this.init = res.data.customer_service;
						if (res.data.base_info.miniapp_activity_swtich == 1) {
							this.tabBarShow = 4;
						} else {
							this.tabBarShow = 3;
						}
						uni.setStorageSync('init', res.data.customer_service)
						uni.setStorageSync('init_info', res.data.activity_info)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			toMycy() {
				// if (!this.token) {
				// 	this.miniLogin()
				// 	this.showPopup = true
				// } else {
				uni.navigateTo({
					url: "/packageA/my/exercise?status=-3,0,2,3,4,5,6,7,9",
				})
				// }
			},
			toMyfb() {
				// if (!this.token) {
				// 	this.miniLogin()
				// 	this.showPopup = true
				// } else {
				uni.navigateTo({
					url: "/packageA/my/orderList",
				})
				// }
			},


			// 退出登录
			out() {
				let _this = this;
				uni.clearStorageSync()
				uni.reLaunch({
					url: '/pages/my/index'
				})
				// uni.$u.http.post('/api/user/logout', {}).then(res => {
				// 	if (res.code == 1) {
				// 		uni.reLaunch({
				// 			url: '/pages/my/index'
				// 		})
				// 	}
				// }).catch(error => {
				// 	uni.showToast({
				// 		title: res.msg,
				// 		icon: 'none',
				// 		duration: 2000
				// 	})
				// })
			},
			to(url) {
				console.log('url',url)
				uni.navigateTo({
					url: url
				});
			},
			//扫码逻辑
			// jump(url, type) {
			// 	console.log(url)
			// 	if (url == '/packageA/my/cancel') {
			// 		uni.scanCode({
			// 			success: (res) => {
			// 				console.log('扫码结果：' + res.result);
			// 				uni.navigateTo({
			// 					url: url + '?res=' + encodeURIComponent(res.result)
			// 				});
			// 			},
			// 			fail: (err) => {
			// 				console.error('扫码失败：' + err);
			// 				uni.showToast({
			// 					title: '扫码已取消',
			// 					icon: 'none'
			// 				});
			// 			}
			// 		});
			// 	} else if (url == '/packageA/my/addManagement') {
			// 		uni.$u.http.get('/api/school/shop/auth_info',).then(res => {
			// 			if (res.code == 1) {
			// 				if (res.data.check_full) {
			// 					uni.navigateTo({
			// 						url: "/packageA/my/addManagement?shop_id=" + this.shop_id
			// 					})
			// 				} else {
			// 					uni.showToast({
			// 						title: '必要展示信息未完善，请先在机构完善展示信息',
			// 						icon: 'none',
			// 						duration: 2000
			// 					});
			// 				}

			// 			} else {
			// 				uni.showToast({
			// 					title: res.msg,
			// 					icon: 'none',
			// 					duration: 2000
			// 				});
			// 			}
			// 		}).catch(error => {
			// 			console.error('请求失败', error);
			// 		});
			// 	} else if (url == '/packageA/my/cancelActivity') {
			// 		uni.scanCode({
			// 			success: (res) => {
			// 				console.log('扫码结果：' + res.result);
			// 				uni.navigateTo({
			// 					url: url + '?res=' + encodeURIComponent(res.result)
			// 				});
			// 			},
			// 			fail: (err) => {
			// 				console.error('扫码失败：' + err);
			// 				uni.showToast({
			// 					title: '扫码已取消',
			// 					icon: 'none'
			// 				});
			// 			}
			// 		});
			// 	} else {
			// 		uni.navigateTo({
			// 			url: url
			// 		});
			// 	}
			// },
			close() {
				this.showPopup = false
			},

			//小程序静默登录
			miniLogin() {
				console.log('小程序静默登录');
				let _this = this;
				uni.getProvider({
					service: 'oauth',
					success: function(res) {
						console.log('小程序静默登录-res',res)
						if (~res.provider.indexOf('weixin')) {
							uni.login({
								provider: 'weixin',
								success: function(res) {
									console.log('res.code', res.code)
									_this.getOpenidByCode(res.code, 1)
									_this.code = res.code
									uni.setStorageSync('code', res.code)
								},
								fail: function(error) {
									console.log('authError', error);
								}
							})
						} else {
							uni.login({
								provider: 'toutiao',
								success: function(res) {
									console.log('res.code', res.code)
									_this.getOpenidByCode(res.code, 2)
									_this.code = res.code
									uni.setStorageSync('code', res.code)
								},
								fail: function(error) {
									console.log('authError', error);
								}
							})
						}
					}
				});

			},
			//根据code获取openid
			getOpenidByCode(code, val) {
				let _this = this;
				if (val == 1) {
					uni.$u.http.post('/api/user/getOpenid ', {
						code: code,
					}).then(res => {
						if (res.code == 1) {
							_this.openidInfo = res.data;
							uni.setStorageSync('openidInfo', res.data)
							// _this.$api.setCache('openidInfo', res.data);
						}
					}).catch(error => {
						console.log('获取openid失败：', error);
					})
				} else {
					uni.$u.http.post('/api/user/getTtOpenid ', {
						code: code,
					}).then(res => {
						if (res.code == 1) {
							_this.openidInfo = res.data;
							uni.setStorageSync('openidInfo', res.data)
							// _this.$api.setCache('openidInfo', res.data);
						}
					}).catch(error => {
						console.log('获取openid失败：', error);
					})
				}

			},
			// 获取手机号
			getPhoneNumber(e) {
				var _this = this;
				//_this.miniLogin()
				console.log('获取手机号',e,e.detail.iv);
				if (!e.detail.iv) {
					console.log('!e.detail.iv');
					uni.showToast({
						title: '授权失败，请重新授权',
						icon: 'none',
						duration: 2000
					})
					_this.miniLogin();
					// this.$api.toast('授权失败，请重新点击授权');
					return false;
				}

				// if (e.detail.errMsg !== "getPhoneNumber:ok") {
				// 	uni.showToast({
				// 		title: '授权失败，请重新点击授权',
				// 		icon: 'none',
				// 		duration: 2000
				// 	})
				// 	_this.miniLogin();
				// 	return false;
				// } 
				_this.miniLogin();
				
				//if (!openidInfo) {
					uni.showLoading({
						title: '获取信息中...'
					});
					setTimeout(() => {
						var openidInfo = uni.getStorageSync('openidInfo');
						//console.log(333)
						uni.hideLoading();
						_this.decodeMobile({
							encryptedData: e.detail.encryptedData,
							iv: e.detail.iv,
							sessionKey: openidInfo.session_key,
						}, openidInfo);
					}, 2000)
					
					return false;
				//}

			},
			//解析手机号
			decodeMobile(data, openidInfo) {
				var _this = this;
				uni.$u.http.post('/api/user/decodeData', {
					encryptedData: data.encryptedData,
					iv: data.iv,
					sessionKey: data.sessionKey,
				}).then(res => {
					console.log('解析手机号-res', res)
					if (res.code == 1) {
						_this.userRegister(res.data.phoneNumber, openidInfo.openid);
					} else {
						uni.showToast({
							title: res.msg || '解析手机号失败',
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg || '解析手机号失败')
					}

				}).catch(error => {

				});
			},
			//用户注册
			userRegister(mobile, openid) {
				var _this = this;
				console.log('用户注册-uni.getStorageInfoSync("code")',uni.getStorageInfoSync("code"))
				uni.$u.http.post('/api/user/registerLogin', {
					mobile: mobile,
					openid: openid,
					code: _this.code,
					apptype: 'miniapp',
					platform: 'wechat'
				}).then(res => {
					if (res.code == 1) {
						uni.setStorageSync('token', res.data.token)
						_this.getUserInfo()
						this.token = res.data.token
						uni.showToast({
							title: '登陆成功',
							icon: 'none',
							duration: 2000
						})
						// _this.$api.setCache('token', res.data.token);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						uni.clearStorageSync()
						_this.miniLogin()
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {
					console.log('error', error);
					// 		_this.$api.modal('温馨提示', error.data.msg, function() {

					// 		}, false);
				});

			},
			// 获取个人信息
			getUserInfo() {
				uni.$u.http.get('/api/user/index', {}).then(res => {
					if (res.code == 1) {
						uni.setStorageSync('userInfo', res.data.user_info)
						uni.setStorageSync('settleInfo', res.data.settle_info)
						uni.setStorageSync('activityInfo', res.data.activity_info)

						this.showPopup = false
						this.userName = res.data.user_info.nickname;
						this.bio = res.data.user_info.bio;
						this.avatar = res.data.user_info.avatar;
						this.actives = res.data.activity_info.join_num;
						this.pushActives = res.data.activity_info.release_num;

						this.settleInfo = res.data.settle_info;

						this.shop_auth_info = res.data.shop_auth_info;

						uni.setStorageSync("niName", res.data.user_info.nickname)
						this.userinfo = res.data.user_info
						this.token = res.data.user_info.token
					} else {
						this.showPopup = false
						uni.showToast({
							title: '登陆状态失效...',
							icon: 'none',
							duration: 2000
						})
					}

				}).catch(error => {
					console.log('error', error);
					this.showPopup = false;
					uni.clearStorageSync()
					uni.reLaunch({
						url: '/pages/my/index'
					})
					uni.showToast({
						title: '登陆状态失效...',
						icon: 'none',
						duration: 2000
					})
				})
			},
			// getPhoneNumber(e) {
			// 	var that = this
			// 	if (e.detail.errMsg == 'getPhoneNumber:ok') {
			// 		wx.login({
			// 			success: function(res) {
			// 				uni.$u.http.post('/Loginye/wx_getcode', {
			// 					code: e.detail.code,
			// 					openid_code: res.code
			// 				}).then(res => {
			// 					if (res.code == 0) {
			// 						uni.setStorageSync('token', res.data)
			// 						uni.setStorageSync('userInfo', res.userinfo)
			// 						uni.setStorageSync('avatar', res.userinfo.head_img)
			// 						that.userName = res.userinfo.name
			// 						that.token = res.data
			// 						that.src = res.userinfo.head_img
			// 						that.showPopup = false
			// 						console.log(that, "sdad")
			// 						that.$refs.uToast.show({
			// 							type: 'success',
			// 							message: '授权成功'
			// 						})
			// 					}
			// 				}).catch(err => {
			// 					console.log(err);
			// 				})
			// 			}
			// 		})

			// 	} else {
			// 		this.$refs.uToast.show({
			// 			type: 'error',
			// 			message: '授权失败,请重试'
			// 		})
			// 		this.showPopup = false
			// 	}
			// },


			clickTop() {
				console.log('clickTop');
				// uni.navigateTo({
				// 	url: '/packageA/my/person-detail'
				// })
				// console.log(this.token)
				if (!this.token) {
					//this.miniLogin()
					this.showPopup = true
				} else {
					uni.navigateTo({
						url: "/packageA/my/person-detail",
					})
				}
			},
			checkTokenAndRequest() {
				if (uni.getStorageSync('token')) {
					this.number();
				}
			},
			// 定时器
			startTimer() {
				if (!this.timer) { // 确保定时器只启动一次
					this.timer = setInterval(() => {
						this.checkTokenAndRequest();
					}, 10000); // 每隔 10 秒执行一次
				}
			},
			stopTimer() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
			// 未读消息
			number() {
				let that = this
				uni.$u.http.get('/api/school/message/message_count', {
					params: {},
				}).then(res => {
					if (res.code == 1) {
						that.$store.commit('setNumber', res.data.unread_number);
						that.unread_number = res.data.unread_number
						uni.setStorageSync('unread_number', res.data.unread_number)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},

		}
	}
</script>

<style lang="scss" scoped>
	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}
	
	.myBox {
		position: fixed;
		padding-top: 200rpx;
		width: 750rpx;
		min-height: 100vh;
		background-color: #F5F5F5;
		background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/bgx2.png");
		background-size: 100%;
		background-repeat: no-repeat;
	}

	.box {
		border-radius: 8rpx 8rpx 8rpx 8rpx;

		// padding-top: env(safe-area-inset-top);
		/* 顶部安全距离 */
		.backImg {
			position: fixed;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			z-index: -1;
		}
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}


	.con-center {
		height: 1314rpx;

		.header {
			width: 690rpx;
			justify-content: space-between;
			margin: 0 auto;

			.hea-left {
				width: 100%;
				position: relative;

				.avatar {
					width: 140rpx;
					height: 140rpx;
					border-radius: 50%;
				}



				.first-header {
					margin-left: 18rpx;

					span {
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 34rpx;
						color: #181818;
						// line-height: 26rpx;
					}
				}

				.line {
					width: 320rpx;
					height: 1rpx;
					background: #BABDC7;
					border-radius: 0rpx 0rpx 0rpx 0rpx;
					margin: 19rpx 0 7rpx 0;
				}

				.red {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 26rpx;
					color: #FF4949;
					line-height: 32rpx;
				}
			}

			.hea-right {
				// width: 20%;
			}

			.line {}

		}
	}

	.top_box {
		width: 690rpx;
		margin: 0 auto;
		margin-top: 32rpx;

		view {
			background: #FFFFFF;
			border-radius: 18rpx;
			width: 330rpx;
			padding: 20rpx 0;

			image {
				width: 70rpx;
				height: 70rpx;
				margin-left: 30rpx;
			}

			span {
				margin-left: 20rpx;
			}

			.p1 {
				width: 100%;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #323232;
			}

			.p2 {
				width: 100%;
				margin-top: 10rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #9C9C9C;
			}
		}

		.top_box_right {
			margin-left: 30rpx;
		}
	}

	.first {
		width: 690rpx;
		padding-bottom: 32rpx;
		margin-top: 52rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;

		.centerBox {
			flex-wrap: wrap;

			view {
				width: 120rpx;
				margin: 42rpx 90rpx 0 0;
				position: relative;

				.numberPos {
					position: absolute;
					right: 0%;
					top: 0;
				}

				.text {
					font-weight: 500;
					font-size: 30rpx;
					color: #181818;
				}
			}
		}
	}

	.popup {

		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-left: 73rpx;
		padding-right: 73rpx;

		.img_shouquan {
			position: absolute;
			width: 680rpx;
			height: 857rpx;
			left: -25rpx;
			top: -390rpx;
		}


		#txt2 {
			text-align: center;
			margin-top: 200rpx;
			font-size: 28rpx;
			color: #525252;
			margin-bottom: 60rpx;
			position: relative;
			font-weight: 500;
		}

		.texbut {
			width: 317rpx;
			height: 90rpx;
			border-radius: 45rpx;
			background: #323232;
			font-size: 34rpx;
			color: #fff;
			font-weight: bold;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.jifen {
		width: 690rpx;
		height: 86rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		margin-top: 41rpx;
		justify-content: space-between;

		.left {
			margin-left: 32rpx;

			.title {
				width: 104rpx;
				height: 32rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 26rpx;
				color: #181818;
				line-height: 32rpx;
				margin: 0 12rpx 0 6rpx;
			}

			.scoring {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 42rpx;
				color: #FFB134;
				line-height: 32rpx;
			}
		}

		.right {
			margin-right: 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #9E9E9E;
			line-height: 32rpx;

			span {
				margin-right: 8rpx;
			}
		}
	}

	.mymoney {

		width: 690rpx;
		height: 200rpx;
		background: #323232;
		border-radius: 24rpx;
		position: relative;
		align-items: flex-start;
		margin: 0 auto;
		margin-top: 30rpx;

		.myyue {
			margin-top: 30rpx;
			margin-left: 30rpx;
			color: #ffffff;
		}

		.tixian {
			width: 148rpx;
			height: 68rpx;
			position: absolute;
			right: -10rpx;
			top: 45rpx;

		}

		.tixianwz {
			position: absolute;
			right: 35rpx;
			top: 62rpx;
			font-size: 28rpx;
			color: #323232;
		}

		.botmoney {
			margin-top: 22rpx;
			margin-left: 30rpx;
			color: #777777;
			font-weight: 400;
			font-size: 26rpx;

			.shu {
				width: 2rpx;
				height: 22rpx;
				margin: 0 20rpx;
				background: #848484;
			}
		}

	}

	.service {
		margin: 0 auto;
		margin-top: 30rpx;
		height: 206rpx;
		background: url("https://naweigetetest2.hschool.com.cn/dyqc/zhuliren.png");
		background-size: 690rpx 206rpx;
		padding-bottom: 0;
	}

	.hot {
		//   margin-top: 30rpx;
		margin-bottom: 100rpx;


	}

	.part {
		width: 162rpx;
		height: 70rpx;
		background: url(@/static/index/shangche.png);
		background-size: 162rpx 70rpx;
		background-repeat: no-repeat;
		font-family: 'YouSheBiaoTiHei';
		font-weight: 400;
		font-size: 42rpx;
		color: #BBFC5B;
	}

	.part1 {
		width: 162rpx;
		height: 70rpx;
		background: url(@/static/index/shangchew.png);
		background-size: 162rpx 70rpx;
		background-repeat: no-repeat;
		font-family: 'YouSheBiaoTiHei';
		font-weight: 400;
		font-size: 42rpx;
		color: #9C9C9C;
	}

	.content {
		// height: 462rpx;
		// overflow-x: auto;
		/* 允许横向滚动 */
		// white-space: nowrap;

		width: 690rpx;
		margin: 0 auto;

		.bbb {
			background: #ffffff;
			margin-top: 24rpx;
			border-radius: 36rpx;
		}

		.right {
			margin-left: 30rpx;
			width: 100%;

			.title {
				width: 93%;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 30rpx;
				color: #323232;
			}

			.first-image {
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #9C9C9C;

				span {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #222222;
				}
			}

			.row {
				margin: 10rpx 0 26rpx 0;

				.remen {
					// width: 136rpx;
					height: 40rpx;
					background: #ebfaf5;
					border-radius: 4rpx 4rpx 4rpx 4rpx;

					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24rpx;
					color: #14bc84;
					line-height: 28rpx;
					padding: 2rpx 8rpx;
				}

				.line-colum {
					width: 1rpx;
					height: 32rpx;
					background: #7a7a7a;
					margin: 0 13rpx;
				}

				.name {
					width: 110rpx;
					height: 32rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 26rpx;
					color: #7a7a7a;
					line-height: 32rpx;
					margin-left: 12rpx;
				}
			}


			.jigou {
				width: 312rpx;
				// height: 32rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #7a7a7a;
				margin: 4rpx 0 14rpx 0;
			}

			.time {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #323232;
				line-height: 30rpx;

				.time_tex {
					width: 116rpx;
					height: 42rpx;
					background: #BBFC5B;
					border-radius: 159rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #323232;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.time_texs {
					background: #FF4810;
					color: #ffffff;
				}

			}

			.imgs_con {
				width: 202rpx;
				height: 202rpx;
				margin-right: 12rpx;

				&:first-child {
					border-radius: 18rpx 0 0 18rpx;
				}

				&:nth-child(3) {
					border-radius: 0 18rpx 18rpx 0;
				}
			}

		}

		.bottom {
			margin: 32rpx 0 32rpx 24rpx;
			width: 655rpx;

			.toptext {
				width: 480rpx;

				.smalld {
					margin-left: 0 !important;
				}
			}

			.number {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #3D3D3D;
				line-height: 30rpx;
				width: 242rpx;
				margin-left: 20rpx;
			}

			.text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 26rpx;
				color: #7a7a7a;
			}

			.money {
				width: 150rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 36rpx;
				color: #ff2323;
				justify-content: flex-end;
			}
		}

		.ovr {
			width: 312rpx;
			background: #ffffff;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			margin-top: 18rpx;
			margin-right: 24rpx;
		}
	}
</style>