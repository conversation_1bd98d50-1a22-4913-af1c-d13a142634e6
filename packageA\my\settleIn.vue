<template>
	<view class="box flex flex-column align-items">
		<view class="top flex flex-column align-items">
			<view class="top-one flex align-items" style="padding-bottom: 25rpx;border-bottom: 1rpx solid #D9D9D9;">
				<span>
					<image src="../../static/shou.png" mode="" style="width: 160rpx;height: 160rpx;"></image>
				</span>
				<view class="flex flex-column" style="margin-left: 33rpx;">
					<span class="title">{{detail.name}}</span>
					<span class="line-row"></span>
					<span class="yao">邀请您入驻 <span>课程讲师</span> </span>
				</view>
			</view>
			<view class="top-one flex flex-column" style="align-items: self-start;border-bottom: 1rpx solid #D9D9D9;">
				<span class="jigou"> 机构信息 </span>
				<view class="flex align-items" style="justify-content: space-between; width: 100%;">
					<span class="hui">联系方式：</span>
					<span class="hui flex align-items" @click.stop="handlePhone(detail.tel)"> {{detail.tel}}
						<image src="../../static/tel.png" mode="" style="width: 36rpx; height: 36rpx;"></image>
					</span>
				</view>
				<view class="flex align-items"
					style="justify-content: space-between; width: 100%;margin: 8rpx 0 25rpx 0;">
					<span class="hui">地址：</span>
					<span class="hui flex align-items"
						@click.stop="toMap(detail.latitude,detail.longitude,detail.address_detail)">{{detail.address_detail}}
						<image src="../../static/center/address.png" mode="" style="width: 36rpx; height: 36rpx;">
						</span>
				</view>
			</view>
			<view class="top-one" style="margin: 17rpx 0 24rpx 0;">
				<span class="hui">
					<u-parse :content="detail.content"></u-parse>
				</span>
			</view>
		</view>
		<view class="detail flex flex-column ">
			<span class="xinxi">个人信息</span>
			<view class="flex align-items" style="justify-content: space-between;width: 100%;">
				<span class="hui">姓名：</span>
				<span class="hui">{{userInfo.realname}}</span>
			</view>
			<view class="flex align-items" style="justify-content: space-between;width: 100%;margin: 8rpx 0 25rpx 0;">
				<span class="hui">联系电话：</span>
				<span class="hui">{{userInfo.mobile}}</span>
			</view>
		</view>
		<view class="footer flex align-items" style="justify-content: space-evenly;">
			<span @click.stop="btn(2)">拒绝</span>
			<span style="color: #FFFFFF;background: #008CFF;" @click.stop="btn(1)">同意</span>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				shop_id: '',
				id:'',
				detail: {},
				userInfo:{}

			};
		},
		onLoad(option) {
			this.shop_id = option.shop_id
			this.id = option.id
			this.getDetail(option.id)
			this.getJGDetail(option.shop_id)
			this.userInfo = uni.getStorageSync('userInfo')
			
		},
		methods: {
			getDetail(id) {
				uni.$u.http.get('/api/school/message/detail', {
					params: {
						id: id
					}
				}).then(res => {
					if (res.code == 1) {
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 获取机构详情
			getJGDetail(id) {
				uni.$u.http.get('/api/school/shop/detail', {
					params: {
						id: id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {

				});
			},
			// 导航
			toMap(latitude, longitude, name) {
				uni.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					success: function() {
						console.log('success');
					}
				});
			},
			// 拨号
			handlePhone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},
			btn(status){
				uni.$u.http.post('/api/school.user_auth/authorization', {
					shop_id:this.shop_id,
					status:status
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '同意入驻',
							icon: 'none',
							duration: 2000
						})
						uni.navigateBack()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						// _this.$api.toast(res.msg);
					}
				}).catch(error => {
				
				});
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.w-100 {
		width: 100%;
	}

	.m-l-24 {
		margin-left: 24rpx;
	}

	.m-r-24 {
		margin-right: 24rpx;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.box {
		width: 750rpx;
		height: 100vh;
		background-color: #F1F2F8;
		overflow-x: hidden;
		overflow-y: scroll;
	}

	.top {
		width: 750rpx;
		background-color: #FFFFFF;
		padding-top: 30rpx;
		border-radius: 24rpx;

		.top-one {
			width: 690rpx;

			.title {
				width: 473rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #7A7A7A;
			}

			.line-row {
				width: 473rpx;
				height: 1rpx;
				background: #D9D9D9;
				margin: 17rpx 0 23rpx 0;
			}

			.yao {
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				font-size: 26rpx;
				color: #7A7A7A;

				span {
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 42rpx;
					color: #008CFF;
				}
			}

			.jigou {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 32rpx;
				color: #4B4B4B;
				margin-top: 23rpx;
			}

		}
	}

	.detail {
		width: 642rpx;
		padding: 0 24rpx;
		background: #FFFFFF;
		margin-top: 24rpx;
		border-radius: 24rpx;
		margin-bottom: 150rpx;

		.xinxi {
			margin: 32rpx 0 24rpx 0;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 32rpx;
			color: #4B4B4B;
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		height: 122rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -6rpx 12rpx 0rpx rgba(111, 190, 255, 0.1);

		span {
			width: 306rpx;
			height: 80rpx;
			border-radius: 401rpx 401rpx 401rpx 401rpx;
			border: 2rpx solid #008CFF;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 34rpx;
			color: #008CFF;
		}
	}
</style>