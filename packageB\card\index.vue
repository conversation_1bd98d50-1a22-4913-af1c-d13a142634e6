<template>
	<view style="page">
		<view style="padding: 30rpx;">
			<view>
				<text style="color: #FF4810;font-size: 28rpx;">*</text>
				<text style="font-weight: 600;">上传身份证</text>
			</view>
			<view
				style="display: flex;justify-content: space-between;align-items: center;gap: 30rpx;margin-top: 30rpx;">
				<view style="width: 50%;text-align: center;">
					<u-upload  @afterRead="afterRead($event,0)" :previewFullImage="true"
						@delete="deletePic" @oversize="oversize"  :maxSize="1 * 1024 * 1024"
						:maxCount="1"> 
						<view style="width: 330rpx;height: 100px;">
							<image v-if="form.positive_img==''" src="/static/index/zheng.png" style="width: 100%;" mode="widthFix"></image>
							<image v-else :src="imgUrl+form.positive_img" style="width: 330rpx;height: 100px;" mode="aspectFit"></image>
						</view>
					</u-upload>
					<view style="font-size: 24rpx;margin-top: 30rpx;">点击上传身份证正面</view>
				</view>
				<view style="width: 50%;text-align: center;">
					<u-upload  @afterRead="afterRead($event,1)" :previewFullImage="true"
						@delete="deletePic" @oversize="oversize"  :maxSize="1 * 1024 * 1024"
						:maxCount="1">
						<view style="width: 330rpx;height: 100px;">
							<image v-if="form.back_img == ''" src="/static/index/bei.png" style="width: 100%;" mode="widthFix"></image>
							<image v-else :src="imgUrl+form.back_img" style="width: 330rpx;height: 100px;" mode="aspectFit"></image>
						</view>
					</u-upload>
					<view style="font-size: 24rpx;margin-top: 30rpx;">点击上传身份证反面</view>
				</view>
			</view>
		</view>
		<view style="height: 10rpx;width: 100%;background: #eeeeee;margin-top: 20rpx;"></view>
		<view style="padding: 30rpx;">
			<view>
				<text style="color: #FF4810;font-size: 28rpx;">*</text>
				<text style="font-weight: 600;">身份证信息</text>
			</view>
			<view>
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 40rpx 0px;">
					<view style="font-size: 30rpx;">
						姓名
					</view>
					<view>
						<input type="text" placeholder="请填写真实姓名" class="input" v-model="form.name" />
					</view>
				</view>
				<view style="height: 1px;width: 100%;background-color: #eeeeee;"></view>
				<view style="display: flex;justify-content: space-between;align-items: center;padding: 40rpx 0px 0px 0px;">
					<view style="font-size: 30rpx;">
						身份证号
					</view>
					<view>
						<input type="idcard" placeholder="请填写身份证号" class="input" v-model="form.idnum" />
					</view>
				</view>
			</view>
		</view>
		<view style="height: 10rpx;width: 100%;background: #eeeeee;"></view>
		<view style="position: fixed;width: 100%;bottom: 4%;">
			<view style="padding:30rpx">
				<cc-protocolBox :agree="agree" :name="protocolArr" :desc="desc"
					@click="protocolClick" @clickOne="protocolClick" ></cc-protocolBox>
			</view>
			<view v-if="agree" class="btn_1" @click="save()">提交信息</view>
			<view v-if="!agree" class="btn_2s">提交信息</view>
		</view>
		
		<u-popup @touchmove.native.stop.prevent :closeable="false" :show="show" :round="10" mode="center"
			@close="show = false" :safeAreaInsetBottom="false"  :custom-style="{
					width: '620rpx',
					padding: '30rpx 40rpx 42rpx 40rpx',
					margin: '0 auto',
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				}" :closeOnClickOverlay="false">
			<span style="font-size: 40rpx;font-weight: 800;">《【搭+】入驻协议》</span>
			<scroll-view ref="scrollView" :show-scrollbar='true'
				@scrolltolower="handleScroll" scroll-y="true" style="height: 800rpx;margin-bottom: 24rpx;padding-top: 30rpx;">
				<view class="popup flex align-items flex-column">
					<rich-text style="text-align: justify;" :nodes="Negotiate"></rich-text>
				</view>
			</scroll-view>
			<view class="popup-footer">
				<!-- <span class="zhixiao" v-if="agreeShow == false">我同意</span>
				<span class="zhixiao shows_zhidao" v-if="agreeShow == true" @click="change">我同意</span> -->
				<view style="gap: 20rpx;width: 100%;display: flex;justify-content: space-between;align-items: center;">
					<view class="btn_4" @click="show = false" >取消</view>
					<view class="btn_3" v-if="agreeShow == false">我同意</view>
					<view class="btn_2" v-if="agreeShow == true" @click="change">我同意</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				agreeShow:false,
				show:false,
				agree:false,
				protocolArr:"《【搭+】入驻协议》",
				desc:'的全部条款。',
				imgUrl:uni.$u.http.config.baseURL,
				Negotiate: null, // 入驻协议
				form:{
					name:'',
					idnum:'',
					positive_img:'',
					back_img:'',
				}
			}
		},
		onShow() {
			this.getInfo();
			this.getAgreement();
		},
		methods: {
			oversize(e) {
				this.$u.toast("请传1MB以内大小的图片！");
				return false;
			},
			protocolClick(tag) {
				this.show=true;
			},
			//同意
			handleScroll() {
				console.log(111);
				this.agreeShow = true
			},
			change(){
				this.show=false;
				this.agree=true;
			},
			getAgreement() {
				uni.$u.http.get('/api/index/agreement', {
					params: {
			
					}
				}).then(res => {
					if (res.code == 1) {
						this.Negotiate = (res.data.entry_agreement).replace(/\<img/gi,
							'<img style="max-width:100%;height:auto" ');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			},
			getInfo(){
				uni.$u.http.get('/api/school.real_name/info').then(res=>{
					console.log(res);
					if(res.data.card_info!='' && res.data.card_info!=null){
						this.form.name = res.data.card_info.name;
						this.form.idnum = res.data.card_info.idnum;
						this.form.positive_img = res.data.card_info.positive_img;
						this.form.back_img = res.data.card_info.back_img;
					}
				})
			},
			save(){
				if(this.form.positive_img=='' || this.form.back_img==''){
					uni.$u.toast('请上传身份证照片');
					return;
				}
				if(this.form.name=='' || this.form.idnum==''){
					uni.$u.toast('请填写身份证信息');
					return;
				}
				//正则判断身份证
				const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if(!reg.test(this.form.idnum)){
					uni.showToast({
						title: '请输入正确的身份证号',
						icon: "none",
						duration: 1500,
					});
					return;
				}
				uni.$u.http.post('/api/school.real_name/save',this.form).then(res=>{
					console.log(res);
					if(res.code==1){
						uni.$u.toast('提交成功');
						setTimeout(()=>{
							uni.navigateBack();
						},1000)
					}else{
						uni.$u.toast(res.data.msg);
					}
				})
			},
			async afterRead(item,index){
				console.log(item);
				const result = await this.uploadFilePromise(item.file.url, 'user');
				if(index == 0){
					this.form.positive_img = result;
				}else{   
					this.form.back_img = result;
				}
			},
			uploadFilePromise(url, category,index) {
				console.log('category', category)
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: 'https://naweigetetest2.hschool.com.cn/api/common/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						formData: {
							user: 'test',
							category: category
						},
						header: {
							"token": uni.getStorageSync("token")
						},
						success: (res) => {
							resolve(JSON.parse(res.data).data.url);
						},
						fail: (err) => {
							reject(err);
						}
					});
				});
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		font-family: PingFang SC, PingFang SC;
	}

	.input {
		text-align: right;
		font-size: 30rpx;
		color: #343434;
		line-height: 32rpx;
		width: 450rpx;
	}
	.btn_1 {
		width: 95%;
		height: 90rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 90rpx;
		text-align: center;
		margin: 0 auto;
		margin-top: 50rpx;
	}
	.btn_2s {
		width: 95%;
		height: 90rpx;
		background: #C1C1C1;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #ffffff;
		line-height: 90rpx;
		text-align: center;
		margin: 0 auto;
	}
	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		// margin: 30rpx 0;
		width: 100%;
	
		.zhixiao {
			height: 80rpx;
			background: #E8E8E8;
			//border-radius: 401rpx 401rpx 401rpx 401rpx;
			font-family: YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 36rpx;
			color: #9C9C9C;
			line-height: 32rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 100%;
			bottom: 0;
			border-radius: 0rpx 0rpx 20rpx 20rpx;
		}
	
		.shows_zhidao {
			background-color: #323232;
			color: #BBFC5B;
			font-weight: 400;
			font-size: 36rpx;
		}
	}
	.btn_2 {
			width: 50%;
			height: 80rpx;
			background: #323232;
			border-radius: 198rpx 198rpx 198rpx 198rpx;
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 32rpx;
			color: #BBFC5B;
			line-height: 80rpx;
			text-align: center;
			margin-top: 40rpx;
			z-index: 100;
		}
		.btn_3 {
			width: 50%;
			height: 80rpx;
			background: #E2E2E2;
			border-radius: 198rpx 198rpx 198rpx 198rpx;
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 32rpx;
			color: #999999;
			line-height: 80rpx;
			text-align: center;
			margin-top: 40rpx;
			z-index: 100;
		}
		.btn_4 {
			width: 50%;
			height: 80rpx;
			background: #ffffff;
			border: 1px solid #999999;
			border-radius: 198rpx 198rpx 198rpx 198rpx;
			font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
			font-weight: 400;
			font-size: 32rpx;
			color: #999999;
			line-height: 80rpx;
			text-align: center;
			margin-top: 40rpx;
			z-index: 100;
		}
</style>