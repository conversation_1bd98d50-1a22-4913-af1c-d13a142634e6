import App from './App'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import uView from "uview-ui";
import "utils/bass.scss";
import store from './store' // 引入 store
import tabBar from "components/my-tabbar.vue"//引入我们自己定义的tabBar组件
// 全局引用分享功能
// 导入并挂载全局的分享方法
import share from './utils/share.js'


Vue.mixin(share)
Vue.use(uView);
Vue.component('tab-bar', tabBar)//使用tabBar组件
Vue.config.productionTip = false
Vue.prototype.$store = store
App.mpType = 'app'
const app = new Vue({
  ...App,
  store
})
require('./utils/request.js')(app)
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif