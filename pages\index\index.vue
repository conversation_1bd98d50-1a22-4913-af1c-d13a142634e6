<template>
	<view class="recharge flex align-items justify-start flex-column" :style="{
		zIndex: '-1',
		backgroundPosition: 'top',
		backgroundRepeat: 'no-repeat',
	}">
		<view class="box" style="position: relative;">
			<!-- logo -->
			<view>
				<image src="/static/index/logo1.png" mode="" class="logo_css"></image>
			</view>
			<!-- 搜索 -->
			<view class="serch_top">
				<view class="serch_top1">
					<u-search class="u-search" v-model="keywords" placeholder="搜索您要找的内容" :showAction="false"
						search-icon="/static/index/search.png" @change="search()">
					</u-search>
					<span class="searchBtn" @click.stop="search()">搜索</span>
				</view>
				<image src="/static/index/schoolNight.png" mode="widthFix" class="imgstop_ye" @click="toshool"></image>
			</view>

			<scroll-view @scrolltolower="onScrolltolower" :refresher-enabled="true" :refresher-triggered="homrS"
				@refresherrefresh="onS" scroll-y="true" class="flex align-items"
				style="height: 1250rpx;box-sizing: border-box;">
				<view class="swiper-box">
					<!-- <MySwiper :list="swiperList"></MySwiper> -->
					<view class="swiper_s">
						<swiper class="swiper_s" :circular="true" :autoplay="true" indicator-active-color="#0DAE11"
							indicator-color="#ffffff" :indicator-dots="false" :current="swiperCurrent"
							@animationfinish="swiperChange">
							<swiper-item v-for="(item, index) in swiperList" :key="index" @click="openSwiper(item)">
								<view style="position: relative;width: 100%;height: 100%">
									<view class="fnon_tit"></view>
									<view class="text">{{ item.title ? item.title : '' }}</view>
									<!-- @click="click(item.url)" 跳转活动链接 -->
									<image class="swiper-image" :src="item.image" mode="scaleToFill" />
								</view>
							</swiper-item>

						</swiper>
						<!-- 自定义指示器 -->
						<view class="dots">
							<text v-for="(dot, idx) in swiperList.length" :key="idx"
								:class="['dot', { active: swiperCurrent === idx }]" />
						</view>
					</view>
				</view>

				<!-- 标签 -->
				<view class="tab_all">
					<!-- <view class="tabs">
					<view v-for="(tab, index) in list" :key="index" :class="['tab-item', { active: current === index }]"
						@click="handleClick(index, tab.id)">
						<image v-if="index != 0 && index != 1 && tab.image" :src="tab.image" mode="" class="imgs_bq">
						</image>
						<image v-if="index === 1" src="/static/index/schoolye.png" mode="" class="imgs_bq"></image>
						<span>{{ tab.name }}</span>
					</view>
				</view>
				<view class="allbq" @click="openBq">
					<image src="/static/index/qbbqfl.png" mode="" class="imgs_all"></image>
				</view> -->
					<view class="flex align-items justify-center w-100" style="margin-top: 50rpx;margin-bottom: 40rpx;">
						<image src="/static/homehot.png" mode="" style="width: 469rpx;height: 54rpx;"></image>
					</view>
				</view>
				<!-- 热门活动 -->
				<view class="hot flex flex-column">
					<view class="content flex align-items flex-column">
						<view class="flex flex-column w-100 bbb" v-for="(item, index) in hotList" :key="index"
							@click="detail(item.id)">


							<view class="flex" style="margin-top: 30rpx">
								<view class="right flex flex-column" style="align-items: self-start">
									<span class="title white-space" style="wi">{{ item.title }}</span>
									<view class="first-image flex align-items" style="margin-top: 20rpx;width: 100%;">
										<image v-if="cate_ids != 1" :src="item.user.avatar"
											style="width: 36rpx;height: 36rpx;border-radius: 80rpx;"></image>
										<image v-else :src="item.headimage"
											style="width: 36rpx;height: 36rpx;border-radius: 80rpx;"></image>
										<span v-if="cate_ids != 1" class="white-space"
											style="color: #9C9C9C;margin-left: 10rpx;width: 210rpx;">{{
											item.user.nickname }}</span>
										<span v-else class="white-space"
											style="color: #9C9C9C;margin-left: 10rpx;width: 210rpx;">{{
												item.teacher.name }}</span>
										<view class="white-space" style="width: 56%;">
											<span style="color:#0CA013;margin-left: 10rpx;"
												v-for="items_t in item.classes_cate">#{{ items_t
												}}</span>
										</view>
									</view>

									<view class="flex flex-column" style="margin-top: 20rpx;">
										<view class="time flex align-items white-space">
											<span class="time_tex" v-if="item.status == 2">{{ '报名中' }}</span>
											<!-- <span class="time_tex time_texs" v-if="item.status == 4">{{ '进行中' }}</span> -->
											<span style="margin:0 20rpx;">{{ dateWeeks(item.start_time) }}</span><span
												style="width: 1rpx;height: 22rpx;background: #323232;"></span>
											<span class="white-space" style="margin-left: 20rpx;width: 260rpx;">{{
												item.address }}{{
												item.address_detail }}</span>
										</view>

									</view>

									<view class="flex align-items justify-center"
										style="margin-top: 20rpx;margin-bottom: 32rpx;">
										<view class="imgs_con_div" v-for="(items_img, index) in item.images.slice(0, 3)"
											:key="index">
											<image class="imgs_con" :src="items_img" mode="aspectFill"></image>
										</view>
									</view>
								</view>
							</view>

							<view class="bottom flex align-items" v-if="cate_ids != 1">
								<view class="flex align-items toptext">
									<span class="flex align-items">
										<u-avatar-group :urls="item.join_info.users" keyName="avatar" size="30"
											:maxCount="4" gap="0.4"></u-avatar-group>
										<image src="/static/index/dian.png"
											:class="item.join_info.users.length > 0 ? '' : 'smalld'"
											style="width: 60rpx;height: 60rpx;margin-left:-20rpx;z-index: 1;"></image>
									</span>
									<view class="number flex align-items">
										{{ Number(item.join_info.people_number) + "人已上车" }}
									</view>
								</view>
								<span v-if="item.status == 5" class="part1 flex justify-center align-items">
									已结束
								</span>
								<span v-if="item.status == 2" class="part flex justify-center align-items">
									上车
								</span>

							</view>


						</view>
					</view>
				</view>
				<u-loadmore v-if="hotList.length > 0" :status="loadStatus"></u-loadmore>
				<view v-if="hotList.length > 0" style="height: 100rpx;width: 100%;"></view>
				<view class="flex flex-column flex-start align-items" v-if="hotList.length == 0">
					<image src="/static/message/activen.png" mode="" style="width: 180rpx;height: 180rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无发布活动</view>
				</view>
			</scroll-view>
			<!-- 全部标签 -->
			<u-popup :show="showPopbq" mode="bottom" round="20"
				:customStyle="{ 'width': '750rpx', 'height': '1040rpx' }" @close="closebq">
				<view class="popup_bq">
					<image @click="closebq" src="@/static/center/close.png" mode=""
						style="width: 44rpx;height: 44rpx;position: absolute;right: 30rpx;top: -160rpx;z-index: 55;">
					</image>
					<img src="https://naweigetetest2.hschool.com.cn/dyqc/biaoqian.png" alt="" />
					<view class="flex flex-column w-100 bqlist">
						<scroll-view scroll-y="true" class="flex align-items allbqs">
							<span>我的标签</span>
							<view class="flex align-items allmybqs">
								<view class="flex align-items bqpiece" v-for="(item_bq, index) in list" :key="index">
									<span>
										<!-- <image
                      v-if="item_bq.name != '全部'"
                      :src="item_bq.image"
                      style="width: 48rpx;height: 48rpx;margin-right:10rpx;"
                    ></image> -->
										{{ item_bq.name }}
									</span>
									<u-icon v-if="index != 0 && index != 1" name="close" color="#babdc7"
										@click="removebq(index)"></u-icon>
								</view>
							</view>

							<span style="margin-top: 30rpx;">全部标签</span>


							<view style="display: flex; flex-wrap: wrap;">
								<view class="flex align-items bqpiece" v-for="(item, index) in bqList" :key="index">
									<span>
										<!-- <image
                        :src="item.image"
                        style="width: 48rpx;height: 48rpx;margin-right:10rpx;"
                      ></image> -->
										{{ item.name }}
									</span>
									<u-icon name="plus" color="#babdc7" @click="addbq(item)"></u-icon>
								</view>
							</view>
						</scroll-view>

					</view>
				</view>


			</u-popup>


			<!-- <u-loadmore v-else style="margin-bottom: 60rpx;" :status="loadStatus" /> -->
		</view>
		<u-popup @touchmove.native.stop.prevent :show="formShow" :round="10">
			<view style="display: flex;align-items: center;justify-content: space-between;padding: 32rpx;">
				<view style="display: flex;align-items: center;">
					<view>
						<view style="height: 35rpx;width: 6rpx;background-color: #BBFC5B;"></view>
					</view>
					<view
						style="font-size: 32rpx;color: #3D3D3D;font-weight: 600;margin-left: 20rpx;font-family: PingFang SC, PingFang SC;">
						会员招募信息
					</view>
				</view>
				<view @click="formShow = false" style="font-family: PingFang SC, PingFang SC;font-size: 32rpx;">关闭
				</view>
			</view>
			<view style="width: 100%;height: 1px;background-color: #eeeeee;"></view>
			<view style="padding:30rpx 30rpx;">
				<view style="display: flex;align-items: center;justify-content: space-between;">
					<view style="color: #3D3D3D;font-weight: 400;font-family: PingFang SC, PingFang SC;">
						联系人
					</view>
					<view>
						<input type="text" placeholder="请填写" class="input" v-model="formData.name"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;">
					<view style="color: #3D3D3D;font-weight: 400;font-family: PingFang SC, PingFang SC;">
						联系电话
					</view>
					<view>
						<input type="text" placeholder="请填写" class="input" v-model="formData.mobile"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;">
					<view style="color: #3D3D3D;font-weight: 400;font-family: PingFang SC, PingFang SC;">
						企业名称
					</view>
					<view>
						<input type="text" placeholder="请填写" class="input" v-model="formData.enterprise_name"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;">
					<view style="color: #3D3D3D;font-weight: 400;font-family: PingFang SC, PingFang SC;">
						企业地址
					</view>
					<view>
						<input type="text" placeholder="请填写" class="input" v-model="formData.enterprise_addr"
							placeholder-class="plasty" />
					</view>
				</view>
				<view class="line-row"></view>
				<view style="display: flex;align-items: center;justify-content: space-between;">
					<view style="color: #3D3D3D;font-weight: 400;font-family: PingFang SC, PingFang SC;">
						经营/热爱的户外运动
					</view>
				</view>
				<view class="textarea_mph" style="margin-top: 30rpx;">
					<u--textarea maxlength="400" placeholder-class="bttops" v-model="formData.outdoor_sport"
						placeholder="请输入您经营热爱的户外运动"></u--textarea>
				</view>
				<view class="btn_1" @click="submitDo()">确认提交</view>
			</view>
		</u-popup>
		<tab-bar :tabBarShow="0"></tab-bar>
	</view>
</template>

<script>
	//	import MySwiper from "@/components/fuyu-MixSwiper/fuyu-MixSwiper.vue";
	import {
		dateWeek
	} from '../../utils/dateFormat'
	export default {
		components: {
			//MySwiper,
		},
		data() {
			return {
				homrS: false,
				swiperCurrent: 0, //轮播下标
				showPopbq: false, //标签弹窗
				bqList: [], //标签列表
				cate_ids: '', //标签id
				loadStatus: 'nomore',
				swiperList: [],
				teacherList: [],
				wqList: [],
				hotList: [],
				unread_number: "",
				keywords: "",
				count: 1,
				current: 0,
				autoplay: true,
				scrollTop: 0,
				dotsShow: true,
				videoList: [],
				currentItemType: "",
				tjShow: true,
				videoAutoplay: false,
				list: [{
						name: "全部",
						id: 0
					},
					{
						name: "夜校",
						id: 1
					}
				],
				limit: 6,
				page: 1,
				show: false,
				formShow: false,
				formData: {
					name: '',
					mobile: '',
					enterprise_name: '',
					enterprise_addr: '',
					outdoor_sport: '',
				}
			};
		},
		onLoad() {
			this.getinit();
			this.resetLists();
			this.getHotList();
			this.getBqList();
			this.getitembq();
			// uni.hideTabBar();

		},
		mounted() {
			this.videoContext = uni.createVideoContext("myVideo"); //创建视频实例指向video
		},
		onShow() {

		},
		beforeDestroy() {
			this.autoplay = false;
		},
		computed: {
			// limitedList() {
			//   return this.items.slice(0, 3);
			// }
		},
		methods: {
			onScrolltolower() {
				if (this.hotList.length < this.count) {
					this.page += 1;
					this.getHotList();
				}
			},
			onS() {
				this.homrS = true;
				uni.showLoading({
					title: '加载中...'
				});
				this.resetLists();
				this.getHotList();
				setTimeout(() => {
					this.homrS = false;
					uni.hideLoading();
					uni.stopPullDownRefresh();
				}, 2000)
			},
			submitDo() {
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.showToast({
						title: '请登录',
						icon: 'none',
						duration: 2000,
						complete: function() {
							setTimeout(function() {
								uni.switchTab({
									url: '/pages/my/index',
								});
							}, 2000);
						}
					});
					return;
				}

				if (this.formData.name == '') {
					uni.showToast({
						title: '请填写联系人！',
						icon: "none",
						duration: 2000,
					});
					return;
				}
				if (this.formData.mobile == '') {
					uni.showToast({
						title: '请填写联系电话！',
						icon: "none",
						duration: 2000,
					});
					return;
				}
				if (this.formData.enterprise_name == '') {
					uni.showToast({
						title: '请填写企业名称！',
						icon: "none",
						duration: 2000,
					});
					return;
				}
				if (this.formData.enterprise_addr == '') {
					uni.showToast({
						title: '请填写企业地址！',
						icon: "none",
						duration: 2000,
					});
					return;
				}
				if (this.formData.outdoor_sport == '') {
					uni.showToast({
						title: '请填写经营/热爱的户外运动！',
						icon: "none",
						duration: 2000,
					});
					return;
				}
				uni.$u.http
					.post("/api/school.spor_apply/add", this.formData)
					.then((res) => {
						console.log(res);
						if (res.code == 1) {
							uni.showToast({
								title: '提交成功！',
								icon: "none",
								duration: 2000,
							});
							this.formShow = false;
							this.formData = {
								name: '',
								mobile: '',
								enterprise_name: '',
								enterprise_addr: '',
								outdoor_sport: '',
							};
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},
			openSwiper(item) {
				console.log(item);
				if(item.type == 'out') {
					uni.navigateTo({
						url: '/packageB/outWeb?url=' + item.url
					})
				}else {
					if (item.url == 'outdoor_sport_apply') {
						const token = uni.getStorageSync('token');
						if (!token) {
							uni.showToast({
								title: '请登录',
								icon: 'none',
								duration: 2000,
								complete: function() {
									setTimeout(function() {
										uni.switchTab({
											url: '/pages/my/index',
										});
									}, 2000);
								}
							});
							return;
						} else {
							this.formShow = true;
						}
					} else {
						uni.navigateTo({
							url: item.url
						})
					}
				}
				
			},
			swiperChange(e) {
				if (e.detail.source == 'autoplay' || e.detail.source == 'touch') {
					this.swiperCurrent = e.detail.current;
				}
			},
			//跳转夜校
			toshool() {
				uni.$u.http
					.get("/api/user/auto_login_token", {
						encryption: 1
					})
					.then((res) => {
						if (res.code == 1) {
							if (res.data.token == null && res.data.time == null && res.data.encryption_data == '') {
								uni.showToast({
									title: '请登录',
									icon: 'none',
									duration: 2000,
									complete: function() {
										setTimeout(function() {
											uni.switchTab({
												url: '/pages/my/index',
											});
										}, 2000);
									}
								});
							} else {
								wx.openEmbeddedMiniProgram({
									appId: 'wx867e324c44b9e016',
									path: 'pages/index/index',
									extraData: res.data,
									envVersion: 'trial',
									success(res) {
										// 打开成功
										console.log('成功！', res)
									}
								})
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});

						}
					})
					.catch((error) => {

						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});

			},
			// 获取本地标签列表
			getitembq() {
				if (uni.getStorageSync("bqlist") != "") {
					this.list = JSON.parse(uni.getStorageSync("bqlist"));
				} else {
					uni.setStorageSync('bqlist', JSON.stringify(this.list))
				}
			},
			dateWeeks(e) {
				return dateWeek(e);
			},
			closebq() {
				this.showPopbq = false
			},
			openBq() {
				this.showPopbq = true
			},
			// 获取胶囊高度
			// capsuleHeight() {
			//   if (uni?.getMenuButtonBoundingClientRect()) {
			//     return `${
			//       uni.getMenuButtonBoundingClientRect().height * 2 +
			//       uni.getMenuButtonBoundingClientRect().top * 2 +
			//       26
			//     }rpx`;
			//   } else {
			//     return `180rpx`; 
			//   }
			// },
			//底部翻页
			// onReachBottom() {
			// 	if (this.hotList.length < this.count) {
			// 		this.page++;
			// 		this.getHotList();
			// 	}
			// },
			// 重置列表
			resetLists() {
				this.page = 1;
				this.hotList = [];
				this.loadStatus = "loading";
			},
			// 标签点击
			handleClick(index, val) {
				this.current = index;
				this.cate_ids = val;
				this.hotList = [];
				this.page = 1;
				this.limit = 6;
				this.getHotList();
			},
			// 搜索
			search() {
				if (this.keywords == "") {
					this.tjShow = true;
				} else {
					this.tjShow = false;
				}

				this.resetLists();
				this.getHotList();
			},

			//轮播图跳转
			click(e) {
				uni.navigateTo({
					url: e,
				});
			},

			// 获取首页信息
			getinit() {
				uni.$u.http
					.get("/api/index/init", {})
					.then((res) => {
						if (res.code == 1) {
							this.swiperList = res.data.home_data.top_images.list;
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},

			// 获取标签
			getBqList() {

				uni.$u.http
					.get("/api/school.new_activity/cate_list", {
						params: {
							page: 1,
							limit: 100
						},
					})
					.then((res) => {
						if (res.code == 1) {
							this.bqList = res.data.list;
							//初始化默认标签
							// if (this.list.length < 3) {
							//   this.list = [...this.list, ...res.data.list.slice(0,2)];
							// }
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none",
								duration: 2000,
							});
						}
					})
					.catch((error) => {
						uni.showToast({
							title: "请求失败，请稍后再试",
							icon: "none",
							duration: 2000,
						});
					});
			},
			//添加标签（本地）
			addbq(val) {
				let arrbql = uni.getStorageSync("bqlist");
				let arrbq = JSON.parse(arrbql);
				let isbq = true;
				for (let i = 0; i < arrbq.length; i++) {
					if (arrbq[i].id == val.id) {
						uni.showToast({
							title: "已添加该标签",
							icon: "none",
							duration: 2000,
						});
						return isbq = false;
					}
				}
				if (this.list.length < 9) {
					if (isbq) {
						this.list.push(val);
						uni.setStorageSync('bqlist', JSON.stringify(this.list))
					}
				} else {
					uni.showToast({
						title: "最多添加9个标签",
						icon: "none",
						duration: 2000,
					});
				}
			},
			//删除标签（本地）
			removebq(i) {
				this.list.splice(i, 1);
				uni.setStorageSync('bqlist', JSON.stringify(this.list))
			},

			// 获取热门活动 和 课程 
			getHotList() {
				let words = this.keywords;
				let cate_ids = this.cate_ids;
				if (cate_ids == 1) { //夜校

					uni.$u.http.get('https://testy.hschool.com.cn/api/school/classes/classes_list', {
						params: {
							keywords: this.keywords,
							page: this.page,
							limit: this.limit,
							is_expire: 2,
							order: 'hot',
							has_shop: 1
						}
					}).then(res => {
						if (res.code == 1) {
							this.count = res.data.list.total;
							this.hotList = [...this.hotList, ...res.data.list.data];
							if (this.hotList.length >= res.data.list.total) {
								this.loadStatus = 'nomore';
							} else {
								this.loadStatus = 'loading';
							}
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
							this.loadStatus = 'loading';
						}
					}).catch(error => {
						console.error('请求失败', error);
						this.loadStatus = 'loading';
					});

				} else {
					//活动
					uni.$u.http.get("/api/school.new_activity/activity_list", {
							params: {
								keywords: words,
								cate_ids: cate_ids,
								page: this.page,
								limit: this.limit,
								order: "normal",
								status: "2,3,4,5", //5 已结束
								auth_status: 1 //审核通过的
							},
						})
						.then((res) => {
							if (res.code == 1) {
								this.count = res.data.count;
								this.hotList = [...this.hotList, ...res.data.list.data];
								if (this.hotList.length >= res.data.count) {
									this.loadStatus = 'nomore';
								} else {
									this.loadStatus = 'loading';
								}

							} else {
								uni.showToast({
									title: res.msg,
									icon: "none",
									duration: 2000,
								});
							}
						})
						.catch((error) => {
							uni.showToast({
								title: "请求失败，请稍后再试",
								icon: "none",
								duration: 2000,
							});
						});
				}

			},
			// 跳转详情
			detail(id) {
				if (this.cate_ids == 1) { //夜校
					uni.$u.http
						.get("/api/user/auto_login_token", {
							encryption: 1
						})
						.then((res) => {
							if (res.code == 1) {
								if (res.data.token == null && res.data.time == null && res.data.encryption_data ==
									'') {
									uni.showToast({
										title: '请登录',
										icon: 'none',
										duration: 2000,
										complete: function() {
											setTimeout(function() {
												uni.switchTab({
													url: '/pages/my/index',
												});
											}, 2000);
										}
									});
								} else {
									wx.navigateToMiniProgram({
										appId: 'wx867e324c44b9e016',
										path: 'pages/center/detail?id=' + id,
										extraData: res.data,
										envVersion: 'trial',
										success(res) {
											// 打开成功
											console.log('成功！', res)
										}
									})
								}
							} else {
								uni.showToast({
									title: res.msg,
									icon: "none",
									duration: 2000,
								});

							}
						})
						.catch((error) => {

							uni.showToast({
								title: "请求失败，请稍后再试",
								icon: "none",
								duration: 2000,
							});
						});

				} else {

					uni.navigateTo({
						url: "/packageA/center/detail?id=" + id,
					});
				}
			},
			// 跳转往期详情
			Wqdetail(id) {
				uni.navigateTo({
					url: "/packageA/index/previousDetail?id=" + id,
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.justify-center {
		justify-content: center;
	}

	.space-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.mar-top-30 {
		margin-top: 30rpx;
	}

	.tab_all {
		position: relative;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.allbq {
			width: 88rpx;
			height: 70rpx;
			background: #f7f7f7;
			position: absolute;
			display: flex;
			justify-content: center;
			align-items: center;
			right: 0;

			.imgs_all {
				width: 74rpx;
				height: 68rpx;
			}
		}

		.tabs {
			display: flex;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
			margin-right: 60rpx;

			/* 优化滚动效果 */
			.tab-item {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 0 0 auto;
				text-align: center;
				margin-right: 20rpx;
				position: relative;
				font-family: "YouSheBiaoTiHei";
				font-weight: 400;
				font-size: 34rpx;
				color: #9c9c9c;
				// width: 200rpx;
				padding: 0 45rpx;
				height: 70rpx;
				line-height: 70rpx;
				background: linear-gradient(180deg, #ffffff 44%, #f7f7f7 100%);
				border-radius: 48rpx 48rpx 48rpx 48rpx;

				.imgs_bq {
					width: 32rpx;
					height: 32rpx;
					margin-right: 10rpx;
				}
			}

			.tab-item.active {
				background: linear-gradient(360deg,
						rgba(196, 249, 119, 0) 0%,
						#bbfc5b 100%);
				font-size: 34rpx;
				color: #323232;
			}
		}
	}

	.recharge {
		width: 750rpx;
		background-color: #f7f7f7;
		background-image: url("https://naweigetetest2.hschool.com.cn/dyqc/bgx2.png");
		background-size: 100%;

		.group {
			width: 690rpx;
			height: 152rpx;
			position: relative;

			.g-left {
				margin-left: 176rpx;

				span {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-size: 36rpx;
					color: #242623;
				}
			}

			.g-center {
				width: 176rpx;
				height: 64rpx;
				margin-left: 56rpx;
				background: #ffffff;
				border-radius: 40rpx 40rpx 40rpx 40rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 28rpx;
				color: #222222;
			}

			.g-right {
				position: absolute;
				width: 50rpx;
				height: 50rpx;
				top: 0;
				right: 0;
			}
		}

		.box {
			width: 690rpx;
			margin-top: 30rpx;

			.logo_css {
				width: 430rpx;
				height: 80rpx;
				// #ifdef MP-WEIXIN
				margin-top: 65rpx;
				// #endif
			}

			.serch_top {
				display: flex;

				.serch_top1 {
					margin-top: 10rpx;
					margin-bottom: 30rpx;
					position: relative;
					width: 530rpx;

					.searchBtn {
						position: absolute;
						width: 133rpx;
						height: 100%;
						background: #323232;
						border-radius: 40rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 26rpx;
						color: #FFFFFF;
						display: flex;
						justify-content: center;
						align-items: center;
						top: 0;
						right: 0;
					}
				}

				.imgstop_ye {
					// margin-left: 30rpx;
					width: 176rpx;
					position: absolute;
					right: -30rpx;
				}
			}

			.hot {
				//   margin-top: 30rpx;
				margin-bottom: 50rpx;

			}
		}

		.content {
			// height: 462rpx;
			// overflow-x: auto;
			/* 允许横向滚动 */
			// white-space: nowrap;

			width: 690rpx;

			.bbb {
				background: #ffffff;
				border-radius: 36rpx;
				margin-bottom: 30rpx;
			}

			.right {
				margin-left: 30rpx;
				width: 100%;

				.title {
					width: 93%;
					overflow: hidden;
					text-overflow: ellipsis;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					font-size: 30rpx;
					color: #323232;
				}

				.first-image {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #9C9C9C;

					span {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 26rpx;
						color: #222222;
					}
				}

				.row {
					margin: 10rpx 0 26rpx 0;

					.remen {
						// width: 136rpx;
						height: 40rpx;
						background: #ebfaf5;
						border-radius: 4rpx 4rpx 4rpx 4rpx;

						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 24rpx;
						color: #14bc84;
						line-height: 28rpx;
						padding: 2rpx 8rpx;
					}

					.line-colum {
						width: 1rpx;
						height: 32rpx;
						background: #7a7a7a;
						margin: 0 13rpx;
					}

					.name {
						width: 110rpx;
						height: 32rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 26rpx;
						color: #7a7a7a;
						line-height: 32rpx;
						margin-left: 12rpx;
					}
				}


				.jigou {
					width: 312rpx;
					// height: 32rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 26rpx;
					color: #7a7a7a;
					margin: 4rpx 0 14rpx 0;
				}

				.time {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #323232;

					.time_tex {
						width: 116rpx;
						height: 42rpx;
						background: #BBFC5B;
						border-radius: 159rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #323232;
						display: flex;
						justify-content: center;
						align-items: center;
						line-height: 30rpx;
					}

					.time_texs {
						background: #FF4810;
						color: #ffffff;
					}

				}

				.imgs_con_div {
					margin-right: 12rpx;
					width: 202rpx;
					height: 202rpx;
					overflow: hidden;

					&:first-child {
						border-radius: 18rpx 0 0 18rpx;
					}

					&:nth-child(3) {
						border-radius: 0 18rpx 18rpx 0;
					}

					.imgs_con {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}


			}

			.bottom {
				margin: 0 0 32rpx 24rpx;
				width: 655rpx;

				.toptext {
					width: 480rpx;

					.smalld {
						margin-left: 0 !important;
					}
				}

				.number {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 26rpx;
					color: #3D3D3D;
					line-height: 30rpx;
					width: 242rpx;
					margin-left: 20rpx;
				}

				.text {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 26rpx;
					color: #7a7a7a;
				}

				.money {
					width: 150rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 36rpx;
					color: #ff2323;
					justify-content: flex-end;
				}
			}

			.ovr {
				width: 312rpx;
				background: #ffffff;
				border-radius: 20rpx 20rpx 20rpx 20rpx;
				margin-top: 18rpx;
				margin-right: 24rpx;
			}
		}

		.content-footer {
			width: 690rpx;
			justify-content: space-between;
			margin-top: 42rpx;

			.line-left,
			.line-right {
				width: 221rpx;
				height: 1rpx;
				background: #7a7a7a;
			}

			.line-text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 32rpx;
				color: #ff4023;
				margin-right: 8rpx;
			}
		}

		.con-center {
			width: 690rpx;
			height: 260rpx;
			background: #ffffff;
			border-radius: 16rpx;
			margin-top: 30rpx;

			.mask {
				margin: 32rpx;
			}


		}
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}










	.pos {
		position: relative;

		.ab {
			position: absolute;
			right: 0%;
			bottom: 0%;
		}

		.m {
			width: 28rpx;
			height: 42rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 28rpx;
			color: #ff4810;
		}

		.money {
			width: 68rpx;
			font-family: D-DIN-PRO, D-DIN-PRO;
			font-weight: bold;
			font-size: 36rpx;
			color: #ff4810;
		}

		.no {
			width: 64rpx;
			height: 48rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: bold;
			font-size: 32rpx;
			color: #ff4810;
			line-height: 38rpx;
		}
	}

	.part {
		width: 162rpx;
		height: 70rpx;
		background: url(@/static/index/shangche.png);
		background-size: 162rpx 70rpx;
		background-repeat: no-repeat;
		font-family: 'YouSheBiaoTiHei';
		font-weight: 400;
		font-size: 42rpx;
		color: #BBFC5B;
	}

	.part1 {
		width: 162rpx;
		height: 70rpx;
		background: url(@/static/index/shangchew.png);
		background-size: 162rpx 70rpx;
		background-repeat: no-repeat;
		font-family: 'YouSheBiaoTiHei';
		font-weight: 400;
		font-size: 42rpx;
		color: #9C9C9C;
	}

	.swiper-box {
		height: 580rpx;
		position: relative;
	}

	.popup_bq {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 1040rpx;
		position: relative;

		img {
			position: absolute;
			width: 750rpx;
			height: 1040rpx;
			top: -164rpx;
			z-index: 0;
		}

		.bqlist {
			margin-top: 70rpx;
			z-index: 1;
			margin-left: 80rpx;

			.allmybqs {
				flex-wrap: wrap;
				width: 720rpx;
				margin-top: 20rpx;
			}

			.allbqs {
				display: flex;
				overflow-y: scroll;
				flex-wrap: wrap;
				height: 840rpx;
				width: 692rpx;
				margin-top: 20rpx;
			}

			.bqpiece {
				width: 210rpx;
				height: 70rpx;
				background: #F7F7F7;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				justify-content: space-around;
				align-items: center;
				margin: 20rpx 20rpx 20rpx 0;

				span {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 26rpx;
					color: #323232;
					display: flex;
					align-items: center;
				}
			}
		}


	}
</style>
<style lang="scss">
	.serch_top {
		.u-search {

			::v-deep .u-search__content {
				background: rgba($color: #FFFFFF, $alpha: 0.6) !important;
			}

			::v-deep .u-search__content__input {
				background-color: rgba($color: #FFFFFF, $alpha: 0) !important;
			}

			::v-deep .u-icon__img {
				width: 36rpx !important;
				height: 36rpx !important;
			}
		}
	}

	.swiper_s {
		width: 100%;
		height: 100%;
	}


	.swiper-image {
		width: 100%;
		height: 100%;
		border-radius: 36rpx;
		object-fit: contain;
	}

	.text {
		position: absolute;
		/* 确保文字在模糊背景之上 */
		z-index: 200;
		/* 提高z-index以确保文字位于最上层 */
		font-family: "YouSheBiaoTiHei";
		font-weight: 400;
		font-size: 38rpx;
		color: #ffffff;
		text-align: center;
		width: 100%;
		bottom: 70rpx;
		transform: translateZ(0);
	}

	.fnon_tit {
		position: absolute;
		left: 5%;
		z-index: 1;
		width: 630rpx;
		height: 117rpx;
		line-height: 100rpx;
		background: rgba(0, 0, 0, 0.24);
		backdrop-filter: blur(4px);
		border-radius: 36rpx;
		bottom: 30rpx;
	}

	// .fnon_tit::before  {
	// 	content: "";
	// 	position: absolute;
	// 	top: 0;
	// 	left: 0;
	// 	right: 0;
	// 	bottom: 0;
	// 	backdrop-filter: blur(4px);
	// 	/* 调整模糊程度 */
	// 	pointer-events: none;
	// 	/* 确保模糊效果不会干扰点击事件 */
	// 	border-radius: 36rpx;
	// 	z-index: 0;
	// 	/* 确保伪元素在背景和文字之间 */
	// }

	.dots {
		position: absolute;
		bottom: 50rpx;
		width: 100%;
		display: flex;
		justify-content: center;

		.dot {
			background-color: #ffffff;
			width: 30rpx;
			height: 6rpx;
			border-radius: 0;
			margin-right: 10rpx;

			&.active {
				background: #0DAE11;
				// transform: scale(0.9);
			}
		}
	}

	.line-row {
		margin: 40rpx 0rpx;
		height: 1rpx;
		width: 100%;
		background: #eeeeee;
	}

	.input {
		text-align: right;
		font-family: PingFang SC, PingFang SC;
		font-size: 26rpx;
		color: #343434;
		line-height: 32rpx;
	}

	.plasty {
		color: #999999;
		font-weight: 300;
		font-size: 28rpx;
	}

	.textarea_mph {
		::v-deep .u-textarea {
			height: 237rpx;
			padding: 20rpx;
			border: none;
			font-size: 26rpx;
			color: #9C9C9C;
			background-color: #F8F8F8 !important;
			border-radius: 18rpx;
		}
	}

	.btn_1 {
		width: 100%;
		height: 90rpx;
		background: #323232;
		border-radius: 198rpx 198rpx 198rpx 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 400;
		font-size: 32rpx;
		color: #BBFC5B;
		line-height: 90rpx;
		text-align: center;
		margin-top: 50rpx;
	}
</style>