<template>
	<view class="page-total">
		<view :class="isOverall ? 'tab-list_ip' : 'tab-list' ">
			<view class="list" v-for="(item,index) in TabBarList" @click="onTabBar(item,index)" :key="index">
				<view v-if="item.index!=2">
					<image :src="item.acImg" mode="widthFix" v-show="tabBarShow === index"></image>
					<image :src="item.img" mode="widthFix" v-show="tabBarShow != index"></image>
					<!-- background: (index == 2) ?  'red' : '' -->
					<view :class="(tabBarShow === index) ? 'action' :'no'">{{item.name}}</view>
				</view>
				<view v-if="item.index==2" style="text-align: center;">
					<image :src="item.img" mode="widthFix" 
						style="border-radius: 50%;width: 102rpx;height: 102rpx;"></image>
					<!-- background: (index == 2) ?  'red' : '' -->
					<!-- <view :class="(tabBarShow === index) ? 'action' :'no'" style="height: 64rpx;line-height: 64rpx;">
						{{item.name}}</view> -->
				</view>
			</view>
			<view v-if="isOverall==1" style="height: 100rpx;position: fixed;bottom: 0;width: 100%;background-color: #ffffff;"></view>
			<!-- <span class="badge" v-if="number != 0">
				<u-badge type="warning " max="99" :value="number"></u-badge>
			</span> -->
		</view>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	export default {
		computed: {
			...mapGetters(['number']),
		},
		data() {
			return {
				TabBarList: [],
				TabBarList1: [{
						index: 0,
						name: '首页',
						img: '/static/index.png',
						acImg: '/static/index1.png',
					},
					{
						index: 1,
						name: '分类',
						img: '/static/fenl.png',
						acImg: '/static/fenl1.png',
					},
					{
						index: 2,
						name: '发布',
						img: '/static/home_add.png',
						acImg: '/static/home_add.png',
					},
					{
						index: 3,
						name: '消息',
						img: '/static/activity.png',
						acImg: '/static/activity1.png',
					},
					{
						index: 4,
						name: '我的',
						img: '/static/my.png',
						acImg: '/static/my1.png',
					},

				],
				TabBarList2: [{
						index: 0,
						name: '首页',
						img: '/static/index.png',
						acImg: '/static/index1.png',
					},
					{
						index: 1,
						name: '消息',
						img: '/static/activity.png',
						acImg: '/static/activity1.png',
					},
					{
						index: 3,
						name: '我的',
						img: '/static/my.png',
						acImg: '/static/my1.png',
					},

				],
				codeheight: 0,
				isOverall: 0,
				phoneModel: '',
				initFb: ''

			};
		},
		props: {
			tabBarShow: {
				type: Number,
				default: 0,
			},

		},

		mounted() {
			this.getinit();
			try {
				const res = uni.getSystemInfoSync();
				let that = this;
				// 获取系统信息
				uni.getSystemInfo({
					success(res) {
						// console.log(res.brand) //手机牌子
						// console.log(res.model) //手机型号
						// console.log(res.screenWidth) //屏幕宽度
						// console.log(res.screenHeight) //屏幕高度
						that.codeheight = Math.round(res.screenHeight);
						that.phoneModel = res.model;

						if (/iPad|iPhone|iPod/.test(res.model)) {
							that.isOverall = 1;
						}

						// else if (Math.round(res.screenHeight) > 740) {
						// 	that.isOverall = 1;
						// }
					}
				});
			} catch (e) {
				// error
			}
		},
		watch: {
			number(newVal) {
				this.TabBarList[3].unread_number = newVal;
			},
			deep: true

		},
		methods: {
			getinit() {
				uni.$u.http.get('/api/index/init', {}).then(res => {
					if (res.code == 1) {
						this.initFb = res.data.base_info.miniapp_activity_swtich;
						if (res.data.base_info.miniapp_activity_swtich == 1) {
							this.TabBarList = this.TabBarList1;
						} else {
							this.TabBarList = this.TabBarList2;
						}
					}
				}).catch(error => {
					console.log(error);
				});
			},
			/**
			 * @param {Object} item
			 * @param {Number} index
			 */
			onTabBar(item, index) {
				this.$emit('tabBarShow', index);
				console.log('index', index);
				if (this.initFb == 1) {

					switch (index) {
						case 0:
							uni.switchTab({
								url: '/pages/index/index'
							})
							break;
						case 1:
							uni.switchTab({
								url: '/pages/fenl/index'
							})
							break;
						case 2:
							uni.switchTab({
								url: '/pages/center/index'
							})
							break;
						case 3:
							uni.switchTab({
								url: '/pages/activity/index'
							})
							break;
						case 4:
							uni.switchTab({
								url: '/pages/my/index'
							})
							break;
						default:
							uni.switchTab({
								url: '/pages/index/index'
							})
					}

				} else {

					switch (index) {
						case 0:
							uni.switchTab({
								url: '/pages/index/index'
							})
							break;
						case 1:
							uni.switchTab({
								url: '/pages/activity/index'
							})
							break;
						case 2:
							uni.switchTab({
								url: '/pages/my/index'
							})
							break;
						default:
							uni.switchTab({
								url: '/pages/index/index'
							})
					}
				}

			},

		}
	}
</script>

<style scoped lang="scss">
	/* 主要颜色 */
	$base: #323232; // 基础颜色

	.page-total {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		z-index: 10;
	}

	.tab-list {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 111rpx;
		padding-bottom: 0;
		background: url('/static/bottom_tab.png') no-repeat;
		background-size: 100%;
		box-sizing: border-box;

		.list {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 200rpx;
			height: 130rpx;
			position: relative;
			z-index: 1000;

			image {
				width: 44rpx;
				height: 44rpx;
				background-color: white;
				margin-top: 12rpx;
			}




		}
	}

	.no {
		color: #C1C1C1;
		font-size: 20rpx;
	}

	.action {
		color: $base;
		font-weight: 400;
		font-size: 20rpx;
	}

	.tab-list_ip {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 160rpx;
		padding-bottom: 50rpx;
		background: url('/static/bottom_tab.png') no-repeat;
		background-size: 100%;
		box-sizing: border-box;
		z-index: 100;

		// box-shadow: 0rpx -1rpx 0rpx 0rpx #EEEEEE;
		.list {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 200rpx;
			height: 125rpx;
			position: relative;
			z-index: 1000;

			image {
				width: 44rpx;
				height: 44rpx;
				background-color: white;
				margin-top: 12rpx;
			}

			text {
				color: #C1C1C1;
				font-size: 20rpx;
				margin-top: 14rpx;
			}

			.action {
				color: $base;
				font-weight: 400;
				font-size: 20rpx;
			}
		}
	}

	.badge {
		position: absolute;
		right: 3%;
		top: 0;
	}
</style>