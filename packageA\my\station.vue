<template>
	<view class="box flex flex-column align-items">
		<view class="header flex align-items w-100" style="justify-content: space-between;">
			<span class="s-header flex justify-center align-items" v-for="(item, index) in headerList" :key="index"
				:class="{ selected: headerSelected(item.status) }" @click="selectHeader(item.status)">
				{{ item.text }}
				<span class="numberPos">
					<u-badge type="warning" numberType="ellipsis" max="99" :value="item.number"></u-badge>
				</span>
			</span>
		</view>
		<view class="yidu">
			<span class="news"
				v-if="selected == 'system,classes,order,activity'">{{ unread_number }}条未读消息,共{{ News.count }}条消息</span>
			<span class="news" v-if="selected == 'system'">{{ unread_system_number }}条未读消息,共{{ News.count }}条消息</span>
			<span class="news" v-if="selected == 'classes'">{{ unread_classes_number }}条未读消息,共{{ News.count }}条消息</span>
			<span class="news" v-if="selected == 'order'">{{ unread_order_number }}条未读消息,共{{ News.count }}条消息</span>
			<span class="One" @click="one">一键已读</span>
		</view>
		<view class="flex flex-column align-items" style="width: 750rpx;background: #F1F2F8;min-height: 87vh;">
			<view class="center flex flex-column align-items">
				<span class="hui" v-if="unread.length != 0">未读消息</span>
				<view class="list flex align-items" v-for="(item,index) in unread" :key="index" @click="detail(item.id,item.params)">
					<span class="left">
						<image :src="item.messageevent.logo_image" mode="" style="width: 90rpx;height: 90rpx;"></image>
					</span>
					<span class="right flex flex-column align-items">
						<span class="flex flex-start">
							<span class="white-space title" style="width: 350rpx;">{{item.title}}</span>
							<span class="white-space time" style="width: 148rpx;">{{item.createtime_text}}</span>
						</span>
						<span class="white-space text">{{item.desc}}</span>
			
					</span>
					<span class="jiao">
						<u-badge :isDot="true" type="success"></u-badge>
					</span>
				</view>
			</view>
			<view class="center flex flex-column align-items">
				<span class="hui" v-if="read.length != 0">今天</span>
				<view class="list flex align-items" v-for="(item,index) in read" :key="index" @click="detail(item.id,item.params)">
					<span class="left">
						<image :src="item.messageevent.logo_image" mode="" style="width: 90rpx;height: 90rpx;"></image>
					</span>
					<span class="right flex flex-column align-items">
						<span class="flex flex-start">
							<span class="white-space title" style="width: 350rpx;">{{item.title}}</span>
							<span class="white-space time" style="width: 148rpx;">{{item.createtime_text}}</span>
						</span>
						<span class="white-space text">{{item.desc}}</span>
			
					</span>
				</view>
			</view>
			<view class="center flex flex-column align-items">
				<span class="hui" v-if="Earlier.length != 0">以前</span>
				<view class="list flex align-items" v-for="(item,index) in Earlier" :key="index" @click="detail(item.id,item.params)">
					<span class="left">
						<image :src="item.messageevent.logo_image" mode="" style="width: 90rpx;height: 90rpx;"></image>
					</span>
					<span class="right flex flex-column align-items">
						<span class="flex flex-start">
							<span class="white-space title" style="width: 350rpx;">{{item.title}}</span>
							<span class="white-space time" style="width: 148rpx;">{{item.createtime_text}}</span>
						</span>
						<span class="white-space text">{{item.desc}}</span>
					</span>
				</view>
			</view>
			<u-loadmore :status="loadStatus" />
		</view>
		
	</view>
</template>

<script>
	import { mapActions } from 'vuex';
	export default {
		 ...mapActions(['number']),
		data() {
			return {
				isFetching: false,
				selected: 'system,classes,order,activity',
				News: {},
				unread: [], // 未读
				read: [], // 今天
				Earlier: [], // 以前
				headerList: [{
						status: 'system,classes,order,activity',
						text: '全部消息',
						number: 10
					},
					{
						status: 'system',
						text: '系统消息',
						number: 10
					},
					{
						status: 'classes',
						text: '课程消息',
						number: 10
					},
					{
						status: 'order',
						text: '订单消息',
						number: 10
					}
				],
				unread_number: '',
				unread_system_number: '',
				unread_classes_number: '',
				unread_order_number: '',
				keywords: '',
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				status: '',
				mini_type: '',
				type: '',
				list: [],
				show: false,
				id: '',
				title: '标题',
				content: 'uView的目标是成为uni-app生态最优秀的UI框架'
			};
		},
		onShow() {
			this.resetLists();
			this.number()
			this.getToal()
			this.fetchMessages(this.selected);
		},
		
		onReachBottom() {
			this.page++;
			this.fetchMessages(this.selected);
		},
		methods: {
			// 获取时间
			getFormattedDates() {
				const today = new Date();
				const startOfDay = new Date(today);
				startOfDay.setHours(0, 0, 0, 0);

				const endOfDay = new Date(today);
				endOfDay.setHours(23, 59, 59, 999);

				const yesterday = new Date(today);
				yesterday.setDate(today.getDate() - 1);
				const startOfYesterday = new Date(1970, 0, 1, 0, 0, 0, 0); // 1970/01/01 00:00:00
				const endOfYesterday = new Date(yesterday);
				endOfYesterday.setHours(23, 59, 59, 999);

				const formatDate = (date) => {
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');

					return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
				};

				return {
					today: `${formatDate(startOfDay)}-${formatDate(endOfDay)}`,
					yesterday: `${formatDate(startOfYesterday)}-${formatDate(endOfYesterday)}`
				};
			},
			selectHeader(status) {
				this.selected = status;
				this.page = 1;
				this.resetLists();
				this.getToal()
				this.fetchMessages(status); // 调用 fetchMessages 方法
			},
			headerSelected(status) {
				return this.selected === status;
			},
			// 获取总条数
			getToal(){
				uni.$u.http.get('/api/school/message/message_list', {
					params: {
						keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status: this.selected,
						mini_type: '',
					},
				}).then(res => {
					if (res.code == 1) {
						this.News = res.data;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			fetchMessages(status) {
				const dates = this.getFormattedDates();
				this.getList(status, 'unread', '1', '');
				this.getList(status, 'read', '2', dates.today);
				this.getList(status, 'Earlier', '2', dates.yesterday);
			},
			// 消息详情
			detail(id,params){
				const message = JSON.parse(params);
				if (message.event == 'user_auth_need_after'){
					uni.navigateTo({
						url:"/packageA/my/settleIn?shop_id=" + message.shop_id + "&id=" + id
					})
				} else {
					uni.navigateTo({
						url:"/packageA/my/stationDetail?id=" + id
					})
				}
			},
		
			// 未读消息
			number() {
				uni.$u.http.get('/api/school/message/message_count').then(res => {
					if (res.code == 1) {
						this.$store.commit('setNumber',res.data.unread_number);
						this.headerList[0].number = res.data.unread_number;
						this.headerList[1].number = res.data.unread_system_number;
						this.headerList[2].number = res.data.unread_classes_number;
						this.headerList[3].number = res.data.unread_order_number;

						this.unread_number = res.data.unread_number;
						this.unread_system_number = res.data.unread_system_number;
						this.unread_classes_number = res.data.unread_classes_number;
						this.unread_order_number = res.data.unread_order_number;
						uni.setStorageSync('unread_number', res.data.unread_number);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			confirm() {
				this.resetLists();
				this.number();
				this.fetchMessages('system,classes,order');
				this.show = false;
			},
			// 一键已读
			one() {
				uni.$u.http.get('/api/school/message/batch', {
					params: {
						id: '*',
						status: this.selected,
						mini_type: '',
						event: ''
					}
				}).then(res => {
					if (res.code == 1) {
						this.resetLists();
						this.number();
						this.fetchMessages('system,classes,order');
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			// 获取消息列表
			async getList(status, zt, type, time) {
				uni.$u.http.get('/api/school/message/message_list', {
					params: {
						keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status: status,
						mini_type: '',
						type: type,
						time: time
					},
				}).then(res => {
					if (res.code == 1) {
						// this.News = res.data;
						this.updateList(zt, res.data.list, res.data.count);
						
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});


			},
			// 更新列表
			updateList(type, list, count) {
				console.log(type, list, count)
				const targetList = this[type];
				this[type] = [...targetList, ...list];
				console.log('this.loadStatus ',this.loadStatus )
				this.loadStatus = targetList.length >= count ? 'nomore' : 'loading';
			},
			// 重置列表
			resetLists() {
				this.page = 1;
				this.unread = [];
				this.read = [];
				this.Earlier = [];
				this.loadStatus = 'loading'
			}
		}
	};
</script>

<style lang="scss" scoped>
	.box {
		background: #ffffff;
	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.header {
		width: 690rpx;
		height: 50rpx;
		margin-top: 37rpx;

		.s-header {
			width: 152rpx;
			height: 50rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #9E9E9E;
			position: relative;

			.numberPos {
				// width: 45rpx;
				// height: 50rpx;
				position: absolute;
				right: -10rpx;
				top: -4px;
			}
		}

		.s-header.selected {
			width: 152rpx;
			height: 50rpx;
			background: #008CFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 26rpx;
		}
	}

	.yidu {
		border-top: #008CFF 1rpx solid;
		width: 690rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 66rpx;
		margin-top: 18rpx;

		.news {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #9E9E9E;
		}

		.One {
			width: 132rpx;
			height: 40rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 1rpx solid #9E9E9E;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #9E9E9E;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.center {
		width: 690rpx;
		background: #F1F2F8;
		.list {
			width: 100%;
			height: 130rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			margin-top: 32rpx;
			position: relative;

			.left {
				font-size: 24rpx;
				font-weight: bold;
				padding: 20rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;

			}

			.right {
				margin-left: 24rpx;

				.title {
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
				}

				.time {
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 18rpx;
					color: #7A7A7A;
					margin-left: 16rpx;
				}

				.text {
					width: 528rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;

				}
			}

			.jiao {
				position: absolute;
				left: 0;
				top: 0;
			}
		}
	}

	.hui {
		width: 132rpx;
		height: 40rpx;
		background: #E7E7E7;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #9E9E9E;
		margin: 24rpx 0;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>