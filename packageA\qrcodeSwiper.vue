<template>
	<view class="carousel-container">
		<!-- 轮播主体 -->
		<swiper class="swiper-box" :current="currentIndex" @change="onSwiperChange" circular
			:style="{height: swiperHeight + 'rpx'}">
			<swiper-item v-for="(item, index) in images" :key="index" class="swiper-item">
				<view
					style="border: 2rpx solid #9C9C9C;width: 280rpx;height: 280rpx;margin: 0 auto;border-radius: 8rpx;">
					<image :src="item.codeimage" class="swiper-image" mode="widthFix" @load="onImageLoad" />
				</view>
				<!-- border: 1px solid #9C9C9C; width: 284rpx;height: 284rpx;margin: 0 auto;
					border-radius: 16rpx;position: absolute;top: 8rpx;left: 1px;right: 0;-->
				<view v-if="item.status==6" style="">
					<image style="width: 286rpx;height: 289rpx;margin: 0 auto;
					position: absolute;top: 7rpx;left: 0px;right: 0;box-sizing: border-box;"
						src="/static/qrcode_yes.png" mode="" />
				</view>
				<view style="text-align: center;font-size: 28rpx;margin-top: 15rpx;font-weight: 600;">{{item.name}}
				</view>
				<view style="text-align: center;font-size: 28rpx;margin-top: 10rpx;font-weight: 600;">{{item.idnum}}
				</view>
			</swiper-item>
		</swiper>

		<!-- 控制按钮 -->
		<view class="nav-buttons">
			<view class="btn prev" @click="switchSlide(-1)">
				<image style="width: 50rpx;height: 50rpx;" src="/static/detail/left.png"></image>
			</view>
			<view class="btn next" @click="switchSlide(1)">
				<image style="width: 50rpx;height: 50rpx;" src="/static/detail/right.png"></image>
			</view>
		</view>

		<!-- 自定义指示器 -->
		<!-- <view class="dots">
        <text 
          v-for="(dot, idx) in images.length" 
          :key="idx" 
          :class="['dot', { active: currentIndex === idx }]"
        />
      </view> -->
	</view>
</template>

<script>
	export default {
		props: {
			images: {
				type: Array,
				default: () => []
			} // 接收外部图片数组
		},
		data() {
			return {
				currentIndex: 0,
				swiperHeight: 400 // 默认高度
			};
		},
		methods: {
			// 滑动切换回调
			onSwiperChange(e) {
				this.currentIndex = e.detail.current;
				this.$emit('onSwiperChanges', e.detail.current);
			},
			// 按钮切换逻辑
			switchSlide(step) {
				const total = this.images.length;
				let newIndex = this.currentIndex + step;

				if (newIndex < 0) newIndex = total - 1; // 循环向前
				else if (newIndex >= total) newIndex = 0; // 循环向后

				this.currentIndex = newIndex;
			},
			onImageLoad(e) {
				// 动态计算图片高度
				const {
					width,
					height
				} = e.detail;
				const ratio = height / width;
				const calculatedHeight = 280 * ratio; // 280rpx是固定宽度
				if (calculatedHeight > this.swiperHeight) {
					this.swiperHeight = calculatedHeight + 60; // 60rpx为文字区域预留空间
				}
			}

		}
	};
</script>
<style lang="scss" scoped>
	.carousel-container {
		position: relative;
		width: 100%;
		margin: 0 auto;
	}

	.swiper-box {
		width: 100%;
	}

	.swiper-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100%;
		box-sizing: border-box;
		padding: 20rpx 0;
	}

	.qrcode-container {
		position: relative;
		width: 280rpx;
		height: 280rpx;
		border: 1px solid #9C9C9C;
		border-radius: 8rpx;
		margin: 0 auto;
		overflow: hidden;
	}

	.swiper-image {
		width: 100%;
		height: 100%;
		display: block;
		box-sizing: border-box;
	}

	.used-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;

		image {
			width: 80%;
			height: 80%;
		}
	}

	.qrcode-name {
		text-align: center;
		font-size: 28rpx;
		margin-top: 15rpx;
		font-weight: 600;
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.nav-buttons {
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		transform: translateY(-50%);
		padding-left: 80rpx;
		padding-right: 80rpx;
		display: flex;
		justify-content: space-between;
		pointer-events: none; // 防止按钮阻挡滑动

		.btn {
			pointer-events: auto;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 50rpx;
				height: 50rpx;
			}
		}
	}


	// .carousel-container {
	// 	position: relative;
	// 	height: 350rpx;
	// }

	// .swiper-box {
	// 	width: 100%;
	// 	height: 100%;
	// }

	// .swiper-item {
	// 	display: block;
	// 	text-align: center;
	// 	align-items: center;
	// 	justify-content: center;
	// 	padding: 3rpx;
	// }

	// .swiper-image {
	// 	width: 100%;
	// 	height: 100%;
	// 	border-radius: 8rpx;

	// 	/* 内边距为边框宽度 */
	// 	box-sizing: border-box;
	// 	/* 内边距和边框计入总尺寸 */
	// }

	// .nav-buttons {
	// 	position: absolute;
	// 	top: 50%;
	// 	transform: translateY(-50%);
	// 	width: 100%;
	// 	display: flex;
	// 	justify-content: space-between;

	// 	.btn {
	// 		width: 60rpx;
	// 		height: 60rpx;
	// 		// border-radius: 50%;
	// 		// background: rgba(0,0,0,0.3);
	// 		color: white;
	// 		display: flex;
	// 		align-items: center;
	// 		justify-content: center;
	// 	}
	// }

	// .dots {
	// 	position: absolute;
	// 	bottom: 20rpx;
	// 	width: 100%;
	// 	display: flex;
	// 	justify-content: center;

	// 	.dot {
	// 		width: 12rpx;
	// 		height: 12rpx;
	// 		border-radius: 50%;
	// 		background: rgba(255, 255, 255, 0.5);
	// 		margin: 0 8rpx;

	// 		&.active {
	// 			background: #007AFF;
	// 			transform: scale(1.2);
	// 		}
	// 	}
	// }
</style>