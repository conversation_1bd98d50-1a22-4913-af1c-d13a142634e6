<template>
	<view class="box flex">
		<view class="con-center flex flex-column justify-center align-items">
			
			<view class="tabs">
				<u-tabs :activeStyle="{
			    color: '#303133',
			    fontWeight: 'bold',fontSize: '32rpx'}" :inactiveStyle="{
			    color: '#606266',fontSize: '32rpx'
			}" :list="headers" lineColor="#323232" @click="selectheader"></u-tabs>
			</view>
			
			<!-- <view class="tabs align-items">
				<span class="s-header flex flex-column" v-for="(item, index) in headers" :key="index"
					:class="{ selected: headerSelected(item.status) }" @click="selectheader(item.status)">
					{{ item.text }}
					<span class="lines" :class="{ selected: headerSelected(item.status) }"></span>
				</span>
			</view> -->
			

			<view style="min-height: 100vh;margin-top: 80rpx;">

				<view class="flex hdkuai flex-column" v-for="(item, index) in list" :key="index">
					<view style="margin:30rpx;">
						<span class="text_h">活动时间：
							{{ dateWeeks(item.detail.start_time) + '-'+dateWeekends(item.detail.end_time)}}</span>
					</view>
					<span class="line"></span>
					<view style="margin:30rpx;" class="flex" @click="toDetail(item.id, item.order_no, item.status)">
						<image class="tut" :src="item.detail.images[0]" mode="aspectFill"></image>
						<view style="margin-left: 20rpx;width: 80%;">
							<view>
								<span style="font-weight: 600;">{{ item.detail.title }}</span>
							</view>
							<view style="margin-top: 30rpx;" class="flex flex-between align-items">
								<span style="font-size: 32rpx;">￥{{ item.totalprice }}</span>
								<span style="font-size: 26rpx;color: #9C9C9C;">购买数量：{{ item.num }}</span>
							</view>
						</view>
					</view>
					<span class="line"></span>
					<view class="flex foot_btn align-items space-between">
						<view>
							<span v-if="item.status == 9">已完成</span>
							<span v-if="item.status == 0" style="color: #FF4810;display: flex;align-items: center;">
								支付倒计时：<u-count-down :time="item.cancel_last_seconds * 1000"></u-count-down>
							</span>
							<span v-if="item.status == 2 || item.status == 3">待核销</span>
							<span v-if="item.status == -3" style="color: #9C9C9C;">已关闭</span>
							<span v-if="item.status == 4 || item.status == 7" style="color: #FF4810;">售后中</span>
							<span v-if="item.status == 5" style="color: #FF4810;">退款中</span>
							<span v-if="item.status == 6" style="color: #FF4810;">已退款</span>
						</view>

						<view class="flex align-items" style="justify-content: flex-end;">
							<view v-if="item.status == 0 && item.cancel_last_seconds > 0" class="btn_xq"
								@click="toPay(item.id, item.order_no)">去支付</view>
							<view v-if="item.status == 2 || item.status == 3" class="btnhx"
								@click="toDetail(item.id, item.order_no, item.status)">核销码</view>
							<!-- <view v-if="item.status == 9 && item.invoice_status == 0 && item.detail.feel == 0" class="btn_Inv"
								@click="toAddInvoic(item.id, item.order_no)">申请开票</view>
							<view v-if="item.status == 9 && item.invoice_status == 1 && item.detail.feel == 0" class="btn_InvIng">开票中</view>
							<view v-if="item.status == 9 && item.invoice_status == 2 && item.detail.feel == 0" class="btn_Inv"
								@click="checkInvoice(item.user_invoice_apply_id, item.order_no)">查看发票</view> -->
							<view v-if="item.status != 0" class="btn_xq"
								@click="toDetail(item.id, item.order_no, item.status)">查看详情</view>
						</view>
					</view>
				</view>

				<view class="flex flex-column flex-start align-items" v-if="list.length == 0"
					style="margin-top: 300rpx;">
					<image src="/static/message/activen.png" mode="" style="width: 180rpx;height: 180rpx;">
					</image>
					<view style="margin-top: 30rpx;font-size: 28rpx;color: #323232;">暂无参与活动</view>
					<view class="tofb" @click="tofb">参与活动</view>
				</view>
			</view>




		</view>
	</view>
</template>

<script>
	import {
		dateWeek,
		dateWeekend
	} from "../../utils/dateFormat";
	export default {
		data() {
			return {
				popupStyle: {
					width: '690rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					padding: '0',
					borderRadius: '20rpx'
				},
				sortList: [{
					text: '按照价格排序',
					value: ['acs', 'desc'],
				}, {
					text: '按照编号排序',
					value: ['acs', 'desc'],
				}],
				size: 13,
				sortStyle: ['#ff557f', '#3f3f3f'],
				headers: [{
						status: '-3,0,2,3,4,5,6,7,9',
						name: '全部'
					},
					{
						status: '0',
						name: '待支付'
					},
					{
						status: '2,3',
						name: '待核销'
					},
					{
						status: '9',
						name: '已完成'
					},
					{
						status: '-3',
						name: '已关闭'
					},
					{
						status: '4,5,6,7',
						name: '退款/售后'
					}
				],
				selected: '0',
				page: 1,
				limit: 10,
				loadStatus: 'loading',
				keywords: '',
				list: [],
				show: false,
				qrcode: '',
				count: 0,
				option: {},
				isInitialized: false,
			};
		},
		onLoad(option) {
			this.isInitialized = true; // 标记已初始化 
			console.log(1);
			if (option?.status) {
				this.selected = option.status
				this.getList(option.status);
			} else {
				this.getList('-3,0,2,3,4,5,6,7,9');
			}

		},
		onShow() {
			console.log(2);
			if (this.isInitialized) return; // 拦截未初始化时的逻辑 
			setTimeout(() => {
				this.list = []
				this.page = 1
				this.getList(this.selected);
			}, 500)
		},
		onPullDownRefresh() {
			uni.showLoading({
				title: '加载中...'
			});
			this.page = 1;
			this.list = [];
			this.getList(this.selected);
			setTimeout(() => {
				uni.hideLoading();
				uni.stopPullDownRefresh();
			}, 2000)
		},
		onReachBottom() {
			if (this.list.length < this.count) {
				this.page++;
				this.getList(this.selected);
			}

		},

		methods: {
			tofb() {
				uni.switchTab({
					url: '/pages/index/index',
				});
			},
			dateWeeks(e) {
				return dateWeek(e);
			},
			//结束
			dateWeekends(e) {
				return dateWeekend(e);
			},
			formatTimestamp(timestamp) {
				const date = new Date(timestamp * 1000); // 10位时间戳需要乘以1000
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			},
			// 关闭弹窗
			close() {
				this.show = false
				// this.qrcode = ''
			},
			open() {
				this.show = true
			},
			againBuy(id) {
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id
				})
			},
			// 取消支付
			Cancel(order_no) {
				console.log(order_no)
				uni.$u.http.post('/api/school/order/cancel', {
					order_no: order_no
				}).then(res => {
					if (res.code == 1) {
						this.list = []
						this.getList(0);
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
			},
			
			toInfo(id) {
				//123
				uni.navigateTo({
					url: "/packageA/center/detail?id=" + id
				})
			},
			//申请发票
			toAddInvoic(id, order_no) {
				const num = 1
				uni.navigateTo({
					url: '/packageB/invoice/addInvoice?id=' + id + "&order_nos=" + order_no + "&num=" + num
				})
			},
			//查看发票
			checkInvoice(id, order_no) {
				uni.navigateTo({
					url: '/packageB/invoice/invoiceInfo?id=' + id + "&order_no=" + order_no
				})
			},
			//查看详情
			toDetail(id, order_no, status) {
				if (status == 2 || status == 3) {
					uni.navigateTo({
						url: "/packageA/my/pendinPayDetail?id=" + id + "&order_no=" + order_no
					})
				} else {
					uni.navigateTo({
						url: "/packageA/my/pendinPayStatus?id=" + id + "&status=" + status
					})
				}


			},
			// 去支付
			toPay(id, order_no) {
				// uni.navigateTo({
				// 	url: "/packageA/my/pendinPay?id=" + id + "&order_no=" + order_no
				// })
				uni.navigateTo({
					url: "/packageA/my/pendinPayStatus?id=" + id + "&status=" + status
				})
			},
			// 售后
			service(id) {
				uni.navigateTo({
					url: "/packageA/afterSales/index?id=" + id
				})
			},
			// 核销码
			QR(id) {
				uni.$u.http.get('/api/school/order/detail', {
					params: {
						id: id
					},
				}).then(res => {
					if (res.code == 1) {
						this.qrcode = res.data.detail

					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {});
				this.show = true
			},
			switchSort(index, value) {
				console.log(index, value);
			},
			// 搜索
			search() {
				const that = this
				that.page = 1
				that.list = []
				this.getList(this.selected)
			},
			// 获取我参与的列表
			getList(status) {
				// if (this.loadStatus === 'nomore') return;
				uni.$u.http.get('/api/school.newactivity.order/order_list', {
					params: {
						// keywords: this.keywords,
						page: this.page,
						limit: this.limit,
						status: status,
					}
				}).then(res => {
					if (res.code == 1) {
						this.count = res.data.count
						this.list = [...this.list, ...res.data.list];
						if (this.list.length >= res.data.count) {
							this.loadStatus = 'nomore';
						} else {
							this.loadStatus = 'loading';
						}
						this.isInitialized = false; // 标记已初始化 
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
						this.loadStatus = 'loading';
					}
				}).catch(error => {
					console.error('请求失败', error);
					this.loadStatus = 'loading';
				});
			},
			selectheader(status) {
				const that = this;
				that.selected = status.status;
				console.log(status, '')
				that.page = 1
				that.list = []
				that.getList(status.status)
			},
			headerSelected(status) {
				return this.selected === status;
			},

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: #f5f5f5;

		.con-center {
			width: 100%;
			height: 100%;
			// margin-top: 25rpx;
			// overflow: hidden;
			
			.tabs {
				background: #ffffff;
				font-family: PingFang SC, PingFang SC;
				position: fixed;
				top: 0;
				z-index: 1000;
			}

			// .tabs {
			// 	background: #ffffff;
			// 	width: 750rpx;
			// 	display: flex;
			// 	overflow-x: auto;
			// 	// margin-top: 10rpx;
			// 	-webkit-overflow-scrolling: touch;
			// 	font-family: PingFang SC, PingFang SC;
			// 	position: fixed;
			// 	top: 0;
			// 	padding-bottom: 10rpx;

			// 	.s-header {
			// 		display: flex;
			// 		justify-content: center;
			// 		align-items: center;
			// 		width: 150rpx;
			// 		height: 80rpx;
			// 		font-weight: 400;
			// 		font-size: 28rpx;
			// 		color: #9E9E9E;
			// 		line-height: 26rpx;
			// 		flex: 0 0 auto;
			// 		position: relative;
			// 	}

			// 	.s-header.selected {
			// 		width: 150rpx;
			// 		height: 80rpx;
			// 		background: #ffffff;
			// 		font-weight: 800;
			// 		font-size: 32rpx;
			// 		color: #323232;
			// 		line-height: 28rpx;
			// 	}

			// 	.lines {
			// 		position: absolute;
			// 		bottom: 0;
			// 		width: 44rpx;
			// 	}

			// 	.lines.selected {
			// 		border-bottom: #323232 solid 8rpx;
			// 		border-radius: 4rpx;
			// 	}
			// }

			.hdkuai {
				background: #FFFFFF;
				// height: 360rpx;
				// justify-content: space-around;
				margin: 30rpx;
				border-radius: 18rpx;

				.text_h {
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					font-size: 28rpx;
					color: #9C9C9C;
					line-height: 39rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;

				}

				.tut {
					width: 130rpx;
					height: 130rpx;
					border-radius: 8rpx;
				}

				.foot_btn {
					margin: 30rpx;
					justify-content: space-between;
					font-size: 28rpx;
					color: #323232;
					font-weight: 400;
					box-sizing: border-box;

					.btn_xq {
						width: 180rpx;
						height: 70rpx;
						line-height: 70rpx;
						text-align: center;
						background: linear-gradient(270deg, #FBF66D 0%, #9CEAA2 100%);
						border-radius: 276rpx 276rpx 276rpx 276rpx;

					}

					.btn_Inv {
						width: 180rpx;
						height: 70rpx;
						line-height: 70rpx;
						text-align: center;
						color: #323232;
						border: 2rpx solid #323232;
						border-radius: 276rpx 276rpx 276rpx 276rpx;
						margin-right: 10rpx;
					}
					

					.btn_InvIng {
						width: 180rpx;
						height: 70rpx;
						line-height: 70rpx;
						text-align: center;
						color: #999999;
						// border: 2rpx solid #999999;
						background-color: #f0f0f0;
						border-radius: 276rpx 276rpx 276rpx 276rpx;
						margin-right: 10rpx;
					}

					.btnhx {
						width: 180rpx;
						height: 70rpx;
						line-height: 70rpx;
						background: #323232;
						text-align: center;
						border-radius: 276rpx 276rpx 276rpx 276rpx;
						border: 1rpx solid #000000;
						color: #BBFC5B;
						margin-right: 20rpx;
					}

					.u-count-down__text {
						color: #FF4810 !important;
					}


				}
			}
		}

	}

	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.flex-between {
		justify-content: space-between;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line {
		width: 630rpx;
		height: 1rpx;
		background: #F0F0F0;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		margin: 0 30rpx;
	}

	.centerBox {
		width: 690rpx;

		.box-line {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			margin-top: 24rpx;
		}

		.center {
			margin: 32rpx 0 32rpx 0;
		}

		.rightBox {
			margin-left: 32rpx;
			width: 50%;

			.line-row {
				width: 382rpx;
				height: 1rpx;
				background: #D9D9D9;
				box-shadow: 1rpx 1rpx 0rpx 0rpx rgba(102, 102, 102, 0.25);
				margin: 14rpx 0 6rpx 0;
			}

			.name {
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #343434;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				/* 设置行数 */
				overflow: hidden;
				text-overflow: ellipsis;
				word-break: break-all;
				margin-bottom: 24rpx;
				/* 防止单词被截断 */
			}

			.minge {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #7A7A7A;
				margin-bottom: 8rpx;
			}

			.money {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #FF2323;

				span {
					font-weight: 500;
					font-size: 24rpx;
					color: #7A7A7A;
				}
			}

			.Cancel {
				width: 138rpx;
				height: 48rpx;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				border: 2rpx solid #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #008CFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
			}

			.make {
				width: 138rpx;
				height: 48rpx;
				background: #008CFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
				letter-spacing: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-left: 24rpx;
			}

			.QR {
				width: 138rpx;
				height: 48rpx;
				background: #4974FF;
				box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #EAEAEA;
				display: flex;
				align-items: center;
				justify-content: center;
				align-self: flex-end;
				margin-top: 50rpx;
			}

		}
	}

	.charge {
		font-family: PingFang SC, PingFang SC;
		font-weight: 800;
		font-size: 28rpx;
		color: #FF2323;
		line-height: 32rpx;
	}

	.search {
		margin-top: 24rpx;
		width: 690rpx;
		height: 64rpx;
		background: #FFFFFF;
		box-shadow: 2rpx 2rpx 0rpx 0rpx rgba(0, 0, 0, 0.4);
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		border: 2rpx solid #008CFF;

		.dashed {
			image {
				width: 52rpx;
				height: 52rpx;
			}
		}

		.line-search {
			width: 2rpx;
			height: 42rpx;
			background: #008CFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
		}

		.input {
			// border: 4rpx solid #EAEAEA;
			padding-left: 12rpx;
			height: 100%;
			width: 100%;
		}

		::v-deep .input-placeholder {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #C0C0C0;
			line-height: 32rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

		}

		.searchBtn {
			width: 128rpx;
			height: 64rpx;
			background: #008CFF;
			border-radius: 5rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}

	.popup {
		.header {
			margin-left: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 800;
			font-size: 42rpx;
			color: #008CFF;
			margin-top: 34rpx;
			width: 690rpx;
		}

		.line-row {
			width: 690rpx;
			height: 1rpx;
			background: #D9D9D9;
			margin: 11rpx 0 31rpx 0;
		}

		.pop-center {
			margin-top: 50rpx;
			align-items: center;
			width: 650rpx;

			.left {
				image {
					border-radius: 12rpx;
				}
			}

			.right {
				margin-left: 30rpx;

				.title {
					width: 340rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: 800;
					font-size: 34rpx;
					color: #343434;
				}


			}
		}

		.line-short {
			width: 400rpx;
			height: 1rpx;
			background: #D9D9D9;
			margin: 24rpx 0;
		}

		.popList {
			justify-content: space-between;
			width: 600rpx;
			margin-top: 32rpx;

			.hei {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 26rpx;
				color: #343434;
			}
		}

		.pop-btn {
			width: 690rpx;
			margin-top: 62rpx;
			justify-content: space-around;

			.Cancel {
				width: 306rpx;
				height: 80rpx;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				border: 2rpx solid #008CFF;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #008CFF;
			}

			.Confirm {
				width: 306rpx;
				height: 80rpx;
				background: #008CFF;
				border-radius: 401rpx 401rpx 401rpx 401rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 800;
				font-size: 34rpx;
				color: #FFFFFF;
			}
		}
	}

	.hui {
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #7A7A7A;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
	}

	.tofb {
		margin-top: 50rpx;
		width: 280rpx;
		height: 70rpx;
		background: #323232;
		border-radius: 198rpx;
		font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
		font-weight: 500;
		font-size: 32rpx;
		color: #BBFC5B;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
<style lang="scss">
	.foot_btn {

		// #ifdef MP-WEIXIN
		.u-count-down__text {
			color: #FF4810 !important;
		}

		// #endif
	}
</style>