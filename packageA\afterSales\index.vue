<template>
	<view class="detail_all">
		<!-- <view class="nav">
			<u-navbar :is-back="true" leftIconColor="#FFFFFF" :autoBack='true' title="" :bgColor="background"
				:titleStyle='titleStyle'> </u-navbar>
		</view> -->
		<view class="box flex justify-start flex-column align-items" >
		<!-- <view class="swiper">
			<u-swiper :list="detail.images" indicator indicatorActiveColor="#323232" indicatorMode="dot" :indicator-style="{ bottom: '60rpx',zIndex: 999}"
			 height="580rpx" circular></u-swiper>
		</view> -->
		
		<view class="con-center w-100 flex justify-center flex-column align-items flex-start">
			

			<view class="first-box flex flex-start flex-column justify-start">

				<view class="flex flex-column">
					<view class="flex">
						<image v-if="detail.images" :src="detail.images[0]" mode="" style="width: 159rpx;height: 159rpx;border-radius: 18rpx;"></image>
						<view class="flex align-items flex-column" style="margin-left: 20rpx;">
							<span class="first-name white-space" style="font-weight: 600;">{{detail.title}}</span>
							<view class="rbot flex align-items">
								<image src="/static/center/address.png" mode="" style="width: 32rpx;height: 32rpx;margin-right: 20rpx;"></image>
								<span class="white-space">{{detail.address}}{{detail.address_detail}}</span>
							</view>
						</view>
					</view>
				</view>
		
				<span class="line"></span>
				<span class="flex align-items" style="width: 100%;justify-content: space-between;">
					<view class="tt1">选择退款原因</view>
					<span class="row-right" @click="opentuikuan">
						<input type="text" placeholder="请选择" class="input" disabled="true" v-model="reasons"
							placeholder-class="plasty" />
							<u-icon name="arrow-right" color="#323232"></u-icon>
					</span>
				</span>
				<span class="line"></span>
				<span class="flex align-items" style="width: 100%;justify-content: space-between;">
					<view class="tt1">选择退款数量<span> (已核销{{detailAny.verification.have_number}}张)</span></view>
					<span class="flex align-items">
						<image style="width: 42rpx;height: 42rpx;" src="/static/detail/jian.png" @click="removeMon"></image>
						<span style="margin: 0 20rpx;">{{ nummoney }}</span>
						<image style="width: 42rpx;height: 42rpx;" src="/static/detail/jia.png" @click="addMon"></image>
					</span>
				</span>
				<span class="line"></span>
				<span class="flex align-items" style="width: 100%;justify-content: space-between;">
					<view class="tt1">支付金额</view>
					<span style="color: #323232;font-weight: 900;">￥{{ detailAny.payprice  }}</span>
				</span>
				<span class="line"></span>
				<span class="flex align-items" style="width: 100%;justify-content: space-between;margin-bottom: 40rpx;">
					<view class="tt1">预计退款金额</view>
					<span style="color: #FF4810;font-weight: 900;">￥{{ priceGem  }}</span>
				</span>

			</view>


		</view>

		<view class="third flex flex-column" style="justify-content: flex-start;" v-if="reasons == '其他'"> 
			<view class="third-top flex align-items">
				<span>退款说明</span>
			</view>
			<view class="flex align-items textarea_fb" >
				<u--textarea v-model="ccontent"  maxlength="400" placeholder="请输入退款说明"
				height="120" placeholder-class="bttop"></u--textarea>
			</view>
		</view>


		
		<view @click="subumit()" class="footer flex align-items" style="justify-content: space-between;">
				<view class="footer-right flex justify-center align-items">
					<span>确定</span>
				</view>
		</view>


		<!-- 退款政策 -->
		<u-popup @touchmove.native.stop.prevent  :show="tuikuan" :round="22" mode="bottom" @close="closetuikuan"
			@open="opentuikuan" :custom-style="popupStyletk">
			<view class="popup_tkall">
				<view class="popup_tk"> 选择退款原因</view>
				<scroll-view  scroll-y="true" class="popup-content">
					<view class="popup-content-item flex align-items" 
					v-for="(item,index) in activityInfo.refund_reason_data" 
					:key="index" @click="selectItem(index,item)">
						<view style="width: 540rpx;"> 
							<span class="popup-content-item-title">{{ item }}</span>
						</view>
						<image v-if="currents === index" src="@/static/fabu/check.png" mode="widthFix" style="width: 44rpx;height: 44rpx;" />
						<image v-else src="@/static/fabu/nocheck.png" mode="widthFix" style="width: 44rpx;height: 44rpx;" />
					</view>
				
				</scroll-view>
				<view class="footer flex align-items" style="justify-content: space-between;">
					<view  @click="closetuikuan" class="footer-right flex justify-center align-items">
						<span>确定</span>
					</view>
			</view>
			</view>
		</u-popup>


	</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import {
		dateWeek,
		dateWeekend
	} from '../../utils/dateFormat'
	export default {
		computed: {
			// formattedTitle() {
			// 	if (this.detail.title.length > 9) {
			// 		return this.detail.title.slice(0, 9) + '..';
			// 	}
			// 	return this.detail.title;
			// },

			fomartertime() {
				return dayjs(this.detail.last_time*1000).format('YYYY-MM-DD HH:mm:ss');
			},
			formattedTime() {
				const startTime = dateWeek(this.detail.start_time);
				const endTime = dateWeekend(this.detail.end_time);
				return {
					formattedTime: `${startTime} - ${endTime}`
				};
			}
		},

		data() {
			return {
				popupStyletk: {
					width: '710rpx',
					padding: '24rpx 24rpx 42rpx 24rpx',
					height: '800rpx',
					margin: '0 auto', // 水平居中
					display: 'flex',
					justifyContent: 'start',
					alignItems: 'center',
					flexColumn: 'column'
				},
				style: {
					// 字符串的形式
					img: 'width: 100%'
				},
				nummoney:1,
				ccontent:'',//退款说明
				reasons:'', //退款原因
				priceGem:null,
				currents:null,
				tuikuan:false,
				activityInfo: {},
				path: 'https://testy.hschool.com.cn//uploads/20241219/3406baf51fcc28c63c31ebcee5c9c75e.jpg',
				uloadingShow: false,
				show: false,
				shows:false,
				buyShow: false,
				type: 0, // 0 支付 1 立即购买 2 预约  3确认时间
				id: 1,
				count: 5,
				value: 5,
				order_no: '',
				PayPirce: 0,
				detail: {},
				detailAny:{},
				people: {},
				qrUrl: '',
				is_collect: 0,


				timeList: [],
				selectedTime: null,
				// indexBackgroundImage: indexBackgroundImage,
				orderId: "",
				classes_lib_spec_id: '',
				order_no2: '',
				mobile: '',
				is_show_model: false, //是否显示分享模态窗
				background: '#ffffff00',
				titleStyle: {
					color: '#FFFFFF'
				},
			};
		},



		onLoad(options) {
			this.id = options.id
			this.getDetail()
		},

		methods: {
			//选择原因
			selectItem(index,item) {
				this.currents = index;
				this.reasons = item;
				if(index==5){
					this.tuikuan = false
				}
			},
			closetuikuan() {
				this.tuikuan = false
			},
			opentuikuan(){
				this.tuikuan = true;
				this.reasons = '';
			},
			//确定
			subumit(){
				if (this.reasons == '') {
					uni.showToast({
						title: '请选择退款原因',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				if (this.nummoney > this.detailAny.verification.total_number) {
					uni.showToast({
						title: '退款数量不能超过已购买数量',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				
				let reasons = ''
				if(this.reasons == '其他'){
					
					if (this.ccontent == '') {
						uni.showToast({
							title: '请输入退款说明',
							icon: 'none',
							duration: 2000
						})
						return;
					}
					reasons = this.ccontent;
				}else{
					reasons = this.reasons;
				}
				
				uni.$u.http.post('/api/school.newactivity.order/after_sales', {
					order_no: this.order_no,
					auth_reason: reasons,
					num: this.nummoney,
				}).then(res => {
					if (res.code == 1) {
						uni.showToast({
							title: '申请成功',
							icon: 'success',
							duration: 2000,
							complete: function() {
								setTimeout(function() {
									uni.redirectTo({
										url: "/packageA/my/exercise?status=4,5,6,7"
									})
								}, 2000);
							}
						});	
					}else{
						uni.showToast({
							title: res.msg,
							icon: 'none',
						})
					}
				})
			},
			addMon(){
				if (this.nummoney < this.detailAny.verification.total_number) {
					this.nummoney++;
					this.priceGem = Number(this.detail.price)*Number(this.nummoney)+".00";
				}else{
					uni.showToast({
						title: '不能超过已购数量',
						icon: 'none',
						duration: 2000
					})
					return;

				}
			},
			removeMon(){
				if (this.nummoney > 1) {
					this.nummoney--;
					this.priceGem = Number(this.detail.price)*Number(this.nummoney)+".00";
				}
			},
			// 获取订单详情
			getDetail() {
				uni.$u.http.get('/api/school.newactivity.order/detail', {
					params: {
						id: this.id,
					}
				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail.detail;
						this.detailAny = res.data.detail;
						this.order_no = res.data.detail.order_no;
						this.priceGem = res.data.detail.detail.price;
						console.log(res.data.detail);
						this.activityInfo = uni.getStorageSync('init_info')
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
					}
				}).catch(error => {});
			},


			// 返回首页
			toIndex() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},

			//分享发布
			sharePoster() { //分享图片给好友按钮的点击事件函数
				let that = this
				this.base64ToFilePath(this.path, (filePath) => {
					console.log(filePath);
					wx.showShareImageMenu({ //分享给朋友
						path: filePath,
						success: (res) => {
							console.log("分享成功：", res);
						},
						fail: (err) => {
							console.log("分享取消：", err);
						},
					})
				})
			},


			base64ToFilePath(base64data, callback) {
				const time = new Date().getTime();
				const imgPath = `${wx.env.USER_DATA_PATH}/addFriends${time}share_qrcode.png`;
				const imageData = base64data.replace(/^data:image\/\w+;base64,/, "");
				const fileSystemManager = uni.getFileSystemManager();

				fileSystemManager.writeFile({
					filePath: imgPath,
					data: imageData,
					encoding: 'base64',
					success: () => {
						callback(imgPath);
					},
					fail: (err) => {
						console.error('Write file failed:', err);
						uni.showToast({
							title: '写入文件失败',
							icon: 'none'
						});
					}
				});
			},


			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},

			// 取消选择
			cancel() {
				this.selectedTime = null
			},
			selectTime(time) {
				this.selectedTime = time;
				this.classes_lib_spec_id = time.id

			},
			timeSelected(time) {
				return this.selectedTime === time;
			},
			moveScroll() {},
		},
	}
</script>

<style lang="scss" scoped>
	.detail_all{
		background-color: #f7f7f7;
		min-height: 100vh;
	}
	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.white-space {
		overflow: hidden;
		/* 确保超出容器的文本被隐藏 */
		white-space: nowrap;
		/* 确保文本在一行内显示 */
		text-overflow: ellipsis;
		/* 使用省略号表示被截断的文本 */
		width: 100%;
	}

	.con-center {
		background: white;
		border-radius:0 0 44rpx 44rpx;
		position: relative;
	}
	
	

	.space-between {
		justify-content: space-between;
	}

	.swiper {
		width: 100%;
		height: 580rpx;
	}

	.box {
		position: relative;
	}

	.topimgs_rmb{
		position: absolute;
		top: -168rpx;
		right: 30rpx;
		width: 201rpx;
		height: 118rpx;
		z-index: 0;
	}
	.topimgs{
		position: absolute;
		top: -120rpx;
		width: 100%;
		z-index: 0;
	}
	.top_texts{
		position: absolute;
		top: -156rpx;
		right: 68rpx;
		z-index: 3;
		color: #ffffff;
		.xiao{
			margin-left: 4rpx;
			font-size: 22rpx;
			font-weight: 400;
		}
	}
	.first-box {
		width: 690rpx;
		background: #FFFFFF;
		// background: url('@/static/detail/conbg.png');
		// padding-left: 24rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
		// z-index: 1;
		.sigh {
			width: 88rpx;
			height: 40rpx;
			background: #BEEE03;
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 24rpx;
			color: #222222;
		}
		.first-name{
			width: 430rpx;
			// height: 77rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 28rpx;
			color: #323232;
			// margin-left: 16rpx;
		}
		.rbot{
			width: 430rpx;
			height: 52rpx;
			font-size: 26rpx;
			color: #323232;
			margin-top: 60rpx;
		}


		.first-mine {
			height: 32rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;

		}

		.first-txt {
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			font-size: 24rpx;
			color: #7A7A7A;
			line-height: 32rpx;
			margin: 0 6rpx;
		}
		.tt1{
			font-size: 28rpx;
			span{
				padding-left: 15rpx;
				font-size: 22rpx;
				color: #9C9C9C;
			}
		}

		.row-right {
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}
		::v-deep.plasty {
			text-align: right;
			font-family: PingFang SC, PingFang SC;
			font-size: 28rpx;
			color: #999999;
		}
		.input {
			text-align: right;
			color: #999999;
			font-size: 28rpx;
		}

	}


	.third {
		width: 100%;
		background: #ffffff;
		margin-top:20rpx;
		border-radius: 44rpx;
		.third-top{
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #323232;
			line-height: 50rpx;
			margin-top: 30rpx;
			margin-left:30rpx;
			position: relative;
			z-index: 10;
		}
		span{
			position: relative;
			.icons{
				width: 37rpx;
				height: 20rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				z-index: -1;
			}
		}
		.textarea_fb{
			margin:0 auto;
			padding: 30rpx;
			width: 92%;
			::v-deep .u-textarea {
				height: 100%;
				padding: 0;
				border: none;
				font-size: 26rpx;
				color: #9C9C9C;
				background: #F7F7F7;
				border-radius: 18rpx;
			}
			::v-deep .u-textarea__field{
				padding: 30rpx;
			}
			::v-deep .bttop {
				color: #999999 ;
			}
		}



	}

	.line {
		width: 690rpx;
		height: 1rpx;
		background: #F0F0F0;
		margin: 30rpx 0;
	}




	.footer {
		width: 100%;
		height: 200rpx;
		background: #ffffff;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
		position: fixed;
		z-index: 99;
		/* 绝对定位 */
		bottom: 0;
		/* 定位在底部 */
		left: 0;
		/* 定位在左边 */


		.footer-right {
			position: absolute;
			right: 30rpx;
			width: 690rpx;
			height: 92rpx;
			background: #C1C1C1;
			border-radius: 200rpx;

			span {
				width: 690rpx;
				height: 92rpx;
				line-height: 92rpx;
				font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
				font-weight: 400;
				font-size: 36rpx;
				color: #BBFC5B;
				background: #323232;
				border-radius: 200rpx;
				text-align: center;
			}
		}
	}


	::v-deep ::-webkit-scrollbar {
		/*滚动条整体样式*/
		width: 4px !important;
		height: 1px !important;
		overflow: auto !important;
		background: #ccc !important;
		-webkit-appearance: auto !important;
		display: block;
	}

	::v-deep ::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius: 10px !important;
		box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		background: #7b7979 !important;
	}

	::v-deep ::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		// box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2) !important;
		// border-radius: 10px !important;
		background: #FFFFFF !important;
	}

	.popup_tkall{
		// height: 1100rpx;
		
		.popup_tk{
			font-size: 42rpx;font-weight: 800;margin: 12rpx 0 24rpx 0;
			text-align: center;
		}
		.popup-content{
			height: 700rpx;
			padding-bottom: 130rpx;
			// overflow-y: auto;
		}
		.popup-content-item{
			width: 690rpx;
			padding: 30rpx 0;
			box-sizing: border-box;
			// background: #F7F7F7;
			// border-radius: 18rpx;
			// border: 5rpx solid #EAEAEA;
			border-bottom:2rpx solid #EEEEEE ;
			padding-bottom: 30rpx;
			line-height: 50rpx;
			font-family: PingFang SC, PingFang SC;
			justify-content: space-between;
			color: #3D3D3D;
			
			.popup-content-item-title{
				font-weight: 500;
				font-size: 28rpx;
			}
		}

	}
</style>