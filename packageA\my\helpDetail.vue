<template>
	<view class="box flex justify-start align-items flex-column">
		<view class="con-center flex flex-column justify-start align-items">
			<span class="title">{{detail.title}}</span>
			<!-- <span class="line-row"></span> -->
			<span class="text">
				<u-parse :content="detail.content" :tagStyle="style"></u-parse>
			</span>
		</view>
		<view class="service flex justify-center align-items" style="padding-bottom: 0;"
			@click="callPhone(init.mobile)">
			<image src="../../static/my/server.png" mode="" style="width: 64rpx;height: 64rpx;"></image>
			<span class="flex justify-center align-items">联系客服:{{init.mobile}}</span>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				style: {
					// 字符串的形式
					img: 'width: 100%',
				},
				detail: {},
				init:{}
			};
		},
		onLoad(option) {
			this.getInit(option.id)
			this.init = uni.getStorageSync('init')
		},
		methods: {
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},
			getInit(id) {
				uni.$u.http.get('/api/school.help/detail', {
					params: {
						id: id,
					}

				}).then(res => {
					if (res.code == 1) {
						this.detail = res.data.detail
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				}).catch(error => {
					uni.showToast({
						title: '请求失败，请稍后再试',
						icon: 'none',
						duration: 2000
					});
				});
			}
		}
	}
</script>

<style lang="scss" lang="less">
	.w-100 {
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.flex-start {
		align-items: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.align-items {
		align-items: center;
	}

	.flex-column {
		flex-flow: column;
	}

	.justify-start {
		justify-content: start;
	}

	.line-row {
		margin: 33rpx 0 23rpx 0;
		width: 690rpx;
		height: 2rpx;
		background: #F1F2F8;
		border-radius: 0rpx 0rpx 0rpx 0rpx;
	}

	.box {
		min-height: 100vh;
		background: #FFFFFF;
	}

	.con-center {
		width: 690rpx;
		height: 90vh;
		overflow-y: auto;
	}

	.title {
		width: 690rpx;
		margin-top: 47rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 32rpx;
		color: #7A7A7A;
		border-bottom: 1rpx solid #F1F2F8;
		padding-bottom: 23rpx;
	}

	.text {
		width: 690rpx;
		margin-bottom: 30rpx;
		margin-top: 23rpx;
	}

	.service {
		margin-top: 24rpx;
		width: 690rpx;
		height: 94rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
		font-family: PingFang SC, PingFang SC;
		font-weight: 500;
		font-size: 30rpx;
		color: #008CFF;
	}
</style>